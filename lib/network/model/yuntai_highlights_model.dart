///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class YuntaiHighlightsModelItems {
/*
{
  "cover": "https://shootz-cos-1308047407.cos.ap-guangzhou.myqcloud.com/mobile/videos/539795/1636/mergeCoverImg/1757990701015.jpg",
  "id": "89",
  "status": 1,
  "name": "1",
  "createTime": "1",
  "path": "https://shootz-cos-1308047407.cos.ap-guangzhou.myqcloud.com/mobile/videos/539795/1636/mergevideo/1757990699938.mp4"
} 
*/

  String? cover;
  String? id;
  int? status;
  String? name;
  String? createTime;
  String? path;

  YuntaiHighlightsModelItems({
    this.cover,
    this.id,
    this.status,
    this.name,
    this.createTime,
    this.path,
  });
  YuntaiHighlightsModelItems.fromJson(Map<String, dynamic> json) {
    cover = json['cover']?.toString();
    id = json['id']?.toString();
    status = json['status']?.toInt();
    name = json['name']?.toString();
    createTime = json['createTime']?.toString();
    path = json['path']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['cover'] = cover;
    data['id'] = id;
    data['status'] = status;
    data['name'] = name;
    data['createTime'] = createTime;
    data['path'] = path;
    return data;
  }
}

class YuntaiHighlightsModel {
/*
{
  "venueId": "0",
  "venueName": "即刻创作",
  "time": "2025-09-17 00:00:00",
  "items": [
    {
      "cover": "https://shootz-cos-1308047407.cos.ap-guangzhou.myqcloud.com/mobile/videos/539795/1636/mergeCoverImg/1757990701015.jpg",
      "id": "89",
      "status": 1,
      "name": "1",
      "createTime": "1",
      "path": "https://shootz-cos-1308047407.cos.ap-guangzhou.myqcloud.com/mobile/videos/539795/1636/mergevideo/1757990699938.mp4"
    }
  ]
} 
*/

  String? venueId;
  String? venueName;
  String? time;
  List<YuntaiHighlightsModelItems?>? items;

  YuntaiHighlightsModel({
    this.venueId,
    this.venueName,
    this.time,
    this.items,
  });
  YuntaiHighlightsModel.fromJson(Map<String, dynamic> json) {
    venueId = json['venueId']?.toString();
    venueName = json['venueName']?.toString();
    time = json['time']?.toString();
    if (json['items'] != null) {
      final v = json['items'];
      final arr0 = <YuntaiHighlightsModelItems>[];
      v.forEach((v) {
        arr0.add(YuntaiHighlightsModelItems.fromJson(v));
      });
      items = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['venueId'] = venueId;
    data['venueName'] = venueName;
    data['time'] = time;
    if (items != null) {
      final v = items;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['items'] = arr0;
    }
    return data;
  }
}
