///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TabYuntaiCreationItemModelVideoListFragmentList {
/*
{
  "shotTime": 5,
  "isGoal": false
} 
*/

  int? shotTime;
  bool? isGoal;

  TabYuntaiCreationItemModelVideoListFragmentList({
    this.shotTime,
    this.isGoal,
  });
  TabYuntaiCreationItemModelVideoListFragmentList.fromJson(
      Map<String, dynamic> json) {
    shotTime = json['shotTime']?.toInt();
    isGoal = json['isGoal'];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['shotTime'] = shotTime;
    data['isGoal'] = isGoal;
    return data;
  }
}

class TabYuntaiCreationItemModelVideoList {
/*
{
  "id": 1,
  "videoLocalUrl": "android/user",
  "videoUrl": "",
  "duration": 200,
  "coverUrl": "",
  "fragmentList": [
    {
      "shotTime": 5,
      "isGoal": false
    }
  ]
} 
*/

  int? id;
  String? videoLocalUrl;
  String? videoUrl;
  int? duration;
  String? coverUrl;
  List<TabYuntaiCreationItemModelVideoListFragmentList?>? fragmentList;

  TabYuntaiCreationItemModelVideoList({
    this.id,
    this.videoLocalUrl,
    this.videoUrl,
    this.duration,
    this.coverUrl,
    this.fragmentList,
  });
  TabYuntaiCreationItemModelVideoList.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toInt();
    videoLocalUrl = json['videoLocalUrl']?.toString();
    videoUrl = json['videoUrl']?.toString();
    duration = json['duration']?.toInt();
    coverUrl = json['coverUrl']?.toString();
    if (json['fragmentList'] != null) {
      final v = json['fragmentList'];
      final arr0 = <TabYuntaiCreationItemModelVideoListFragmentList>[];
      v.forEach((v) {
        arr0.add(TabYuntaiCreationItemModelVideoListFragmentList.fromJson(v));
      });
      fragmentList = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['videoLocalUrl'] = videoLocalUrl;
    data['videoUrl'] = videoUrl;
    data['duration'] = duration;
    data['coverUrl'] = coverUrl;
    if (fragmentList != null) {
      final v = fragmentList;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['fragmentList'] = arr0;
    }
    return data;
  }
}

class TabYuntaiCreationItemModel {
/*
{
  "trainingId": 1556,
  "venueId": 1,
  "coverUrl": "",
  "venueName": "长沙岳麓区军民融合产业园的球场",
  "day": "2025-09-18",
  "week": "星期四",
  "status": 2,
  "duration": 145,
  "shootType": 1,
  "sliceNum": 0,
  "fragmentNum": 3,
  "createdTime": "2025-09-18T11:42:09+08:00",
  "videoList": [
    {
      "id": 1,
      "videoLocalUrl": "android/user",
      "videoUrl": "",
      "duration": 200,
      "coverUrl": "",
      "fragmentList": [
        {
          "shotTime": 5,
          "isGoal": false
        }
      ]
    }
  ]
} 
*/

  int? trainingId;
  int? venueId;
  String? coverUrl;
  String? venueName;
  String? day;
  String? week;
  int? status;
  int? duration;
  int? shootType;
  int? sliceNum;
  int? fragmentNum;
  String? createdTime;
  List<TabYuntaiCreationItemModelVideoList?>? videoList;

  TabYuntaiCreationItemModel({
    this.trainingId,
    this.venueId,
    this.coverUrl,
    this.venueName,
    this.day,
    this.week,
    this.status,
    this.duration,
    this.shootType,
    this.sliceNum,
    this.fragmentNum,
    this.createdTime,
    this.videoList,
  });
  TabYuntaiCreationItemModel.fromJson(Map<String, dynamic> json) {
    trainingId = json['trainingId']?.toInt();
    venueId = json['venueId']?.toInt();
    coverUrl = json['coverUrl']?.toString();
    venueName = json['venueName']?.toString();
    day = json['day']?.toString();
    week = json['week']?.toString();
    status = json['status']?.toInt();
    duration = json['duration']?.toInt();
    shootType = json['shootType']?.toInt();
    sliceNum = json['sliceNum']?.toInt();
    fragmentNum = json['fragmentNum']?.toInt();
    createdTime = json['createdTime']?.toString();
    if (json['videoList'] != null) {
      final v = json['videoList'];
      final arr0 = <TabYuntaiCreationItemModelVideoList>[];
      v.forEach((v) {
        arr0.add(TabYuntaiCreationItemModelVideoList.fromJson(v));
      });
      videoList = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['trainingId'] = trainingId;
    data['venueId'] = venueId;
    data['coverUrl'] = coverUrl;
    data['venueName'] = venueName;
    data['day'] = day;
    data['week'] = week;
    data['status'] = status;
    data['duration'] = duration;
    data['shootType'] = shootType;
    data['sliceNum'] = sliceNum;
    data['fragmentNum'] = fragmentNum;
    data['createdTime'] = createdTime;
    if (videoList != null) {
      final v = videoList;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['videoList'] = arr0;
    }
    return data;
  }
}
