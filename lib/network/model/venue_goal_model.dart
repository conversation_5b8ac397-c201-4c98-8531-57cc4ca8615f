import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'dart:io';

class VenueGoalModel {
  final String id;
  final String videoTime;
  final RxBool isCheck;
  final String videoPath;
  String localFilePath;
  final String coverPath;
  final String eventId;
  final String trainingId;
  final int typeIndex;
  final String shotTime;

  // 新增下载状态字段
  RxDouble downloadProgress = 0.0.obs; // 下载进度 0.0~1.0
  RxBool isDownloading = false.obs; // 是否正在下载
  RxString downloadStatus = 'pending'.obs; // 下载状态

  VenueGoalModel({
    required this.id,
    required this.videoTime,
    required bool isCheck,
    required this.videoPath,
    required this.localFilePath,
    required this.coverPath,
    required this.eventId,
    required this.trainingId,
    required this.typeIndex,
    required this.shotTime,
  }) : isCheck = isCheck.obs;

  factory VenueGoalModel.fromJson(Map<String, dynamic> json) {
    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(
        int.parse(json['shotTime'] == "" ? "0" : json['shotTime'] ?? '0'));
    return VenueGoalModel(
      id: json['id']?.toString() ?? '',
      videoTime: json['videoTime']?.toString() ?? '',
      isCheck: json['isCheck'] ?? false,
      videoPath: json['videoPath']?.toString() ?? '',
      coverPath: json['coverPath']?.toString() ?? '',
      eventId: json['eventId']?.toString() ?? '',
      trainingId: json['trainingId']?.toString() ?? '',
      typeIndex: json['typeIndex']?.toInt() ?? 0,
      localFilePath: json['localFilePath']?.toString() ?? '',
      shotTime: DateFormat("HH:mm:ss").format(dateTime),
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'videoTime': videoTime,
        'isCheck': isCheck.value,
        'videoPath': videoPath,
        'coverPath': coverPath,
        'eventId': eventId,
        'trainingId': trainingId,
        'typeIndex': typeIndex,
        'localFilePath': localFilePath,
      };

  VenueGoalModel copyWith({
    String? id,
    String? videoTime,
    bool? isCheck,
    String? videoPath,
    String? coverPath,
    String? eventId,
    String? trainingId,
    String? localFilePath,
    int? typeIndex,
    double? downloadProgress,
    bool? isDownloading,
    String? downloadStatus,
  }) {
    final newModel = VenueGoalModel(
      id: id ?? this.id,
      videoTime: videoTime ?? this.videoTime,
      isCheck: isCheck ?? this.isCheck.value,
      videoPath: videoPath ?? this.videoPath,
      coverPath: coverPath ?? this.coverPath,
      eventId: eventId ?? this.eventId,
      trainingId: trainingId ?? this.trainingId,
      typeIndex: typeIndex ?? this.typeIndex,
      localFilePath: localFilePath ?? this.localFilePath,
      shotTime: shotTime,
    );

    // 同步状态字段
    newModel.downloadProgress.value =
        downloadProgress ?? this.downloadProgress.value;
    newModel.isDownloading.value = isDownloading ?? this.isDownloading.value;
    newModel.downloadStatus.value = downloadStatus ?? this.downloadStatus.value;

    return newModel;
  }

  /// 生成平台相关文件名
  String get generatedFileName {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return Platform.isAndroid
        ? 'extended_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.mp4'
        : 'download_$timestamp.mp4';
  }

  /// 重置下载状态
  void resetDownloadStatus() {
    downloadProgress.value = 0;
    isDownloading.value = false;
    downloadStatus.value = 'pending';
  }
}
