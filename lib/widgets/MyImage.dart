import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:math' as math;

class MyImage extends StatelessWidget {
  final String imageUrl;
  final BoxFit fit;

  final Widget? child;
  final Color borderColor;

  final bool isAssetImage;
  final bool isOval;
  final bool hasBorder;
  final double borderWidth;
  final double radius;
  final double? height;
  final double? width;
  final Color? imageColor;
  final Color? bgColor;
  final bool loadProgress;
  final String? errorImage;
  final String? placeholderImage;
  final double placeholderSize;

  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;

  final GestureTapCallback? onTap;
  final GestureTapCallback? onLongPress;
  final GestureTapCallback? onDoubleTap;

  final int? memCacheWidth;
  final int? memCacheHeight;

  const MyImage(
    this.imageUrl, {
    super.key,
    this.fit = BoxFit.cover,
    this.bgColor,
    this.child,
    this.width,
    this.height,
    this.isAssetImage = false,
    this.isOval = false,
    this.hasBorder = false,
    this.borderColor = Colors.transparent,
    this.borderWidth = 0.5,
    this.radius = 0,
    this.onTap,
    this.onLongPress,
    this.onDoubleTap,
    this.padding,
    this.margin,
    this.loadProgress = false,
    this.errorImage = "error_image.png",
    this.placeholderImage = "error_image.png",
    this.placeholderSize = 24,
    this.imageColor,
    this.memCacheWidth,
    this.memCacheHeight,
  });

  // 获取资源文件路径
  String _getAssetPath(String? assetName) {
    if (assetName == null) return '';
    return 'assets/images/$assetName';
  }

  // 构建占位图
  Widget _buildPlaceholder({bool withProgress = false, double? progress}) {
    if (placeholderImage != null) {
      return Image.asset(
        _getAssetPath(placeholderImage),
        fit: BoxFit.cover,
        width: width,
        height: height,
      );
    }

    return Container(
      color: bgColor ?? Colors.grey[200],
      child: Center(
        child: withProgress && progress != null
            ? CircularProgressIndicator(
                value: progress,
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation(Colors.grey[400]!),
              )
            : SizedBox(
                width: placeholderSize,
                height: placeholderSize,
                child: Icon(Icons.image,
                    size: placeholderSize, color: Colors.grey),
              ),
      ),
    );
  }

  // 构建错误图
  Widget _buildErrorWidget() {
    if (errorImage != null) {
      return Image.asset(
        _getAssetPath(errorImage),
        fit: BoxFit.cover,
        width: width,
        height: height,
      );
    }

    return Container(
      color: Colors.grey[200],
      child: Center(
        child:
            Icon(Icons.broken_image, size: placeholderSize, color: Colors.grey),
      ),
    );
  }

  // 为iOS设备优化的内存计算
  ({int? cacheWidth, int? cacheHeight}) _iosMemoryOptimizedCacheSize() {
    final devicePixelRatio =
        PlatformDispatcher.instance.views.first.devicePixelRatio;

    // 如果没有指定尺寸，使用默认限制防止超大图片
    if (width == null && height == null) {
      return (
        cacheWidth: memCacheWidth ?? 800,
        cacheHeight: memCacheHeight ?? 800
      );
    }

    // iOS内存优化策略：
    // 1. 最大不超过屏幕物理像素
    // final screenSize =
    //     MediaQueryData.fromView(PlatformDispatcher.instance.views.first).size;

    // final maxScreenWidth = screenSize.width * devicePixelRatio;
    // final maxScreenHeight = screenSize.height * devicePixelRatio;

    // 2. 计算基于显示尺寸的缓存尺寸
    int? calculateDimension(double? logicalSize) {
      // 检查是否为 null、无穷大或 NaN
      if (logicalSize == null || logicalSize.isInfinite || logicalSize.isNaN) {
        return null;
      }
      final physicalSize = logicalSize * devicePixelRatio;
      return physicalSize.round();
    }

    int? cacheWidth = calculateDimension(width);
    int? cacheHeight = calculateDimension(height);

    // 3. 应用用户自定义覆盖
    cacheWidth = memCacheWidth ?? cacheWidth;
    cacheHeight = memCacheHeight ?? cacheHeight;

    // 4. 对超大图片施加额外限制（防止10000px+的图片导致OOM）
    const maxDimension = 2048; // iOS建议的最大纹理尺寸
    if (cacheWidth != null && cacheWidth > maxDimension) {
      cacheWidth = maxDimension;
    }
    if (cacheHeight != null && cacheHeight > maxDimension) {
      cacheHeight = maxDimension;
    }

    return (cacheWidth: cacheWidth, cacheHeight: cacheHeight);
  }

  // 构建Asset图片
  Widget _buildAssetImage() {
    return Image.asset(
      _getAssetPath(imageUrl),
      fit: fit,
      width: width,
      height: height,
      color: imageColor,
    );
  }

  // iOS优化的网络图片加载
  Widget _buildIOSOptimizedNetworkImage() {
    final cacheSize = _iosMemoryOptimizedCacheSize();
    final maxDimension =
        cacheSize.cacheWidth != null && cacheSize.cacheHeight != null
            ? math.max(cacheSize.cacheWidth!, cacheSize.cacheHeight!)
            : null;

    // 关键内存优化技术：
    // 1. 限制缓存尺寸，避免加载超分辨率图片
    // 2. 使用低分辨率placeholder减少内存峰值
    // 3. 磁盘和内存缓存分离
    return CachedNetworkImage(
      imageUrl: imageUrl,
      memCacheWidth: cacheSize.cacheWidth,
      memCacheHeight: cacheSize.cacheHeight,
      maxWidthDiskCache: maxDimension,
      maxHeightDiskCache: maxDimension,
      fit: fit,
      width: width,
      height: height,
      placeholder: (context, url) => _buildPlaceholder(),
      // progressIndicatorBuilder: (context, url, progress) => _buildPlaceholder(
      //   withProgress: loadProgress,
      //   progress: progress.progress,
      // ),
      errorWidget: (context, url, error) => _buildErrorWidget(),
      cacheKey: '${imageUrl}_${cacheSize.cacheWidth}x${cacheSize.cacheHeight}',

      // iOS内存优化关键参数
      imageBuilder: (context, imageProvider) {
        return Image(
          image: imageProvider,
          fit: fit,
          width: width,
          height: height,
          // 防止iOS上过度绘制
          frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
            if (wasSynchronouslyLoaded) return child;
            return AnimatedOpacity(
              child: child,
              opacity: frame == null ? 0 : 1,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget imageWidget;

    if (isAssetImage) {
      imageWidget = _buildAssetImage();
    } else {
      // 为iOS设备特别优化
      imageWidget = _buildIOSOptimizedNetworkImage();

      // 内存优化：封装在内存受限容器中
      imageWidget = SizedBox(
        width: width,
        height: height,
        child: imageWidget,
      );
    }

    // 添加内边距
    if (padding != null) {
      imageWidget = Padding(padding: padding!, child: imageWidget);
    }

    // iOS圆角优化：避免过度绘制
    if (isOval) {
      imageWidget = ClipOval(child: imageWidget);
    } else if (radius > 0) {
      imageWidget = PhysicalModel(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(radius),
        clipBehavior: Clip.antiAlias,
        child: imageWidget,
      );
    }

    // 添加子组件
    if (child != null) {
      imageWidget = Stack(
        clipBehavior: Clip.none,
        children: [imageWidget, child!],
      );
    }

    // 添加点击事件
    if (onTap != null || onDoubleTap != null || onLongPress != null) {
      imageWidget = InkWell(
        onTap: onTap,
        onDoubleTap: onDoubleTap,
        onLongPress: onLongPress,
        borderRadius: BorderRadius.circular(radius),
        child: imageWidget,
      );
    }

    // 容器装饰 - iOS优化：避免不必要的装饰层
    return Container(
      width: width,
      height: height,
      margin: margin,
      decoration: BoxDecoration(
        color: bgColor,
        border: hasBorder
            ? Border.all(color: borderColor, width: borderWidth)
            : null,
        borderRadius: isOval ? null : BorderRadius.circular(radius),
        shape: isOval ? BoxShape.circle : BoxShape.rectangle,
      ),
      child: imageWidget,
    );
  }
}
