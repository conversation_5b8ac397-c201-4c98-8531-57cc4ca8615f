import 'dart:developer';
import 'dart:io';
import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_common/wx_logger.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:video_player/video_player.dart';

class VideoController {
  final chewieController = Rx<ChewieController?>(null);
  VideoPlayerController? videoPlayerController;
  final bool autoPlay;
  final bool looping;
  final RxString networkUrl;
  final bool isNetwork; //
  String? videoCover;
  String? highlightVideoId;
  bool watermark;
  bool pushDisposeOnAndroid; //安卓设备上push到新页面时是否销毁播放器

  var fromFullScreen = false; //用于didpop判断是否从全屏页返回

  bool get toFullScreen {
    //用于didPushNext判断是否是去全屏页
    return chewieController.value?.isFullScreen ?? false;
  }

  String? get dataSource {
    return chewieController.value?.videoPlayerController.dataSource;
  }

  VideoController(
      {String videoPath = '',
      this.autoPlay = true,
      this.looping = true,
      this.videoCover,
      this.isNetwork = true,
      this.watermark = false,
      this.pushDisposeOnAndroid = false})
      : networkUrl = RxString(videoPath);

  void setData(
      {required String videoPath,
      String? videoCover,
      bool? showWatermark,
      String? highlightVideoId}) {
    networkUrl.value = videoPath;
    this.videoCover = videoCover;
    this.highlightVideoId = highlightVideoId;
    if (showWatermark != null) {
      watermark = showWatermark;
    }
    dispose();
  }

  void showWatermark(bool show) async {
    if (networkUrl.isEmpty) {
      watermark = show;
      return;
    }
    if (show != watermark) {
      watermark = show;
      final position = videoPlayerController?.value.position;
      if (_isInitializing) {
        //reinitialize方法正在初始化initVideo
        return;
      }
      dispose();
      // 添加标记，防止 VideoView 重复初始化
      _isInitializing = true;
      try {
        await initVideo(startAt: position);
      } finally {
        _isInitializing = false;
      }
    }
  }

  bool _isInitializing = false;
  bool get isInitializing => _isInitializing;
  Duration? position;
  void dispose({bool savePosition = false}) {
    debug('dispose---');
    if (savePosition) {
      position = videoPlayerController?.value.position;
      _isInitializing = true;
    }
    videoPlayerController?.pause();
    videoPlayerController?.dispose();
    chewieController.value?.dispose();
    chewieController.value = null;
    videoPlayerController = null;
  }

  void reinitialize() async {
    if (networkUrl.value.isNotEmpty) {
      await initVideo(startAt: position);
      _isInitializing = false;
    }
  }

  Future<void> initVideo({Duration? startAt}) async {
    if (isPause) {
      debug('not initVideo isPause');
      return;
    }
    try {
      final videoPath = networkUrl.value;
      if (videoPath.isEmpty) return;

      if (chewieController.value != null &&
          chewieController.value?.videoPlayerController.dataSource ==
              videoPath) {
        return;
      }
      debug(videoPath);
      dispose();
      if (isNetwork == true) {
        videoPlayerController =
            VideoPlayerController.networkUrl(Uri.parse(videoPath));
      } else {
        // 2. 假设视频文件名为"my_video.mp4"
        final videoFile =
            File(UserManager.instance.changeFilePathInIOS(videoPath));
        // 3. 检查文件是否存在
        if (await videoFile.exists()) {
          videoPlayerController = VideoPlayerController.file(videoFile);
        } else {
          WxLoading.showToast("视频文件不存在");
          return;
        }
      }

      await videoPlayerController?.initialize();
      debug('videoPlayerController?.initialize()');
      final chewieControllerValue = ChewieController(
        videoPlayerController: videoPlayerController!,
        autoPlay: !isPause,
        optionsTranslation: OptionsTranslation(
            cancelButtonText: "取消",
            subtitlesButtonText: "确定",
            playbackSpeedButtonText: "播放速度"),
        looping: true,
        startAt: startAt, // 动态设置开始位置
        aspectRatio: _calculateCorrectAspectRatio(),
        //16 / 9 ?? videoPlayerController!.value.aspectRatio,
        allowedScreenSleep: false,
        showControlsOnInitialize: false,
        overlay: watermark
            ? WxAssets.images.watermark.image(
                width: double.infinity,
                height: double.infinity,
                fit: BoxFit.fill,
              )
            : null,
        errorBuilder: (context, errorMessage) {
          return Center(
            child: Text(
              errorMessage,
              style: const TextStyle(color: Colors.white),
            ),
          );
        },
      );
      log('videoPlayerController2=${videoPlayerController!.value.aspectRatio}');
      if (isPause) {
        debug('videoPlayerController?.initialize() isPause');
        videoPlayerController?.pause();
      }
      chewieController.value = chewieControllerValue;
    } catch (e) {
      debug('Video initialization error: $e');
      dispose();
    }
  }

  double _calculateCorrectAspectRatio() {
    final videoWidth = videoPlayerController!.value.size.width;
    final videoHeight = videoPlayerController!.value.size.height;

    // 如果视频是横屏（宽>高），直接返回原始比例
    if (videoWidth > videoHeight) return videoWidth / videoHeight;

    // 如果是竖屏视频（可能实际是横屏旋转），交换宽高
    return videoHeight / videoWidth;
  }

  var isPause = false;
  void pause() {
    isPause = true;
    videoPlayerController?.pause();
  }

  void play() {
    isPause = false;
    videoPlayerController?.play();
  }

  void retplay() {
    isPause = false;
    // videoPlayerController?.();
  }
}
