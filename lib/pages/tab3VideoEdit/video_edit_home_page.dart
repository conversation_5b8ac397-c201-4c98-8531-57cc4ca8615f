import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab3VideoEdit/video_edit_home_logic.dart';
import 'package:shoot_z/pages/tab3VideoEdit/yuntai_creation/yuntai_creation_page.dart';
import 'package:shoot_z/pages/tab3VideoEdit/yuntai_highlights/yuntai_highlights_page.dart';
import 'package:shoot_z/widgets/keep_alive_widget.dart';
import 'package:ui_packages/ui_packages.dart';

//剪辑一级页面
class VideoEditHomePage extends StatelessWidget {
  VideoEditHomePage({super.key});

  final logic = Get.put(VideoEditHomeLogic());

  @override
  Widget build(BuildContext context) {
    return KeepAliveWidget(
        child: Scaffold(
            backgroundColor: Colours.color0F0F16,
            body: _createDetailWidget(context)));
  }

  _createDetailWidget(BuildContext context) {
    return Column(
      children: [
        _topBarWidget(context),
        Expanded(
          child: Tab<PERSON><PERSON><PERSON>ie<PERSON>(
            controller: logic.tabController,
            children: [
              YuntaiCreationPage(),
              YuntaiHighlightsPage(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _topBarWidget(BuildContext context) {
    return Container(
      width: double.infinity,
      alignment: Alignment.center,
      height: 44.w,
      margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight, bottom: 25.w),
      child: TabBar(
        controller: logic.tabController,
        indicator: const UnderlineTabIndicator(
          borderSide: BorderSide.none,
          insets: EdgeInsets.zero,
        ),
        labelColor: Colors.transparent,
        unselectedLabelColor: Colors.transparent,
        labelStyle: TextStyles.titleSemiBold16,
        tabAlignment: TabAlignment.center,
        isScrollable: false,
        dividerColor: Colors.transparent,
        dividerHeight: 0,
        unselectedLabelStyle: TextStyles.regular.copyWith(fontSize: 14.sp),
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        tabs: [
          Tab(
            child: Column(
              // mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Obx(() => logic.currentTabIndex.value == 0
                    ? ShaderMask(
                        shaderCallback: (bounds) => const LinearGradient(
                          colors: [
                            Colours.colorFFF9DC,
                            Colours.colorE4C8FF,
                            Colours.colorE5F3FF,
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ).createShader(bounds),
                        child: Text(
                          logic.tabNameList[0],
                          style: TextStyles.titleSemiBold16.copyWith(
                            color: Colors.white,
                          ),
                        ),
                      )
                    : Text(
                        logic.tabNameList[0],
                        style: TextStyles.regular.copyWith(
                          fontSize: 14.sp,
                          color: Colours.color5C5C6E,
                        ),
                      )),
                SizedBox(
                  height: 6.w,
                ),
                Obx(() => logic.currentTabIndex.value == 0
                    ? WxAssets.images.imgCheckIn2.image()
                    : SizedBox(height: 10.w)),
              ],
            ),
          ),
          Tab(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Obx(() => logic.currentTabIndex.value == 1
                    ? ShaderMask(
                        shaderCallback: (bounds) => const LinearGradient(
                          colors: [
                            Colours.colorFFF9DC,
                            Colours.colorE4C8FF,
                            Colours.colorE5F3FF,
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ).createShader(bounds),
                        child: Text(
                          logic.tabNameList[1],
                          style: TextStyles.titleSemiBold16.copyWith(
                            color: Colors.white,
                          ),
                        ),
                      )
                    : Text(
                        logic.tabNameList[1],
                        style: TextStyles.regular.copyWith(
                          fontSize: 14.sp,
                          color: Colours.color5C5C6E,
                        ),
                      )),
                SizedBox(
                  height: 6.w,
                ),
                Obx(() => logic.currentTabIndex.value == 1
                    ? WxAssets.images.imgCheckIn2.image()
                    : SizedBox(height: 10.w)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
