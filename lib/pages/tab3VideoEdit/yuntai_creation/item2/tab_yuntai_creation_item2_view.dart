import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/tab_yuntai_creation_item_model.dart';
import 'package:shoot_z/pages/tab3VideoEdit/yuntai_creation/item2/tab_yuntai_creation_item2_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/video/exclusive_dialog2.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///剪辑 一级页面-> 云台创作端  全部
// 在页面中绑定独立的 Controller（通过 itemType 区分）

class TabYuntaiCreationItem2Page extends StatelessWidget {
  TabYuntaiCreationItem2Page({super.key});
  final controller = Get.put(TabYuntaiCreationItem2Logic());
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return SmartRefresher(
        controller: controller.refreshController1,
        footer: buildFooter(),
        header: buildClassicHeader(),
        enablePullDown: true,
        enablePullUp: true,
        onRefresh: () {
          controller.getFeaturedList(isLoad: false);
        },
        onLoading: () {
          // WxLoading.showToast("onLoading");
          controller.getFeaturedList(isLoad: true);
        },
        physics: const AlwaysScrollableScrollPhysics(),
        //   child:
        child: (controller.dataFag["isFrist1"] as bool)
            ? buildLoad(isShowGif: false)
            : controller.dataList.isEmpty
                ? Center(
                    child: myNoDataView(
                      context,
                      msg: '暂无数据，快去创作吧',
                      //  imagewidget: WxAssets.images.battleEmptyIcon.image(),
                    ),
                  )
                : ListView.separated(
                    scrollDirection: Axis.vertical,
                    // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                    shrinkWrap: true,
                    itemCount: controller.dataList.length,
                    physics: const NeverScrollableScrollPhysics(),

                    padding: EdgeInsets.only(bottom: 35.w),
                    itemBuilder: (context, position) {
                      return _listItemWidget(
                          controller.dataList[position], position);
                    },
                    separatorBuilder: (BuildContext context, int index) {
                      return SizedBox(
                        height: 25.w,
                      );
                    },
                  ),
      );
    });
  }

  _listItemWidget(TabYuntaiCreationItemModel item, int index) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(left: 15.w, right: 15.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.createdTime ?? "",
                      style: TextStyles.regular.copyWith(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                          fontFamily: "DIN",
                          color: Colours.color5C5C6E,
                          height: 1),
                      maxLines: 1,
                    ),
                    SizedBox(
                      height: 13.w,
                    ),
                    Text(
                      item.venueName ?? "",
                      style: TextStyles.regular
                          .copyWith(fontSize: 12.sp, height: 1),
                      maxLines: 1,
                    ),
                  ],
                ),
              ),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  getMyDialog(
                    S.current.hint,
                    S.current.sure,
                    content: "确认下载该原视频至相册？", //该视频还未分析切片，若删除原视频将完全删除该拍摄记录
                    () {
                      Get.back();
                      controller.downloadToPhotos("");
                    },
                    isShowClose: false,
                    btnIsHorizontal: true,
                    btnText2: "取消",
                    onPressed2: () {
                      Get.back();
                    },
                  );
                },
                child: Container(
                    width: 44.w,
                    height: 40.w,
                    alignment: Alignment.centerRight,
                    child: WxAssets.images.downloadRound
                        .image(width: 24.w, height: 24.w)),
              ),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  //分析状态 (0: 待分析, 1: 分析中, 2: 已分析, 3: 分析取消)
                  getMyDialog(
                    "确认删除该原视频？",
                    S.current.sure,
                    content: item.status == 0 || item.status == 1
                        ? "该视频还未分析切片，若删除原视频将完全删除该拍摄记录"
                        : "该视频已经分析切片，若删除原视频将只保留切片视频", //该视频还未分析切片，若删除原视频将完全删除该拍摄记录
                    () {
                      Get.back();
                      controller.deleteVideo(item, index);
                    },
                    isShowClose: false,
                    btnIsHorizontal: true,
                    btnText2: S.current.cancel,
                    onPressed2: () {
                      Get.back();
                    },
                  );
                },
                child: Container(
                    width: 44.w,
                    height: 40.w,
                    alignment: Alignment.centerRight,
                    child: WxAssets.images.deleteRound
                        .image(width: 24.w, height: 24.w)),
              )
            ],
          ),
          SizedBox(
            height: 15.w,
          ),
          Container(
            height: 94.w,
            width: double.infinity,
            decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(8.r)),
            child: Row(
              children: [
                ((item.videoList?.length ?? 0) <= 0)
                    ? Container(
                        width: 165.w,
                        height: 94.w,
                        decoration: BoxDecoration(
                            color: Colours.color1E1E27,
                            borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(8.r),
                                bottomLeft: Radius.circular(8.r))),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            MyImage(
                              "", // featuredListModel.coverUrl ??
                              width: 48.w,
                              height: 30.w,
                              errorImage: "video_min_image.png",
                              placeholderImage: "video_min_image.png",
                            ),
                            SizedBox(
                              height: 11.w,
                            ),
                            Text(
                              "原视频已删除",
                              style: TextStyles.regular.copyWith(
                                  fontSize: 10.sp, color: Colours.color5C5C6E),
                            )
                          ],
                        ),
                      )
                    : GestureDetector(
                        onTap: () {
                          if ((item.videoList?.length ?? 0) > 1) {
                            showBottoVideosDialog(Get.context!, index);
                          } else {
                            //TODO播放本地视频
                            AppPage.to(Routes.videoPath, arguments: {
                              "videoPath": item.videoList?.first?.videoLocalUrl,
                              "teamName": item.venueName,
                              "isShowShareUpdate": "4",
                              "trainingId": item.trainingId.toString(),
                              "videoId": item.videoList?.first?.id.toString(),
                            });
                          }
                        },
                        child: SizedBox(
                          width: 165.w,
                          height: 94.w,
                          child: Stack(
                            children: [
                              MyImage(
                                "", // featuredListModel.coverUrl ??
                                width: 165.w,
                                height: 94.w,
                                radius: 8.r,
                                errorImage: "error_shot_video.png",
                                placeholderImage: "error_shot_video.png",
                              ),
                              Positioned(
                                  left: 0,
                                  right: 0,
                                  bottom: 0,
                                  top: 0,
                                  child: WxAssets.images.videoPlay
                                      .image(width: 28.w, height: 28.w)),
                              if (item.duration != 0)
                                Positioned(
                                  bottom: 5.w,
                                  right: 5.w,
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 6.w, vertical: 2.w),
                                    decoration: BoxDecoration(
                                      color: Colours.color000000,
                                      borderRadius: BorderRadius.circular(2.r),
                                    ),
                                    child: Text(
                                      Utils.formatDaysAndHms(
                                          item.duration ?? 0),
                                      style: TextStyles.medium.copyWith(
                                          fontSize: 10.sp,
                                          color: Colours.white),
                                    ),
                                  ),
                                ),
                              if ((item.videoList?.length ?? 0) > 1)
                                Positioned(
                                  left: 0.w,
                                  top: 0.w,
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 12.w, vertical: 5.w),
                                    decoration: BoxDecoration(
                                      color: Colours.color65000000,
                                      borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(8.r),
                                          bottomRight: Radius.circular(8.r)),
                                    ),
                                    child: Text(
                                      "${(item.videoList?.length ?? 0)}个文件",
                                      style: TextStyles.medium.copyWith(
                                          fontSize: 10.sp,
                                          color: Colours.white),
                                    ),
                                  ),
                                )
                            ],
                          ),
                        ),
                      ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 10.w, vertical: 5.w),
                          decoration: BoxDecoration(
                            color: Colours.color0F0F16,
                            borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(8.r),
                                topRight: Radius.circular(8.r)),
                            gradient: const LinearGradient(
                                colors: [
                                  Colours.colorFFECC1,
                                  Colours.colorE7CEFF,
                                  Colours.colorD1EAFF
                                ],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight),
                          ),
                          child: Text(
                            //	'拍摄类型 0:未知 1: 半场拍摄, 2: 全场拍摄, 3: 全场赛事
                            (item.shootType ?? 0) == 0
                                ? "其他"
                                : (item.shootType ?? 0) == 1
                                    ? "半场拍摄"
                                    : (item.shootType ?? 0) == 2
                                        ? "全场拍摄"
                                        : "全场赛事",
                            style: TextStyles.medium.copyWith(
                                fontSize: 10.sp, color: Colours.color191921),
                          )),
                      Center(
                        child: (item.status ?? 0) != 0
                            //分析状态 (0: 待分析, 1: 分析中, 2: 已分析, 3: 分析取消)
                            ? ShaderMask(
                                shaderCallback: (bounds) =>
                                    const LinearGradient(
                                  colors: [
                                    Colours.colorFFECC1,
                                    Colours.colorE7CEFF,
                                    Colours.colorD1EAFF,
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ).createShader(bounds),
                                child: Text(
                                  "${item.fragmentNum ?? 0}个片段",
                                  style: TextStyles.medium.copyWith(
                                      color: Colors.white, fontSize: 12.sp),
                                ),
                              )
                            : Text(
                                "待分析",
                                style: TextStyles.medium.copyWith(
                                  fontSize: 12.sp,
                                  color: Colours.cFF3F3F,
                                ),
                              ),
                      ),
                      const Spacer(),
                      (item.status ?? 0) == 0
                          ? GestureDetector(
                              behavior: HitTestBehavior.translucent,
                              onTap: () {
                                showAIDialog2();
                                controller.startAnimation();
                              },
                              child: Container(
                                width: double.infinity,
                                height: 32.w,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  image: DecorationImage(
                                      image: WxAssets.images.aiBtBg.provider()),
                                ),
                                child: ShaderMask(
                                  shaderCallback: (bounds) =>
                                      const LinearGradient(
                                    colors: [
                                      Colours.colorFFECC1,
                                      Colours.colorE7CEFF,
                                      Colours.colorD1EAFF,
                                    ],
                                    begin: Alignment.centerLeft,
                                    end: Alignment.centerRight,
                                  ).createShader(bounds),
                                  child: Text(
                                    "AI切片",
                                    style: TextStyles.medium.copyWith(
                                      fontSize: 12.sp,
                                      color: Colours.white,
                                    ),
                                  ),
                                ),
                              ),
                            )
                          : (item.status ?? 0) == 1 &&
                                  ((item.sliceNum ?? 0) >
                                      (item.fragmentNum ?? 0))
                              ? Container(
                                  width: double.infinity,
                                  height: 32.w,
                                  margin:
                                      EdgeInsets.only(left: 15.w, right: 14.w),
                                  child: Row(
                                    children: [
                                      GestureDetector(
                                        behavior: HitTestBehavior.translucent,
                                        onTap: () {
                                          //TODO 去查看
                                          AppPage.to(
                                              Routes.YuntaiCreationInfoPage,
                                              arguments: {
                                                "tabYuntaiCreationItemModel":
                                                    item,
                                              });
                                        },
                                        child: Container(
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                              border: Border.all(
                                                width: 1.w,
                                                color: Colours.white,
                                              ),
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(16.r))),
                                          height: 32.w,
                                          padding: EdgeInsets.symmetric(
                                            horizontal: 12.w,
                                          ),
                                          child: Text(
                                            "去查看",
                                            style: TextStyles.medium.copyWith(
                                              fontSize: 12.sp,
                                              color: Colours.white,
                                            ),
                                          ),
                                        ),
                                      ),
                                      const Spacer(),
                                      GestureDetector(
                                        behavior: HitTestBehavior.translucent,
                                        onTap: () {
                                          //TODO 继续上传
                                        },
                                        child: Container(
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                              gradient: const LinearGradient(
                                                colors: [
                                                  Colours.color7732ED,
                                                  Colours.colorA555EF,
                                                ],
                                                begin: Alignment.centerLeft,
                                                end: Alignment.centerRight,
                                              ),
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(16.r))),
                                          height: 32.w,
                                          padding: EdgeInsets.symmetric(
                                            horizontal: 12.w,
                                          ),
                                          child: Text(
                                            "继续上传",
                                            style: TextStyles.medium.copyWith(
                                              fontSize: 12.sp,
                                              color: Colours.white,
                                            ),
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                )
                              : GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () {
                                    AppPage.to(Routes.YuntaiCreationInfoPage,
                                        arguments: {
                                          "tabYuntaiCreationItemModel": item,
                                        });
                                  },
                                  child: Container(
                                    margin: EdgeInsets.only(
                                        left: 15.w, right: 15.w),
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                        border: Border.all(
                                          width: 1.w,
                                          color: Colours.white,
                                        ),
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(16.r))),
                                    height: 32.w,
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 12.w,
                                    ),
                                    child: Text(
                                      "去查看",
                                      style: TextStyles.medium.copyWith(
                                        fontSize: 12.sp,
                                        color: Colours.white,
                                      ),
                                    ),
                                  ),
                                ),
                      SizedBox(
                        height: 15.w,
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  //ai分析显示弹窗（自动避免重复）
  void showAIDialog2() {
    ExclusiveDialog2.show(
        context: Get.context!,
        barrierDismissible: false,
        builder: (_) => Padding(
              padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
              child: Material(
                type: MaterialType.transparency,
                color: Colors.transparent,
                child: Center(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        Container(
                          color: Colors.transparent,
                          child: Column(
                            children: <Widget>[
                              Container(
                                alignment: Alignment.topLeft,
                                height: 204.w,
                                decoration: BoxDecoration(
                                  color: Colours.color191921,
                                  borderRadius: BorderRadius.only(
                                    bottomLeft: Radius.circular(25.r),
                                    bottomRight: Radius.circular(25.r),
                                  ),
                                ),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 18, vertical: 2),
                                width: double.infinity,
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: <Widget>[
                                    const SizedBox(
                                      height: 30,
                                    ),
                                    Center(
                                      child: Text(
                                        ("提示"), //"1.修复了一些BUG;\n2.更新部分内容",//
                                        //  "1.修复了一些BUG;\n2.更新部分内容" * 20,
                                        style: TextStyles.regular.copyWith(
                                            color: Colours.white,
                                            fontSize: 18.sp),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    Obx(() {
                                      return LinearPercentIndicator(
                                        lineHeight: 4,
                                        linearGradient: const LinearGradient(
                                            colors: [
                                              Colours.color7732ED,
                                              Colours.colorA555EF
                                            ]),
                                        percent: controller.progress.value, //??
                                        //     controller.progressNotifier
                                        //         .value, //uploadController.progress, //
                                        backgroundColor: Colours.color000000,
                                        //    progressColor: Colours.colorA555EF, // 已完成的任务为绿色，否则根据进度设置颜色
                                        barRadius: const Radius.circular(3),
                                        animation: true,
                                        animateFromLastPercent: true,
                                        animationDuration: 500,
                                      );
                                    }),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    RichText(
                                      textAlign: TextAlign.left,
                                      text: TextSpan(
                                          text: "AI分析视频中，请稍等",
                                          style: TextStyle(
                                              color: Colours.colorA8A8BC,
                                              fontSize: 14.sp,
                                              height: 1,
                                              fontWeight: FontWeight.w400),
                                          children: <InlineSpan>[
                                            TextSpan(
                                                text: "",
                                                style: TextStyle(
                                                    color: Colours.white,
                                                    fontSize: 14.sp,
                                                    height: 1,
                                                    fontWeight:
                                                        FontWeight.w400)),
                                          ]),
                                    ),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    GestureDetector(
                                      behavior: HitTestBehavior.translucent,
                                      onTap: () {
                                        controller.pauseAnimation();
                                        getMyDialog(
                                          S.current.hint,
                                          "继续分析",
                                          content:
                                              "当前分析进度为${controller.progress.value * 1000 ~/ 10}%，取消后，已分析的结果将不会被保存。", //该视频还未分析切片，若删除原视频将完全删除该拍摄记录
                                          () {
                                            Get.back();
                                            controller.resumeAnimation();
                                          },
                                          isShowClose: false,
                                          btnIsHorizontal: true,
                                          btnText2: "确定取消",
                                          onPressed2: () {
                                            Get.back();
                                            controller.checkDialog2();
                                          },
                                        );
                                      },
                                      child: Container(
                                        width: double.infinity,
                                        height: 40.w,
                                        alignment: Alignment.center,
                                        margin: EdgeInsets.symmetric(
                                            horizontal: 20.w),
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(22.w),
                                            border: Border.all(
                                                width: 1.w,
                                                color: Colours.white)),
                                        child: Text(
                                          "取消分析",
                                          style: TextStyles.regular.copyWith(
                                              fontWeight: FontWeight.w600),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ));
  }

  showBottoVideosDialog(BuildContext context, int index) {
    var item = controller.dataList[index];
    return showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return Container(
              width: double.infinity,
              height: 682,
              padding: EdgeInsets.only(left: 15.w, right: 15.w),
              decoration: BoxDecoration(
                  color: Colours.color191921, //Colours.color191921
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(12.r),
                      topRight: Radius.circular(12.r))),
              child: Column(
                children: [
                  SizedBox(
                    height: 8.w,
                  ),
                  Container(
                    width: 38.w,
                    height: 4.w,
                    decoration: BoxDecoration(
                        color: Colours.colorD8D8D8,
                        borderRadius: BorderRadius.circular(4.r)),
                  ),
                  SizedBox(
                    height: 18.w,
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          item.venueName ?? "",
                          style: TextStyles.regular
                              .copyWith(fontSize: 12.sp, height: 1),
                          maxLines: 1,
                        ),
                      ),
                      SizedBox(
                        height: 13.w,
                      ),
                      Text(
                        item.createdTime ?? "",
                        style: TextStyles.regular.copyWith(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                            fontFamily: "DIN",
                            color: Colours.color5C5C6E,
                            height: 1),
                        maxLines: 1,
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 15.w,
                  ),
                  Expanded(
                    child: GridView.builder(
                      scrollDirection: Axis.vertical,
                      // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                      shrinkWrap: true,
                      physics:
                          const AlwaysScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        crossAxisSpacing: 15,
                        mainAxisSpacing: 15,
                        childAspectRatio: 166 / 95,
                      ),
                      padding: EdgeInsets.only(bottom: 30.w),
                      itemCount:
                          controller.dataList[index].videoList?.length ?? 0,
                      itemBuilder: (context, position) {
                        return GestureDetector(
                          onTap: () {
                            //TODO播放本地视频
                            AppPage.to(Routes.videoPath, arguments: {
                              "videoPath":
                                  item.videoList?[position]?.videoLocalUrl,
                              "teamName": item.venueName,
                              "isShowShareUpdate": "4",
                              "trainingId": item.trainingId.toString(),
                              "videoId":
                                  item.videoList?[position]?.id.toString(),
                            });
                          },
                          child: SizedBox(
                            width: 165.w,
                            height: 94.w,
                            child: Stack(
                              children: [
                                MyImage(
                                  "", // featuredListModel.coverUrl ??
                                  width: 165.w,
                                  height: 94.w,
                                  radius: 8.r,
                                  errorImage: "error_shot_video.png",
                                  placeholderImage: "error_shot_video.png",
                                ),
                                Positioned(
                                    left: 0,
                                    right: 0,
                                    bottom: 0,
                                    top: 0,
                                    child: WxAssets.images.videoPlay
                                        .image(width: 28.w, height: 28.w)),
                                if (item.duration != 0)
                                  Positioned(
                                    bottom: 5.w,
                                    right: 5.w,
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 6.w, vertical: 2.w),
                                      decoration: BoxDecoration(
                                        color: Colours.color000000,
                                        borderRadius:
                                            BorderRadius.circular(2.r),
                                      ),
                                      child: Text(
                                        Utils.formatDaysAndHms(
                                            item.duration ?? 0),
                                        style: TextStyles.medium.copyWith(
                                            fontSize: 10.sp,
                                            color: Colours.white),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ));
        });
      },
    );
  }
}
