import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/tab3VideoEdit/yuntai_creation/item1/tab_yuntai_creation_item1_view.dart';
import 'package:shoot_z/pages/tab3VideoEdit/yuntai_creation/item2/tab_yuntai_creation_item2_view.dart';
import 'package:shoot_z/pages/tab3VideoEdit/yuntai_creation/item3/tab_yuntai_creation_item3_view.dart';
import 'package:shoot_z/pages/tab3VideoEdit/yuntai_creation/yuntai_creation_logic.dart';
import 'package:ui_packages/ui_packages.dart';

/// 赛程列表页面
class YuntaiCreationPage extends StatelessWidget {
  YuntaiCreationPage({super.key});
  final logic = Get.put(YuntaiCreationLogic());
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _tabbarWidget(),
        Expanded(
            child: TabBarView(
          controller: logic.tabController,
          children: [
            TabYuntaiCreationItem1Page(), // 仅需传递不同type
            TabYuntaiCreationItem2Page(), // 仅需传递不同type
            TabYuntaiCreationItem3Page() // 仅需传递不同type
          ],
        ))
      ],
    );
  }

  Widget _tabbarWidget() {
    return Container(
        width: double.infinity,
        alignment: Alignment.center,
        padding: EdgeInsets.only(bottom: 10.w),
        child: TabBar(
            controller: logic.tabController,
            unselectedLabelColor: Colours.color5C5C6E,
            unselectedLabelStyle: TextStyle(
                fontSize: 18.sp,
                color: Colours.color5C5C6E,
                fontWeight: FontWeight.w600),
            labelColor: Colours.white,
            labelStyle: TextStyle(
                fontSize: 20.sp,
                color: Colours.white,
                fontWeight: FontWeight.w600),
            isScrollable: false,
            // labelPadding: EdgeInsets.only(left: 20.w, right: 20.w),
            indicatorPadding: EdgeInsets.zero,
            dividerColor: Colors.transparent,
            dividerHeight: 0,
            labelPadding: EdgeInsets.only(right: 20.w), // 调整标签间的间距
            indicatorSize: TabBarIndicatorSize.label,
            padding: EdgeInsets.zero,
            indicatorColor: Colors.transparent,
            physics: const NeverScrollableScrollPhysics(),
            overlayColor: WidgetStateProperty.all(Colors.transparent),
            tabAlignment: TabAlignment.center,
            tabs: List.generate(logic.tabList.length, (index) {
              Map<String, dynamic> tabData = logic.tabList[index];
              return Obx(() {
                return SizedBox(
                  width: 40.w,
                  height: 25.w,
                  child: Stack(
                    alignment: Alignment.bottomCenter,
                    children: [
                      if (logic.currentTabIndex.value == index)
                        Container(
                            width: 16.w,
                            height: 3.w,
                            margin: EdgeInsets.only(top: 4.w),
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [
                                  Colours.color7732ED,
                                  Colours.colorA555EF,
                                ],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                              ),
                              borderRadius: BorderRadius.circular(2.r),
                            )),
                      Positioned(
                          bottom: 7.w,
                          child: logic.currentTabIndex.value == index
                              ? ShaderMask(
                                  shaderCallback: (bounds) =>
                                      const LinearGradient(
                                    begin: Alignment.centerLeft,
                                    end: Alignment.centerRight,
                                    colors: [
                                      Color(0xFF7732ED),
                                      Color(0xFFA555EF),
                                    ],
                                  ).createShader(bounds),
                                  child: Text(
                                    tabData["title"],
                                    style: TextStyles.display14.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                )
                              : Text(
                                  tabData['title'],
                                  style: TextStyles.display12.copyWith(
                                    color: Colours.color5C5C6E,
                                  ),
                                )),
                    ],
                  ),
                );
              });
            })));
  }
}
