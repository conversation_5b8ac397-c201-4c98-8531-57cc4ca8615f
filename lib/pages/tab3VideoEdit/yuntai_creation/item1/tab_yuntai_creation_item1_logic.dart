import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/tab_yuntai_creation_item_model.dart';
import 'package:shoot_z/utils/FileUtils.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/video/exclusive_dialog2.dart';

class TabYuntaiCreationItem1Logic extends GetxController
    with GetSingleTickerProviderStateMixin {
  int itemType = 0; // 1, 2, 3 对应不同Tab
  RefreshController refreshController1 =
      RefreshController(initialRefresh: false);
  late AnimationController _controller;
  RxDouble progress = (0.0).obs;
  var dataFag = {
    "isFrist1": true,
    "page1": 1,
  }.obs;
  var dataList = <TabYuntaiCreationItemModel>[].obs;
  StreamSubscription? subscription;
  @override
  void onInit() {
    super.onInit();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 4),
    )..addListener(() async {
        progress.value = _controller.value;
        if (progress.value >= 1) {
          checkDialog2();
        }
      });
    subscription = BusUtils.instance.on((action) {
      if (action.key == EventBusKey.deleteLocalVideo3) {
        getFeaturedList();
      }
    });
    getFeaturedList();
  }

  //查询热点资讯视频
  getFeaturedList({
    isLoad = false,
  }) async {
    var page = dataFag["page1"] as int;
    if (!isLoad) {
      page = 1;
    }
    Map<String, dynamic> param = {
      'limit': 10,
      'page': page,
      'status': itemType == 0
          ? -1
          : itemType == 1
              ? 2
              : 0, //-1全部 0: 待分析, 1: 分析中, 2: 已分析
    };
    var res = await Api().get(ApiUrl.getPtzRecordList, queryParameters: param);
    if (res.isSuccessful()) {
      log("getPtzRecordList=param=$param \n${jsonEncode(res.data)}");
      List list = res.data["result"] ?? [];
      List<TabYuntaiCreationItemModel> modelList =
          list.map((e) => TabYuntaiCreationItemModel.fromJson(e)).toList();
      dataFag["page1"] = page + 1;
      if (isLoad) {
        dataList.addAll(modelList);
        dataList.refresh();
        if (modelList.length < 10) {
          refreshController1.loadNoData();
        } else {
          refreshController1.loadComplete();
        }
      } else {
        refreshController1.resetNoData();
        dataList.assignAll(modelList);
        refreshController1.refreshCompleted();
      }
      if (dataFag["isFrist1"] as bool) {
        dataFag["isFrist1"] = false;
        dataFag.refresh();
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }

//删除视频
  Future<void> deleteVideo(
    TabYuntaiCreationItemModel item,
    int index,
  ) async {
    if ((item.videoList?.length ?? 0) <= 0) {
      WxLoading.showToast("本地文件已删除");
      return;
    }
    var list = item.videoList?.map((e) {
      return e?.id;
    }).toList();
    //删除本地文件
    WxLoading.show();
    Map<String, dynamic> param = {
      'trainingId': item.trainingId,
      "videoIds": list,
    };
    var res = await Api().delete(ApiUrl.deletePtzRecordList, data: param);
    log("deletePtzRecordList=${param} ${res.data}");
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      WxLoading.showToast("删除成功");
      if (item.status == 0) {
        //分析状态 (0: 待分析, 1: 分析中, 2: 已分析, 3: 分析取消)
        dataList.remove(item);
      } else {
        getFeaturedList();
      }
      for (var item2 in item.videoList!) {
        FileUtils.deleteFile(item2?.videoLocalUrl ?? "", //list[i].filePath ??
            context: Get.context!,
            isShow: false);
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }

  void checkDialog2() {
    print("checkDialog2弹窗正在显示${ExclusiveDialog2.isShowing}");
    if (ExclusiveDialog2.isShowing) {
      print("checkDialog2弹窗正在显示");
      ExclusiveDialog2.forceClose(); // 强制关闭
    }
  }

  void startAnimation() {
    _controller.reset(); // 重置到0
    progress.value = 0; // 重置进度
    _controller.forward();
  }

  void pauseAnimation() {
    _controller.stop(); // 暂停动画
  }

  void resumeAnimation() {
    _controller.forward();
  }

  //把视频从沙盒下载到相册
  void downloadToPhotos(String path) {
    Utils.localDownloadAndSaveToSinglePhotoAlbum(path);
  }

  @override
  void onClose() {
    _controller.dispose();
    refreshController1.dispose(); // 必须手动释放
    // if (Get.isRegistered<TabYuntaiCreationItem1Logic>()) {
    //   Get.delete<TabYuntaiCreationItem1Logic>();
    // }
    subscription?.cancel();
    super.onClose();
  }
}
