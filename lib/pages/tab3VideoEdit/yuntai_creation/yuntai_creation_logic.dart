import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/model/competition_model.dart';

//剪辑->云台端创作
class YuntaiCreationLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  late TabController tabController;
  var currentTabIndex = 0.obs;
  var tabList = [
    {'title': '全部', 'id': '0'},
    {'title': '已分析', 'id': '1'},
    {'title': '待分析', 'id': '2'}
  ];

  //数据列表
  var dataList = <CompetitionModel>[].obs;
  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: tabList.length, vsync: this);
    tabController.addListener(() {
      // 只在tab切换完成时调用，避免重复调用
      if (!tabController.indexIsChanging) {
        currentTabIndex.value = tabController.index;
      }
    });
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }
}
