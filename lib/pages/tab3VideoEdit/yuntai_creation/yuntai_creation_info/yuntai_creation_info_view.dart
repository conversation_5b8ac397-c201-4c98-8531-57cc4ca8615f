import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/shot_record_model.dart';
import 'package:shoot_z/pages/tab3VideoEdit/yuntai_creation/yuntai_creation_info/yuntai_creation_info_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/utils/DateTimeUtils.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/video/video_view.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

//云台查看记录的视频
class YuntaiCreationInfoPage extends StatelessWidget {
  YuntaiCreationInfoPage({super.key});
  final logic = Get.put(YuntaiCreationInfoLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: MyAppBar(
          title: const Text("球场视频"),
          actions: [
            Obx(() {
              return GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  logic.checkAllVideo();
                },
                child: Container(
                  height: 40.w,
                  alignment: Alignment.centerRight,
                  margin: EdgeInsets.only(right: 15.w),
                  child: Row(
                    children: [
                      logic.allCheck.value
                          ? WxAssets.images.checkOn3
                              .image(width: 16.w, height: 16.w)
                          : WxAssets.images.checkOn3Wihte
                              .image(width: 16.w, height: 16.w),
                      SizedBox(
                        width: 6.w,
                      ),
                      Text(
                        logic.allCheck.value
                            ? S.current.Deselect_all
                            : S.current.select_all,
                        style: TextStyles.medium.copyWith(
                          fontSize: 13.sp,
                          color: logic.allCheck.value
                              ? Colours.color964AEE
                              : Colours.white,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }),
          ],
        ),
        body: Obx(() {
          return (logic.dataFag["isFrist"] as bool)
              ? buildLoad()
              : logic.dataList.isEmpty
                  ? Container(
                      margin: EdgeInsets.only(top: 15.w, bottom: 70.w),
                      child: myNoDataView(
                        context,
                        msg: "暂无视频",
                        textColor: Colours.color5C5C6E,
                        height: 10.w,
                        imagewidget: Container(
                          margin: EdgeInsets.only(left: 10.w),
                          child: WxAssets.images.noVideos.image(
                              width: 96.w, height: 60.w, fit: BoxFit.fill),
                        ),
                      ),
                    )
                  : (logic.isHorizontal.value == true)
                      ? SingleChildScrollView(
                          child: Container(
                            margin: EdgeInsets.all(15.w),
                            child: Column(
                              children: [
                                _titleWidget(),
                                //视频播放器
                                _videoHorizontalWidget(),
                                _functionWidget(),
                                _videoListHorizontalWidget(),
                              ],
                            ),
                          ),
                        )
                      : Container(
                          margin: EdgeInsets.all(15.w),
                          child: Column(
                            children: [
                              _titleWidget(),
                              _functionWidget(),
                              _videoVerticalWidget()
                            ],
                          ),
                        );
        }),
        bottomNavigationBar: Obx(() {
          return logic.dataList.isEmpty
              ? const SizedBox()
              //单人模式不管有没有选场地，都没有上传，多人对战只有选了场地才有上传
              : (logic.tabYuntaiCreationItemModel.value.venueId == 0 ||
                      logic.type.value == "1")
                  ? Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            GestureDetector(
                              behavior: HitTestBehavior.translucent,
                              onTap: () {
                                var list = logic.dataList.where((value) {
                                  return value.isCheck == "1";
                                }).toList();
                                if (list.length < 2) {
                                  WxLoading.showToast("请至少选择2个视频片段");
                                  return;
                                }
                                if (list.length > 20) {
                                  WxLoading.showToast("合成视频最多只能选择20个进球片段");
                                  return;
                                }
                                //去合成
                                showChooseGoalDialog(context, list);
                              },
                              child: Container(
                                height: 44.w,
                                margin: EdgeInsets.only(
                                    bottom: 25.w, top: 10.w, left: 15.w),
                                width: 345.w,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  color: Colours.color282735,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(28.r)),
                                  gradient: const LinearGradient(
                                    colors: [
                                      Colours.color7732ED,
                                      Colours.colorA555EF
                                    ],
                                    begin: Alignment.bottomLeft,
                                    end: Alignment.bottomRight,
                                  ),
                                ),
                                child: Text(
                                  "剪辑",
                                  style: TextStyles.semiBold14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    )
                  : Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        //该气泡对每个用户只首次进入展示一次
                        if (logic.showUploadTip.value)
                          Container(
                            height: 27.w,
                            padding: EdgeInsets.only(
                                top: 7.w, left: 10.w, right: 10.w),
                            decoration: BoxDecoration(
                                color: Colors.transparent,
                                image: DecorationImage(
                                    image: WxAssets.images.dialogTipsBg
                                        .provider())),
                            child: Text(
                              "与其他球友共享你的精彩记录",
                              style: TextStyles.regular.copyWith(
                                  fontSize: 12.sp, color: Colours.color191921),
                            ),
                          ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            GestureDetector(
                              behavior: HitTestBehavior.translucent,
                              onTap: () {
                                var list = logic.dataList.where((value) {
                                  return value.isCheck == "1";
                                }).toList();
                                if (list.isEmpty) {
                                  WxLoading.showToast("请至少选择1个视频片段");
                                } else {
                                  getMyDialog(
                                    S.current.dialog_title,
                                    S.current.sure,
                                    content: "确认上传选中视频至场地？",
                                    () {
                                      AppPage.back();
                                      logic.uploadVideo();
                                    },
                                    isShowClose: false,
                                    btnIsHorizontal: true,
                                    btnText2: S.current.cancel,
                                    onPressed2: () {
                                      AppPage.back();
                                    },
                                  );
                                }
                              },
                              child: Container(
                                height: 44.w,
                                margin: EdgeInsets.only(
                                    bottom: 25.w, top: 10.w, left: 15.w),
                                width: 165.w,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  color: Colours.color282735,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(28.r)),
                                  gradient: const LinearGradient(
                                    colors: [
                                      Colours.color7732ED,
                                      Colours.colorA555EF
                                    ],
                                    begin: Alignment.bottomLeft,
                                    end: Alignment.bottomRight,
                                  ),
                                ),
                                child: Text(
                                  "上传",
                                  style: TextStyles.semiBold14,
                                ),
                              ),
                            ),
                            GestureDetector(
                              behavior: HitTestBehavior.translucent,
                              onTap: () {
                                var list = logic.dataList.where((value) {
                                  return value.isCheck == "1";
                                }).toList();
                                if (list.length < 2) {
                                  WxLoading.showToast("请至少选择2个视频片段");
                                  return;
                                }
                                if (list.length > 20) {
                                  WxLoading.showToast("合成视频最多只能选择20个进球片段");
                                  return;
                                }
                                //去合成
                                showChooseGoalDialog(context, list);
                              },
                              child: Container(
                                height: 44.w,
                                margin: EdgeInsets.only(
                                    bottom: 25.w, top: 10.w, right: 15.w),
                                width: 165.w,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  color: Colours.color282735,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(28.r)),
                                  gradient: const LinearGradient(
                                    colors: [
                                      Colours.color7732ED,
                                      Colours.colorA555EF
                                    ],
                                    begin: Alignment.bottomLeft,
                                    end: Alignment.bottomRight,
                                  ),
                                ),
                                child: Text(
                                  "剪辑",
                                  style: TextStyles.semiBold14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    );
        }));
  }

  Expanded _videoVerticalWidget() {
    return Expanded(
      child: Row(
        children: [
          SizedBox(
            width: 270.w,
            height: 480.w,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8.r),
              child: AspectRatio(
                aspectRatio: 270 / 480, // 宽高比
                child: VideoView(
                  controller: logic.videoController,
                ),
              ),
            ),
          ),
          SizedBox(
            width: 15.w,
          ),
          Expanded(
            child: ListView.separated(
              scrollDirection: Axis.vertical,
              // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
              shrinkWrap: true,
              padding: EdgeInsets.only(
                bottom: 40.w,
              ),
              physics: const AlwaysScrollableScrollPhysics(),
              itemCount: logic.dataList.length,
              itemBuilder: (context, position) {
                return _listItemWidget(position);
              },
              separatorBuilder: (BuildContext context, int index) {
                return SizedBox(
                  height: 15.w,
                );
              },
            ),
          )
        ],
      ),
    );
  }

  Widget _titleWidget() {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Text(
                logic.tabYuntaiCreationItemModel.value.venueName ?? "",
                style: TextStyle(
                  color: Colours.colorA8A8BC,
                  height: 1,
                  fontSize: 12.sp,
                ),
              ),
            ),
            Text(
              logic.tabYuntaiCreationItemModel.value.createdTime ?? "",
              style: TextStyle(
                color: Colours.color5C5C6E,
                fontFamily: "DIN",
                height: 1,
                fontSize: 14.sp,
              ),
            ),
          ],
        ),
        SizedBox(
          height: 15.w,
        ),
      ],
    );
  }

  SizedBox _functionWidget() {
    return SizedBox(
      width: double.infinity,
      height: 54.w,
      child: Row(
        children: [
          RichText(
            textAlign: TextAlign.left,
            text: TextSpan(
                text: "投篮片段（${logic.dataList.length}）",
                style: TextStyle(
                    color: Colours.white,
                    fontSize: 14.sp,
                    height: 1,
                    fontWeight: FontWeight.w600),
                children: <InlineSpan>[
                  TextSpan(
                      text: "已选${logic.checkCount.value}个片段",
                      style: TextStyle(
                          color: Colours.white,
                          fontSize: 12.sp,
                          height: 1,
                          fontWeight: FontWeight.w400)),
                ]),
          ),
          const Spacer(),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              var list = logic.dataList.where((value) {
                return value.isCheck == "1";
              }).toList();
              if (list.isEmpty) {
                WxLoading.showToast("请至少选择1个视频片段");
              } else {
                getMyDialog(
                  S.current.dialog_title,
                  S.current.sure,
                  content: "确认下载选中视频至手机相册？",
                  () {
                    AppPage.back();
                    logic.loadVideo();
                  },
                  isShowClose: false,
                  btnIsHorizontal: true,
                  btnText2: S.current.cancel,
                  onPressed2: () {
                    AppPage.back();
                  },
                );
              }
            },
            child: Container(
                width: 44.w,
                height: 40.w,
                alignment: Alignment.centerRight,
                child: WxAssets.images.downloadRound
                    .image(width: 24.w, height: 24.w)),
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              //分析状态 (0: 待分析, 1: 分析中, 2: 已分析, 3: 分析取消)
              var list = logic.dataList.where((value) {
                return value.isCheck == "1";
              }).toList();
              if (list.isEmpty) {
                WxLoading.showToast("请至少选择1个视频片段");
              } else {
                getMyDialog(
                  "确认删除选中视频？",
                  S.current.sure,
                  content: "删除后报告中的视频也会被删除",
                  () {
                    AppPage.back();
                    logic.deleteVideo();
                  },
                  isShowClose: false,
                  btnIsHorizontal: true,
                  btnText2: S.current.cancel,
                  onPressed2: () {
                    AppPage.back();
                  },
                );
              }
            },
            child: Container(
                width: 44.w,
                height: 40.w,
                alignment: Alignment.centerRight,
                child: WxAssets.images.deleteRound
                    .image(width: 24.w, height: 24.w)),
          )
        ],
      ),
    );
  }

  Widget _videoListHorizontalWidget() {
    return GridView.builder(
        scrollDirection: Axis.vertical,
        // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
        shrinkWrap: true,
        physics:
            const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          crossAxisSpacing: 9.w,
          mainAxisSpacing: 9.w,
          childAspectRatio: 1,
        ),
        padding: EdgeInsets.only(bottom: 60.w, top: 0.w),
        itemCount: logic.dataList.length,
        itemBuilder: (context, index) {
          return Obx(() {
            return Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(8.r)),
                border: (logic.dataList[index].isCheck == "1")
                    ? Border.all(width: 2.w, color: Colours.white)
                    : null,
                image: DecorationImage(
                    image: WxAssets.images.errorShotVideo.provider(),
                    fit: BoxFit.cover),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      (logic.dataList[index].videoLoadOK == "1")
                          ? Container(
                              padding: EdgeInsets.only(
                                  left: 5.w, right: 5.w, top: 4.w, bottom: 4.w),
                              decoration: BoxDecoration(
                                  gradient: const LinearGradient(colors: [
                                    Colours.color7732ED,
                                    Colours.colorA555EF
                                  ]),
                                  borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(8.r),
                                      bottomRight: Radius.circular(8.r))),
                              child: Text(
                                logic.dataList[index].videoLoadOK == "1"
                                    ? "已传"
                                    : "",
                                style: TextStyles.regular.copyWith(
                                    fontSize: 10.sp, color: Colours.white),
                              ))
                          : SizedBox(),
                      GestureDetector(
                        onTap: () {
                          logic.dataList[index].isCheck =
                              logic.dataList[index].isCheck == "1" ? "0" : "1";
                          logic.dataList.refresh();
                        },
                        child: Container(
                            padding: EdgeInsets.only(
                                left: 20.w, right: 5.w, top: 5.w, bottom: 20.w),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(2.r)),
                            child: (logic.dataList[index].isCheck == "1")
                                ? WxAssets.images.checkOn3
                                    .image(width: 16.w, height: 16.w)
                                : WxAssets.images.checkOn3Wihte
                                    .image(width: 16.w, height: 16.w)),
                      ),
                    ],
                  ),
                  Container(
                    margin: EdgeInsets.only(bottom: 5.w),
                    padding: EdgeInsets.only(
                        left: 5.w, right: 5.w, top: 2.w, bottom: 2.w),
                    child: Text(
                      DateTimeUtils.formatTimeWithSeconds(
                          DateTime.fromMillisecondsSinceEpoch(
                              (logic.dataList[index].shootTime ?? 0.0)
                                  .toInt())),
                      textAlign: TextAlign.right,
                      style: TextStyles.medium
                          .copyWith(fontSize: 10.sp, color: Colours.white),
                    ),
                  ),
                ],
              ),
            );
          });
        });
  }

  Widget _videoHorizontalWidget() {
    return SizedBox(
      width: double.infinity,
      height: ScreenUtil().screenWidth / 375 * 194,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8.r),
        child: AspectRatio(
          aspectRatio: 375 / 194, // 宽高比
          child: VideoView(
            controller: logic.videoController,
          ),
        ),
      ),
    );
  }

  Widget _listItemWidget(int index) {
    return Obx(() {
      return Container(
        width: 60.w,
        height: 60.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(8.r)),
          border: (logic.dataList[index].isCheck == "1")
              ? Border.all(width: 2.w, color: Colours.white)
              : null,
          image: DecorationImage(
              image: WxAssets.images.errorShotVideo.provider(),
              fit: BoxFit.cover),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // MyImage(
            //   logic.dataList[index].playerImagePath ?? "",
            //   width: 120.w,
            //   height: 90.w,
            //   radius: 12.r,
            //   errorImage: "error_image_width.png",
            //   placeholderImage: "error_image_width.png",
            // ),
            // 显示本地图片
            // Image.file(
            //   File(
            //     logic.dataList[index].playerImagePath ?? "",
            //   ),
            //   width: 60.w,
            //   height: 60.w,
            //   fit: BoxFit.cover,
            // ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                (logic.dataList[index].videoLoadOK == "1")
                    ? Container(
                        padding: EdgeInsets.only(
                            left: 5.w, right: 5.w, top: 4.w, bottom: 4.w),
                        decoration: BoxDecoration(
                            gradient: const LinearGradient(colors: [
                              Colours.color7732ED,
                              Colours.colorA555EF
                            ]),
                            borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(8.r),
                                bottomRight: Radius.circular(8.r))),
                        child: Text(
                          logic.dataList[index].videoLoadOK == "1" ? "已传" : "",
                          style: TextStyles.regular
                              .copyWith(fontSize: 10.sp, color: Colours.white),
                        ))
                    : SizedBox(),
                GestureDetector(
                  onTap: () {
                    logic.dataList[index].isCheck =
                        logic.dataList[index].isCheck == "1" ? "0" : "1";
                    logic.dataList.refresh();
                  },
                  child: Container(
                      padding: EdgeInsets.only(
                          left: 15.w, right: 5.w, top: 5.w, bottom: 15.w),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(2.r)),
                      child: (logic.dataList[index].isCheck == "1")
                          ? WxAssets.images.checkOn3
                              .image(width: 16.w, height: 16.w)
                          : WxAssets.images.checkOn3Wihte
                              .image(width: 16.w, height: 16.w)),
                ),
              ],
            ),
            Container(
              padding:
                  EdgeInsets.only(left: 5.w, right: 5.w, top: 0.w, bottom: 5.w),
              child: Text(
                DateTimeUtils.formatTimeWithSeconds(
                    DateTime.fromMillisecondsSinceEpoch(
                        (logic.dataList[index].shootTime ?? 0.0).toInt())),
                textAlign: TextAlign.right,
                style: TextStyles.medium.copyWith(
                    fontSize: 10.sp, color: Colours.white, fontFamily: "DIN"),
              ),
            ),
          ],
        ),
      );
    });
  }

  void showChooseGoalDialog(BuildContext context, List<ShotRecordModel> list2) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return Container(
            width: double.infinity,
            color: Colours.color191921,
            padding: EdgeInsets.only(left: 20.w, right: 20.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Container(
                    width: 38.w,
                    height: 4,
                    margin: EdgeInsets.only(top: 8.w),
                    decoration: BoxDecoration(
                        color: Colours.color10D8D8D8,
                        borderRadius: BorderRadius.circular(4.r)),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 25.w, bottom: 15.w),
                  child: Text(
                    "视频效果与个性化",
                    style: TextStyles.medium
                        .copyWith(fontSize: 14.sp, color: Colours.white),
                  ),
                ),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    //单个视频片段时长
                    logic.compositeOption2[1] =
                        logic.compositeOption2[1] == "0" ? "1" : "0";
                  },
                  child: Container(
                    width: 105.w,
                    height: 40.w,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        color: Colours.color22222D,
                        borderRadius: BorderRadius.circular(22.r),
                        gradient: logic.compositeOption2[1] == "1"
                            ? const LinearGradient(colors: [
                                Colours.color7732ED,
                                Colours.colorA555EF
                              ])
                            : null,
                        border: logic.compositeOption2[1] != "1"
                            ? Border.all(width: 1, color: Colours.white)
                            : null),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          S.current.composite_video_dialog_tips5,
                          style: TextStyles.medium
                              .copyWith(fontSize: 14.sp, color: Colours.white),
                        ),
                      ],
                    ),
                  ),
                ),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    logic.isShare.value = !logic.isShare.value;
                  },
                  child: Container(
                    padding: EdgeInsets.only(top: 20.w, bottom: 20.w),
                    child: Row(
                      children: [
                        logic.isShare.value
                            ? WxAssets.images.checkOn3
                                .image(width: 16.w, height: 16.w)
                            : WxAssets.images.checkOn3Wihte
                                .image(width: 16.w, height: 16.w),
                        SizedBox(
                          width: 6.w,
                        ),
                        Text(
                          "共享到场地展示为精彩视频",
                          style: TextStyles.medium
                              .copyWith(fontSize: 14.sp, color: Colours.white),
                        )
                      ],
                    ),
                  ),
                ),
                Container(
                  height: 46.w,
                  alignment: Alignment.centerLeft,
                  decoration: BoxDecoration(
                    color: Colours.color0F0F16,
                    borderRadius: BorderRadius.circular(23.r),
                  ),
                  margin: EdgeInsets.only(bottom: 5.w, top: 20.w),
                  padding: EdgeInsets.only(
                      left: 8.w, right: 5.w, top: 4.w, bottom: 3.w),
                  child: TextField(
                    controller: logic.nickNameController,
                    style: TextStyle(
                      color: Colours.white,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w400,
                    ),
                    inputFormatters: [
                      FilteringTextInputFormatter.deny(
                          RegExp(r'[" "]')), // 只允许输入数字
                      LengthLimitingTextInputFormatter(20), // 限制输入长度为8
                    ],
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      hintText: S.current.video_name,
                      contentPadding: EdgeInsets.only(left: 10.w, bottom: 10.w),
                      hintStyle: TextStyle(
                        color: Colours.color999999,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () async {
                    //一键成片
                    Get.back();
                    await Future.delayed(const Duration(milliseconds: 100));
                    logic.compositeVideo(list2);
                  },
                  child: Container(
                    height: 46.w,
                    width: double.infinity,
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(top: 25.w, bottom: 15.w),
                    padding: EdgeInsets.only(
                        left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                    decoration: BoxDecoration(
                      color: Colours.color282735,
                      borderRadius: BorderRadius.all(Radius.circular(28.r)),
                      gradient: const LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: Text(
                      S.current.One_click_make_piece,
                      style: TextStyles.semiBold14,
                    ),
                  ),
                ),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    logic.rememberOption.value =
                        logic.rememberOption.value == "0" ? "1" : "0";
                  },
                  child: Container(
                    margin: EdgeInsets.only(top: 3.w),
                    alignment: Alignment.center,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                            width: 14.w,
                            height: 14.w,
                            margin: EdgeInsets.only(right: 8.w, bottom: 3.w),
                            child: logic.rememberOption.value == "0"
                                ? MyImage(
                                    "check_on_3.png",
                                    width: 16.w,
                                    height: 16.w,
                                    isAssetImage: true,
                                  )
                                : MyImage(
                                    "check_on_3_wihte.png",
                                    width: 16.w,
                                    height: 16.w,
                                    isAssetImage: true,
                                    imageColor: Colours.color353542,
                                    bgColor: Colours.color353542,
                                    radius: 8.w,
                                  )

                            // Icon(
                            //   logic.state.rememberOption.value == "0"
                            //       ? Icons.check_circle
                            //       : Icons.radio_button_unchecked,
                            //   color: logic.state.rememberOption.value == "0"
                            //       ? Colours.white
                            //       : Colours.color9393A5,
                            //   size: 17,
                            // ),
                            ),
                        Text(
                          S.current.composite_video_dialog_tips12,
                          style: TextStyles.medium.copyWith(
                              fontSize: 12.sp, color: Colours.color9393A5),
                        )
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: 30.w,
                ),
                SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
              ],
            ),
          );
        });
      },
    );
  }
}
