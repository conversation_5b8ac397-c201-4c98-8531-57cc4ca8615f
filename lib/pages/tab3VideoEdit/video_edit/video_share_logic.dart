import 'package:get/get.dart';

import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/video/video_controller.dart';

class VideoShareLogic extends GetxController {
  late VideoController videoController;
  late String videoPath;
  bool isVertical = false;
  @override
  void onInit() async {
    final map = Get.arguments as Map;
    videoPath = map['videoPath'];
    videoController = VideoController(videoPath: videoPath);
    if (map.containsKey('isVertical')) {
      isVertical = true;
    }
    super.onInit();
  }

  @override
  void onClose() {
    videoController.dispose();
    super.onClose();
  }

  void share() async {
    // MyShareH5.getShareH5(ShareHighlights(
    //     sharedFrom: UserManager.instance.userInfo.value?.userId ?? "",
    //     highlightId: video.id));
  }

  void downloadAndSaveVideo() {
    Utils.downloadAndSaveToPhotoAlbum(videoPath);
  }

  void showDeleteDialog() {
    // Get.dialog(CustomAlertDialog(
    //   title: S.current.confirm_deletion,
    //   content: S.current.video_removal_tips,
    //   onPressed: () async {
    //     AppPage.back();
    //     final res = await Api().delete(ApiUrl.deleteHighlights + video.id);
    //     if (res.isSuccessful()) {
    //       AppPage.back(result: true);
    //     }
    //   },
    // ));
  }
}
