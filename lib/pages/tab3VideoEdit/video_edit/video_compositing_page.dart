import 'dart:developer' as cc;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 我的球队列表->新增球队
class VideoCompositingPage extends StatelessWidget {
  const VideoCompositingPage({super.key});

  // final logic = Get.put(StadiumVideoLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('视频合成'),
      ),
      body: Center(
        child: SizedBox(
            height: 200.w,
            width: ScreenUtil().screenWidth,
            child: myNoDataView(
              isbold: true,
              context,
              textColor: Colors.white,
              msg: '视频生成中，请稍后\n 您可以在云台“我的视频”中查看',
              imagewidget: WxAssets.images.battleEmptyIcon
                  .image(width: 70.w, height: 70.w),
            )),
      ),
      bottomNavigationBar: InkWell(
        onTap: () {},
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            GestureDetector(
              onTap: () {
                // AppPage.to(Routes.ptzHomePage);
              },
              child: Container(
                width: (ScreenUtil().screenWidth - 45) / 2,
                height: 44.w,
                alignment: Alignment.center,
                margin: EdgeInsets.only(
                    left: 15.w, bottom: ScreenUtil().bottomBarHeight),
                decoration: BoxDecoration(
                    border: Border.all(color: Colors.white, width: 1),
                    borderRadius: BorderRadius.circular(22.r)),
                child: Text(
                  '继续拍摄',
                  style: TextStyles.semiBold14,
                ),
              ),
            ),
            GestureDetector(
                onTap: () {
                  // AppPage.to(Routes.sourceVideoPage);
                },
                child: Container(
                  width: (ScreenUtil().screenWidth - 45) / 2,
                  height: 44.w,
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(
                      right: 15.w, bottom: ScreenUtil().bottomBarHeight),
                  decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(22.r)),
                  child: Text(
                    '去我的视频中查看',
                    style: TextStyles.semiBold14,
                  ),
                ))
          ],
        ),
      ),
    );
  }
}
