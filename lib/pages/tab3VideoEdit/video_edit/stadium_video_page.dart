import 'dart:developer' as cc;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab3VideoEdit/video_edit/stadium_video_logic.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 我的球队列表->新增球队
class StadiumVideoPage extends StatelessWidget {
  StadiumVideoPage({super.key});

  final logic = Get.put(StadiumVideoLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('球场视频'),
      ),
      body: Center(
        child: SizedBox(
            height: 200.w,
            width: ScreenUtil().screenWidth,
            child: myNoDataView(
              context,
              msg: '已上传片段数：199 / 322',
              imagewidget:
                  WxAssets.images.noVideos.image(width: 120.w, height: 60.w),
            )),
      ),
      bottomNavigationBar: InkWell(
        onTap: () {},
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Container(
              width: (ScreenUtil().screenWidth - 45) / 2,
              height: 44.w,
              alignment: Alignment.center,
              margin: EdgeInsets.only(
                  left: 15.w, bottom: ScreenUtil().bottomBarHeight),
              decoration: BoxDecoration(
                  border: Border.all(color: Colors.white, width: 1),
                  borderRadius: BorderRadius.circular(22.r)),
              child: Text(
                '返回首页',
                style: TextStyles.semiBold14,
              ),
            ),
            Container(
              width: (ScreenUtil().screenWidth - 45) / 2,
              height: 44.w,
              alignment: Alignment.center,
              margin: EdgeInsets.only(
                  right: 15.w, bottom: ScreenUtil().bottomBarHeight),
              decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Colours.color7732ED, Colours.colorA555EF],
                    begin: Alignment.bottomLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(22.r)),
              child: Text(
                '继续拍摄',
                style: TextStyles.semiBold14,
              ),
            )
          ],
        ),
      ),
    );
  }
}
