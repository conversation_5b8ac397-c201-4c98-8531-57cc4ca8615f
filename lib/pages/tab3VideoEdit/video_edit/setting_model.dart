///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class SettingModel {
/*
{'title': '进球慢放', 'isSelect': false}
*/

  String? title;
  bool? isSelect;

  SettingModel({
    this.title,
    this.isSelect
  });
  SettingModel.fromJson(Map<String, dynamic> json) {
    title = json['title']?.toString();
    isSelect = json['isSelect'];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['title'] = title;
    data['isSelect'] = isSelect;
    return data;
  }
}
