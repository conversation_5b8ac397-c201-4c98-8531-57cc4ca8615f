// ignore_for_file: use_build_context_synchronously
import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab3VideoEdit/video_edit/video_clip_selection_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/keep_alive_widget.dart';
import 'package:shoot_z/widgets/video/video_view.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';
import 'dart:developer' as cc;

///半场自由投篮->去剪辑 选球片段
class VideoClipSelectionPage extends StatelessWidget {
  VideoClipSelectionPage({super.key});

  final logic = Get.put(VideoClipSelectionLogic());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('球场视频'),
        actions: [
          IconButton(
              onPressed: () {
                AppPage.to(Routes.videoSharePage, arguments: {
                  "videoPath": logic.currentSelectModel.value.videoPath
                });
              },
              icon: Image.asset('assets/images/share_icon.png'))
        ],
      ),
      body: Obx(() {
        return (logic.dataFag["isFrist"] as bool)
            ? buildLoad()
            : KeepAliveWidget(
                child: Stack(
                  children: [
                    SafeArea(
                      bottom: false,
                      child: Padding(
                        padding:
                            const EdgeInsets.only(top: 10, left: 15, right: 15),
                        child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                // color: Colors.red,
                                height: 24,
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      children: [
                                        Text(
                                          textAlign: TextAlign.start,
                                          '片段',
                                          style: TextStyles.semiBold14,
                                        ),
                                        const SizedBox(
                                          width: 4,
                                        ),
                                        ShaderMask(
                                          shaderCallback: (Rect bounds) {
                                            return const LinearGradient(
                                              colors: [
                                                Color(0xFF7732ED),
                                                Color(0xFFA555EF)
                                              ], // 渐变颜色
                                              begin: Alignment.topLeft, // 渐变起点
                                              end:
                                                  Alignment.bottomRight, // 渐变终点
                                            ).createShader(bounds);
                                          },
                                          child: Text(
                                            textAlign: TextAlign.start,
                                            (logic.state.indexVideoLibrary
                                                        .value +
                                                    1)
                                                .toString()
                                                .padLeft(2, '0'),
                                            style: const TextStyle(
                                                fontFamily: 'DIN',
                                                height: 0.81111,
                                                fontSize: 30,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.white),
                                          ),
                                        )
                                      ],
                                    ),
                                    const Text('10:22',
                                        style: TextStyle(
                                            fontFamily: 'DIN',
                                            fontSize: 14,
                                            height: 0.81111,
                                            color: Colors.white))
                                  ],
                                ),
                              ),
                              const SizedBox(
                                height: 15,
                              ),
                              //视频播放器
                              _videoWidget(),
                              const SizedBox(
                                height: 20,
                              ),
                              //选球
                              Expanded(child: _optionGoalWidget(context)),
                            ]),
                      ),
                    ),
                  ],
                ),
              );
      }),
      bottomNavigationBar: createBottomWidget(context),

      // floatingActionButton: FloatingActionButton(
      //   onPressed: () {
      //     // 按钮点击事件处理代码
      //     //logic.getDateInfo();
      //     logic.aIoptionCheckID.value =
      //         logic.aIoptionCheckID.value == "" ? "1" : "";
      //   },
      //   tooltip: 'Increment Counter', // 提示信息（长按显示）
      //   child: Icon(Icons.add), // 按钮内部的图标
      //   backgroundColor: Colors.pink, // 按钮背景颜色
      // ),
      floatingActionButtonLocation:
          FloatingActionButtonLocation.endFloat, // 默认位置
    );
  }

  Widget createBottomWidget(BuildContext context, {bool isDialog = false}) {
    return Obx(() {
      return (logic.dataFag["isFrist"] as bool)
          ? const SizedBox()
          : Container(
              // width: ScreenUtil().screenWidth,
              color: const Color(0xff191921),
              padding: EdgeInsets.only(
                  bottom: 34.w, left: 15.w, right: 15.w, top: 15.w),
              child: Row(
                children: [
                  GestureDetector(
                      onTap: () {
                        if (isDialog) {
                          return;
                        }
                        if (logic.dataList
                            .where((value) {
                              return value.checked == true;
                            })
                            .toList()
                            .isNotEmpty) {
                          logic.curSelectAll.value = true;
                          logic.getCheckedVideoList();
                          _getTypeDialog(context);
                        }
                      },
                      child: Row(
                        children: [
                          WxAssets.images.selectIcon
                              .image(width: 44.w, height: 44.w),
                          SizedBox(
                            width: 10.w,
                          ),
                          SizedBox(
                            height: 44.w,
                            // constraints: BoxConstraints(
                            //   maxWidth: ScreenUtil().screenWidth -
                            //       30 -
                            //       44 -
                            //       25 -
                            //       48 -
                            //       165 -
                            //       12, // 设置最大宽度为200.0
                            // ),
                            // color: Colors.red,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  S.current.selected,
                                  style: TextStyles.regular.copyWith(
                                      fontSize: 12.sp, color: Colors.white),
                                ),
                                SizedBox(
                                  height: 6.w,
                                ),
                                RichText(
                                  textAlign: TextAlign.left,
                                  text: TextSpan(
                                      text: "${logic.dataList.where((value) {
                                            return value.checked == true;
                                          }).toList().length}\t",
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 12.sp,
                                        height: 1.5,
                                      ),
                                      children: <InlineSpan>[
                                        TextSpan(
                                            text: S.current
                                                .option_player_goal_tip1,
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 12.sp,
                                              height: 1,
                                            )),
                                      ]),
                                ),
                              ],
                            ),
                          ),
                        ],
                      )),
                  SizedBox(
                    width: 15.w,
                  ),
                  isDialog
                      ? TextButton(
                          onPressed: () {
                            logic.curSelectAll.value =
                                !logic.curSelectAll.value;
                            logic.selectAllOrCancelSelectAllDialog(
                                logic.curSelectAll.value);
                          },
                          style: TextButton.styleFrom(
                            minimumSize: Size.zero,
                            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            padding: EdgeInsets.zero,
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              logic.curSelectAll.value
                                  ? WxAssets.images.selectIcon.image()
                                  : WxAssets.images.unselectIcon.image(),
                              SizedBox(width: 6.w),
                              Text('全选', style: TextStyles.display14),
                            ],
                          ),
                        )
                      : TextButton(
                          onPressed: () {
                            logic.selectAll.value = !logic.selectAll.value;
                            logic.selectAllOrCancelSelectAll(
                                logic.selectAll.value);
                          },
                          style: TextButton.styleFrom(
                            minimumSize: Size.zero,
                            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            padding: EdgeInsets.zero,
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              logic.selectAll.value
                                  ? WxAssets.images.selectIcon.image()
                                  : WxAssets.images.unselectIcon.image(),
                              SizedBox(width: 6.w),
                              Text('全选', style: TextStyles.display14),
                            ],
                          ),
                        ),
                  const Spacer(),
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      if (Utils.isToLogin()) {
                        // if (await logic.getVideosUsedShots2(logic.arenaID.value,
                        //     type: 1)) {
                        //   //没有合成次数和vip的时候弹窗
                        //   getVIPDialog();
                        //   // logic.getShareWx(0);
                        // } else {
                        var list = logic.dataList.where((value) {
                          return value.checked == true;
                        }).toList();
                        if ((list.length) <= 1) {
                          WxLoading.showToast("请至少选择2个视频片段");
                        } else {
                          _showCustomSettingDialog(context);
                        }
                        // }
                      }

                      // if (Utils.isToLogin()) {
                      //   if (await logic.getVideosUsedShots2(logic.arenaID.value,
                      //       type: 1)) {
                      //     //没有合成次数和vip的时候弹窗
                      //     getVIPDialog();
                      //     // logic.getShareWx(0);
                      //   } else {
                      //     var list = logic.dataList.where((value) {
                      //       return value.checked == true;
                      //     }).toList();
                      //     if ((list.length) <= 1) {
                      //       WxLoading.showToast("请至少选择2个视频片段");
                      //     } else {
                      //       logic.videoController.pause();
                      //       log("optionPlayerGoalModel3=${logic.dataList[0].checked}");
                      //       //去合成进球
                      //       AppPage.to(Routes.shootCompositeVideoPage,
                      //           arguments: {
                      //             "trainingId": logic.trainingId.value,
                      //             "reportId": logic.reportId.value,
                      //             "type": logic.type.value,
                      //             "list": list,
                      //           }).then((v) {});
                      //       var list2 = logic.dataList.where((value) {
                      //         return value.checked == true;
                      //       }).toList();
                      //       log("optionPlayerGoalModel31=${list2.length}");
                      //     }
                      //   }
                      // }
                    },
                    child: Container(
                      height: 44.w,
                      width: 165.w,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(25.r)),
                        gradient: const LinearGradient(
                          colors: [Colours.color7732ED, Colours.colorA555EF],
                          begin: Alignment.bottomLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                      child: Text(
                        '去合成视频',
                        style:
                            TextStyles.titleMedium18.copyWith(fontSize: 14.sp),
                      ),
                    ),
                  ),
                ],
              ),
            );
    });
  }

  //没有合成次数和vip的时候弹窗
  void getVIPDialog() {
    return getMyDialog2(
      "",
      S.current.sure,
      () {
        AppPage.back();
      },
      // btnText2: S.current.Go_points,
      // onPressed2: () {
      //   AppPage.back();
      //   AppPage.to(Routes.pointsPage);
      // },
      isShowClose: true,
      imageAsset: "vip_dialog10.png",
      imgHeight: 165.w,
      btnIsHorizontal: true,
      imgWidth: double.infinity,
      bottomBtnWidget: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Expanded(
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                AppPage.back();
                AppPage.to(Routes.vipPage).then((v) async {
                  await Future.delayed(const Duration(milliseconds: 1000));
                  if ((UserManager.instance.userInfo.value?.vipLevel ?? 0) ==
                      0) {
                    WxLoading.showToast(S.current.Please_open_vip_first);
                  } else {
                    if (!await logic.getVideosUsedShots2(logic.arenaID.value,
                        type: 1)) {
                      //去合成进球
                      AppPage.to(Routes.compositeVideoPage, arguments: {
                        "arenaID": logic.arenaID.value,
                      }).then((v) {});
                    }
                  }
                });
              },
              child: Container(
                height: 46.w,
                width: double.infinity,
                alignment: Alignment.center,
                margin: EdgeInsets.only(
                  left: 20.w,
                  top: 15.w,
                  right: 7.5.w,
                ),
                decoration: BoxDecoration(
                  color: Colours.color282735,
                  borderRadius: BorderRadius.all(Radius.circular(28.r)),
                  gradient: const LinearGradient(
                    colors: [Colours.color7732ED, Colours.colorA555EF],
                    begin: Alignment.bottomLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: Text(
                  S.current.Open_immediately,
                  style: TextStyles.regular
                      .copyWith(fontSize: 14.sp, color: Colours.white),
                ),
              ),
            ),
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () async {
              AppPage.back();
              await Future.delayed(const Duration(milliseconds: 500));
              getVIPInvitationDialog2();
            },
            child: Column(
              children: [
                Transform.translate(
                  offset: const Offset(-10, 10),
                  child: MyImage(
                    "vip_dialog11.png",
                    width: 154.w,
                    // height: 30.w,
                    fit: BoxFit.fitWidth,
                    isAssetImage: true,
                  ),
                ),
                Container(
                  height: 46.w,
                  width: 144,
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(
                    left: 7.5.w,
                    right: 20.w,
                  ),
                  decoration: BoxDecoration(
                    color: Colours.color282735,
                    borderRadius: BorderRadius.all(Radius.circular(28.r)),
                    gradient: const LinearGradient(
                      colors: [Colours.colorBFFF9C, Colours.colorEEFC62],
                      begin: Alignment.bottomLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: Text(
                    S.current.vip_dialog_text10,
                    style: TextStyles.regular
                        .copyWith(fontSize: 14.sp, color: Colours.color333333),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      contentWidget: Container(
          alignment: Alignment.topCenter,
          margin: EdgeInsets.only(left: 40.w, right: 40.w, top: 10.w),
          child: Wrap(
            spacing: 21.w,
            runSpacing: 10.w,
            children: List.generate(2, (index) {
              return Container(
                width: 54.w,
                alignment: Alignment.center,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 44.w,
                      height: 44.w,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          MyImage(
                            "vip_dialog12.png",
                            width: 44.w,
                            height: 44.w,
                            isAssetImage: true,
                            radius: 11.r,
                          ),
                          MyImage(
                            "",
                            width: 26.w,
                            height: 26.w,
                            isAssetImage: true,
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 4.w,
                    ),
                    Text(
                      "",
                      style: TextStyles.regular.copyWith(
                          fontSize: 10.sp, color: Colours.color7732ED),
                    )
                  ],
                ),
              );
            }),
          )),
    );
  }

//规则说明 邀请新人
  void getVIPInvitationDialog2() {
    return getMyDialog2(
      "",
      S.current.sure,
      () {
        AppPage.back();
      },
      isShowClose: false,
      btnIsHorizontal: true,
      imgWidth: double.infinity,
      contentTopRadius: 16.r,
      bottomBtnWidget: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  logic.getShareWx(0);
                },
                child: Column(
                  children: [
                    MyImage(
                      "vip_Invitation_dialog5.png",
                      width: 36.w,
                      height: 36.w,
                      fit: BoxFit.fitWidth,
                      isAssetImage: true,
                    ),
                    SizedBox(
                      height: 6.w,
                    ),
                    Text(
                      S.current.vip_Invitation_dialog_text9,
                      style: TextStyles.regular.copyWith(
                          fontSize: 13.sp, color: Colours.color333333),
                    )
                  ],
                ),
              ),
              // GestureDetector(
              //   behavior: HitTestBehavior.translucent,
              //   onTap: () {
              //     logic.getShareWx(1);
              //   },
              //   child: Column(
              //     children: [
              //       MyImage(
              //         "vip_Invitation_dialog6.png",
              //         width: 36.w,
              //         height: 36.w,
              //         fit: BoxFit.fitWidth,
              //         isAssetImage: true,
              //       ),
              //       SizedBox(
              //         height: 6.w,
              //       ),
              //       Text(
              //         S.current.vip_Invitation_dialog_text10,
              //         style: TextStyles.regular.copyWith(
              //             fontSize: 13.sp, color: Colours.color333333),
              //       )
              //     ],
              //   ),
              // ),
            ],
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              AppPage.back();
            },
            child: Container(
              width: 100.w,
              height: 40.w,
              alignment: Alignment.bottomCenter,
              child: Text(
                S.current.cancel,
                style: TextStyles.regular
                    .copyWith(fontSize: 11.sp, color: Colours.colorA44EFF),
              ),
            ),
          )
        ],
      ),
      contentWidget: Container(
          alignment: Alignment.topCenter,
          padding: EdgeInsets.only(left: 40.w, right: 40.w),
          decoration: BoxDecoration(
            color: Colours.color282735,
            borderRadius: BorderRadius.all(Radius.circular(16.r)),
            gradient: const LinearGradient(
              colors: [Colours.colorD2ABFF, Colours.white],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: Column(
            children: [
              SizedBox(
                height: 20.w,
              ),
              MyImage(
                "vip_Invitation_dialog7.png",
                width: 187.w,
                height: 26.w,
                isAssetImage: true,
              ),
              SizedBox(
                height: 20.w,
              ),
              Column(
                children: List.generate(2, (index) {
                  return Container(
                    width: double.infinity,
                    height: 44.w,
                    alignment: Alignment.center,
                    child: Row(
                      children: [
                        MyImage(
                          "",
                          width: 13.w,
                          height: 13.w,
                          isAssetImage: true,
                        ),
                        SizedBox(
                          width: 10.w,
                        ),
                        Expanded(
                          child: Container(
                            padding: EdgeInsets.only(
                                left: 10.w, right: 10.w, top: 6.w, bottom: 4.w),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8.r),
                                color: Colours.color7732ED),
                            child: Row(
                              children: [
                                RichText(
                                  textAlign: TextAlign.left,
                                  text: TextSpan(
                                      text: index == 0
                                          ? S.current
                                              .vip_Invitation_dialog_text1
                                          : index == 1
                                              ? S.current
                                                  .vip_Invitation_dialog_text3
                                              : index == 2
                                                  ? S.current
                                                      .vip_Invitation_dialog_text5
                                                  : S.current
                                                      .vip_Invitation_dialog_text6,
                                      style: TextStyle(
                                          color: Colours.white,
                                          fontSize: 12.sp,
                                          height: 1,
                                          fontWeight: FontWeight.w400),
                                      children: <InlineSpan>[
                                        TextSpan(
                                            text: index == 0
                                                ? S.current
                                                    .vip_Invitation_dialog_text2
                                                : index == 1
                                                    ? S.current
                                                        .vip_Invitation_dialog_text4
                                                    : index == 2
                                                        ? ""
                                                        : S.current
                                                            .vip_Invitation_dialog_text7,
                                            style: TextStyle(
                                                color: Colours.colorCDFDDE,
                                                fontSize: 12.sp,
                                                height: 1,
                                                fontWeight: FontWeight.w400)),
                                        TextSpan(
                                            text: index == 3
                                                ? S.current
                                                    .vip_Invitation_dialog_text8
                                                : "",
                                            style: TextStyle(
                                                color: Colours.white,
                                                fontSize: 12.sp,
                                                height: 1,
                                                fontWeight: FontWeight.w400)),
                                      ]),
                                ),
                                Expanded(
                                    child: Text(
                                        " -----------------------------",
                                        maxLines: 1,
                                        style: TextStyle(
                                            color: Colours.white,
                                            fontSize: 12.sp,
                                            height: 1,
                                            overflow: TextOverflow.clip,
                                            fontWeight: FontWeight.w400)))
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ),
            ],
          )),
    );
  }

  Widget _videoWidget() {
    return ClipRRect(
        borderRadius: BorderRadius.circular(8.r),
        child: SizedBox(
          width: double.infinity,
          height: (ScreenUtil().screenWidth - 30) / 345 * 194,
          child: AspectRatio(
            aspectRatio: 345 / 194, // 宽高比
            child: VideoView(
              controller: logic.videoController,
            ),
          ),
        ));
  }

  Widget _optionGoalWidget(BuildContext context) {
    return Obx(() {
      return logic.dataList.isEmpty
          ? myNoDataView(
              context,
              msg: S.current.no_goal,
              imagewidget:
                  WxAssets.images.noGoal.image(width: 100.w, height: 84.w),
            )
          : ListView(
              children: [
                Wrap(
                  runSpacing: 10.w,
                  spacing: 10.w,
                  children: List.generate(logic.dataList.length, (position) {
                    return (logic.chooseType.value != "" &&
                                logic.dataList[position].shootType.toString() !=
                                    logic.chooseType.value) ||
                            (logic.chooseState.value != "" &&
                                ((logic.dataList[position].hit ?? false) !=
                                    (logic.chooseState.value ==
                                        "1"))) //1进球  2打铁
                        ? const SizedBox(
                            width: 0,
                          )
                        : GestureDetector(
                            behavior: HitTestBehavior.translucent,
                            onTap: () async {
                              // if (position == logic.state.indexVideo.value) {
                              //   if (await Utils.isToLogin()) {
                              //     logic.checkVideo(position);
                              //   }
                              // } else {
                              logic.changeVideoIndex(position);
                              // }
                              cc.log('!!!!!!!!!!!');
                            },
                            child: Container(
                              height: (ScreenUtil().screenWidth - 60.w) / 4,
                              width: (ScreenUtil().screenWidth - 60.w) / 4,
                              decoration: BoxDecoration(
                                  image: const DecorationImage(
                                    image: NetworkImage(
                                        'https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/banner/zxf.png'),
                                    fit: BoxFit.cover, // 可以根据需要调整图片的填充方式
                                  ),
                                  color: position ==
                                          logic.state.indexVideoLibrary.value
                                      ? Colours.color291A3B
                                      : Colours.color191921,
                                  borderRadius: BorderRadius.circular(8.r),
                                  border: position ==
                                          logic.state.indexVideoLibrary.value
                                      ? Border.all(
                                          width: 2, color: Colors.white)
                                      : null),
                              child: Stack(
                                alignment: Alignment.center,
                                children: [
                                  Positioned(
                                    top: 0.w,
                                    right: 0.w,
                                    child: GestureDetector(
                                      onTap: () {
                                        logic.checkVideo(position);
                                        cc.log('222222');
                                      },
                                      child: SizedBox(
                                        width: 26.w,
                                        height: 26.w,
                                        child: Center(
                                          child: logic.dataList[position]
                                                      .checked ??
                                                  false
                                              ? WxAssets.images.selectIcon
                                                  .image(
                                                      height: 16.w, width: 16.w)
                                              : WxAssets.images.unselectIcon
                                                  .image(
                                                      height: 16.w,
                                                      width: 16.w),
                                        ),
                                      ),
                                    ),
                                  ),
                                  Positioned(
                                      bottom: 5.w,
                                      left: 5.w,
                                      child: Text(
                                        logic.dataList[position].time ?? "",
                                        textAlign: TextAlign.left,
                                        style: TextStyles.regular.copyWith(
                                          fontFamily: 'DIN',
                                          fontSize: 12.sp,
                                        ),
                                      )),
                                ],
                              ),
                            ),
                          );
                  }),
                ),
              ],
            );
    });
  }

  //选择类型
  void _getTypeDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return Container(
            width: double.infinity,
            //height: 632,
            // margin: EdgeInsets.only(bottom: 70.w),
            decoration: BoxDecoration(
              color: const Color(0xFF191921),
              borderRadius: BorderRadius.all(Radius.circular(8.r)),
            ),
            child: Column(mainAxisSize: MainAxisSize.min, children: [
              // _optionGoalWidget(context),
              Container(
                width: 38.w,
                height: 4.w,
                margin: const EdgeInsets.only(top: 8, bottom: 20),
                decoration: BoxDecoration(
                    color: const Color(0x1AD8D8D8),
                    borderRadius: BorderRadius.all(Radius.circular(2.r))),
              ),
              Text(
                '已选片段',
                style: TextStyles.titleSemiBold16,
              ),
              SizedBox(
                height: 20.w,
              ),
              Container(
                width: ScreenUtil().screenWidth,
                margin: const EdgeInsets.only(left: 15, right: 15),
                child: Wrap(
                  runSpacing: 10.w,
                  spacing: 15.w,
                  children:
                      List.generate(logic.selectDataList.length, (position) {
                    return GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () async {
                        logic.selectCheckVideo(position);
                      },
                      child: Container(
                        height: (ScreenUtil().screenWidth - 75.w) / 4,
                        width: (ScreenUtil().screenWidth - 75.w) / 4,
                        decoration: BoxDecoration(
                          image: const DecorationImage(
                            image: NetworkImage(
                                'https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/banner/zxf.png'),
                            fit: BoxFit.cover, // 可以根据需要调整图片的填充方式
                          ),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            Positioned(
                              top: 0.w,
                              right: 0.w,
                              child: SizedBox(
                                width: 26.w,
                                height: 26.w,
                                child: Center(
                                  child:
                                      logic.selectDataList[position].checked ??
                                              false
                                          ? WxAssets.images.selectIcon
                                              .image(height: 16.w, width: 16.w)
                                          : WxAssets.images.unselectIcon
                                              .image(height: 16.w, width: 16.w),
                                ),
                              ),
                            ),
                            Positioned(
                                bottom: 5.w,
                                left: 5.w,
                                child: Text(
                                  logic.selectDataList[position].time ?? "",
                                  textAlign: TextAlign.left,
                                  style: TextStyles.regular.copyWith(
                                    fontFamily: 'DIN',
                                    fontSize: 12.sp,
                                  ),
                                )),
                          ],
                        ),
                      ),
                    );
                  }),
                ),
              ),
              createBottomWidget(context, isDialog: true)
            ]),
          );
        });
      },
    );
  }

  void _showCustomSettingDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return Container(
            width: double.infinity,
            //height: 632,
            decoration: BoxDecoration(
              color: const Color(0xFF191921),
              borderRadius: BorderRadius.all(Radius.circular(8.r)),
            ),
            child: Column(mainAxisSize: MainAxisSize.min, children: [
              // _optionGoalWidget(context),
              Container(
                width: 38.w,
                height: 4.w,
                margin: const EdgeInsets.only(top: 8, bottom: 20),
                decoration: BoxDecoration(
                    color: const Color(0x1AD8D8D8),
                    borderRadius: BorderRadius.all(Radius.circular(2.r))),
              ),
              Text(
                '视频编辑',
                style: TextStyles.titleSemiBold16,
              ),
              SizedBox(
                height: 20.w,
              ),
              Container(
                margin: const EdgeInsets.only(left: 15),
                width: double.infinity,
                child: Text(
                  '视频效果与个性化',
                  textAlign: TextAlign.left,
                  style: TextStyles.semiBold14,
                ),
              ),
              SizedBox(
                height: 10.w,
              ),
              Wrap(
                runSpacing: 15.w,
                spacing: 15.w,
                children: List.generate(logic.settingBtnList.length, (index) {
                  return GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      logic.settingBtnList[index].isSelect =
                          !(logic.settingBtnList[index].isSelect ?? false);
                      // cc.log("message${logic.settingBtnList[index]}");
                      logic.settingBtnList.refresh();
                    },
                    child: Container(
                      height: 40.w,
                      width: (ScreenUtil().screenWidth - 45.w) / 2,
                      decoration: BoxDecoration(
                          gradient: logic.settingBtnList[index].isSelect == true
                              ? const LinearGradient(
                                  colors: [
                                    Colours.color7732ED,
                                    Colours.colorA555EF
                                  ],
                                  begin: Alignment.bottomLeft,
                                  end: Alignment.bottomRight,
                                )
                              : null,
                          borderRadius: BorderRadius.circular(20.r),
                          border: logic.settingBtnList[index].isSelect == false
                              ? Border.all(width: 1, color: Colors.white)
                              : null),
                      child: Center(
                        child: Text(
                          logic.settingBtnList[index].title ?? '',
                          style: TextStyles.bold.copyWith(
                            fontSize: 14.sp,
                          ),
                        ),
                      ),
                    ),
                  );
                }),
              ),
              SizedBox(
                height: 20.w,
              ),
              Container(
                margin: const EdgeInsets.only(left: 15),
                width: double.infinity,
                child: Text(
                  '视频标题',
                  textAlign: TextAlign.left,
                  style: TextStyles.semiBold14,
                ),
              ),
              Container(
                margin: const EdgeInsets.only(
                    left: 15, right: 15, top: 10, bottom: 20),
                padding: const EdgeInsets.only(left: 20, right: 20),
                height: 50.w,
                decoration: BoxDecoration(
                    color: Colours.color0F0F16,
                    borderRadius: BorderRadius.all(Radius.circular(25.r))),
                child: TextField(
                  controller: logic.txtController1,
                  style: TextStyles.regular,
                  decoration: InputDecoration(
                    fillColor: Colours.color0F0F16,
                    filled: true,
                    hintText: '请输入视频标题',
                    hintStyle:
                        TextStyles.regular.copyWith(color: Colours.color5C5C6E),
                    contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
                    //让文字垂直居中,
                    border: InputBorder.none,
                  ),
                  keyboardType: TextInputType.name,
                ),
              ),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  // AppPage.to(Routes.videoCompositingPage);
                },
                child: Container(
                  height: 44.w,
                  margin: const EdgeInsets.only(left: 15, right: 15),
                  decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(22.r)),
                  child: Center(
                    child: Text(
                      '去合成视频',
                      style: TextStyles.bold.copyWith(
                        fontSize: 14.sp,
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 34.w,
              ),
            ]),
          );
        });
      },
    );
  }
}
