import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab3VideoEdit/video_edit/video_share_logic.dart';
import 'package:shoot_z/widgets/video/video_view.dart';
import 'package:ui_packages/ui_packages.dart';

class VideoSharePage extends StatelessWidget {
  VideoSharePage({super.key});

  final VideoShareLogic logic = Get.put(VideoShareLogic());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(''),
      ),
      body: SafeArea(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // _title(context),
              // SizedBox(
              //   height: 47.w,
              // ),
              _video(context),
              SizedBox(
                height: 20.w,
              ),
              _action(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _action(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 26),
      child: Wrap(
        spacing: 64.w,
        children: [
          GestureDetector(
            onTap: () => logic.share(),
            child: Container(
              width: 46.w,
              height: 46.w,
              decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(23.w),
              ),
              child: WxAssets.images.icShare.image(width: 24.w, height: 24.w),
            ),
          ),
          GestureDetector(
            onTap: () => logic.downloadAndSaveVideo(),
            child: Container(
              width: 46.w,
              height: 46.w,
              decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(23.w),
              ),
              child: WxAssets.images.icDownload.image(width: 22.w, height: 2.w),
            ),
          ),
          GestureDetector(
            onTap: () => logic.showDeleteDialog(),
            child: Container(
              width: 46.w,
              height: 46.w,
              decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(23.w),
              ),
              child: WxAssets.images.icDelete.image(width: 22.w, height: 22.w),
            ),
          ),
        ],
      ),
    );
  }

  Widget _video(BuildContext context) {
    return Expanded(
      child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
        logic.isVertical
            ? ClipRRect(
                borderRadius: BorderRadius.circular(8.r),
                child: SizedBox(
                  width: (ScreenUtil().screenHeight - 310) / 532 * 300,
                  height: ScreenUtil().screenHeight - 310,
                  child: AspectRatio(
                    aspectRatio: 300 / 532, // 宽高比
                    child: VideoView(
                      controller: logic.videoController,
                    ),
                  ),
                ))
            : ClipRRect(
                borderRadius: BorderRadius.circular(8.r),
                child: SizedBox(
                  width: ScreenUtil().screenWidth - 30,
                  height: (ScreenUtil().screenWidth - 30) / 345 * 194,
                  child: AspectRatio(
                    aspectRatio: 345 / 194, // 宽高比
                    child: VideoView(
                      controller: logic.videoController,
                    ),
                  ),
                )),
        SizedBox(
          height: 20.w,
        ),
        Text(
          '大江东去，浪淘尽，千古风流人物...',
          style: TextStyles.textBold16.copyWith(color: Colors.white),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        SizedBox(
          height: 15.w,
        ),
        Text(
          '2024.8.14 周日 20:32',
          style: TextStyles.display12.copyWith(color: Colours.color5C5C6E),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        )
      ]),
    );
  }
}
