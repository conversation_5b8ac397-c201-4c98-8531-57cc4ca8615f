import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/invite_mini_path_model.dart';
import 'package:shoot_z/network/model/shoot_goal_model.dart';
import 'package:shoot_z/network/model/user_is_new_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab3VideoEdit/video_edit/setting_model.dart';
import 'package:shoot_z/pages/tab3Create/shoot_composite/shoot_goal/shoot_goal_state.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/utils/myShareH5.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/dialo_view.dart';
import 'package:shoot_z/widgets/video/video_controller.dart';

class HalfVideoSelectionLogic extends GetxController
    with WidgetsBindingObserver, GetSingleTickerProviderStateMixin {
  final ShootGoalLogicState state = ShootGoalLogicState();
  TextEditingController txtController1 = TextEditingController();
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  final VideoController videoController =
      VideoController(pushDisposeOnAndroid: true);
  var arenaID = 0.obs;
  var isFrist = true.obs;
  var isVip = false.obs;
  StreamSubscription<EventAction>? eventbus;
  var trainingId = "".obs;
  var reportId = "".obs;
  var type = "".obs; //1单人 2多人
  var chooseType = "".obs;
  var chooseState = "".obs;
  var selectAll = false.obs;
  var curSelectAll = true.obs;
  var dataList = <ShootGoalModel>[].obs;
  var currentSelectModel = ShootGoalModel().obs;
  var selectDataList = <ShootGoalModel>[].obs;
  var settingBtnList = <SettingModel>[].obs;
  // [
  //     {'title': '进球慢放', 'isSelect': false},
  //     {'title': '保留原声', 'isSelect': false},
  //     {'title': '去除水印', 'isSelect': false},
  //     {'title': '背景音乐', 'isSelect': false}
  //   ].obs;
  @override
  Future<void> onInit() async {
    super.onInit();
    settingBtnList.value = [
      {'title': '进球慢放', 'isSelect': false},
      {'title': '保留原声', 'isSelect': false},
      {'title': '去除水印', 'isSelect': false},
      {'title': '背景音乐', 'isSelect': false}
    ].map((e) => SettingModel.fromJson(e)).toList();
    log("onInitonInitonInit");
    WidgetsBinding.instance.addObserver(this);
    trainingId.value = Get.arguments["trainingId"];
    reportId.value = Get.arguments["reportId"];
    type.value = Get.arguments["type"];
    // 当 isVip 变化时，执行回调
    ever(isVip, (value) {
      print("isVip 变了，新值: $value");
      videoController.showWatermark(!value);
    });
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    eventbus = BusUtils.instance.on((p0) async {
      if (p0.key == EventBusKey.loginSuccessful) {
        if (UserManager.instance.isLogin) {
          isVip.value = await getVideosUsedShots(arenaID.value, type: 1);
        }
      } else if (p0.key == EventBusKey.receiveVip) {
        if (UserManager.instance.isLogin) {
          isVip.value = await getVideosUsedShots(arenaID.value, type: 1);
        }
      }
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 这里的代码将在当前帧结束后执行
      refresh();
    });
    if (UserManager.instance.isLogin) {
      isVip.value = await getVideosUsedShots(arenaID.value, type: 1);
    }
    await getDataList(isLoad: false);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {}

  reloadVideo(String videoUrl) async {
    if (videoUrl.isEmpty) {
      log("betterPlayer-message11=$videoUrl");
      return;
    }
    videoController.setData(videoPath: videoUrl, showWatermark: !isVip.value);
  }

  //获得最新列表
  getDataList({
    isLoad = true,
  }) async {
    var param = {
      'id': trainingId.value,
      'reportId': reportId.value,
    };
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.show();
    }

    log("getTeamPlayerReport3=${param}");
    final res = await Api().get(
        ApiUrl.getShootingFragments(trainingId.value, reportId.value),
        queryParameters: param);
    log("getTeamPlayerReport4=${jsonEncode(res.data)}");
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.dismiss();
    }
    if (res.isSuccessful()) {
      List list = res.data;
      List<ShootGoalModel> modelList =
          list.map((e) => ShootGoalModel.fromJson(e)).toList();
      dataList.assignAll(modelList);
      log("zzzzzz12removeAt12-${dataList.length}");
      if (dataList.isNotEmpty) {
        changeVideoIndex(0);
        currentSelectModel.value = dataList[0];
      } else {
        state.indexVideoLibrary.value = 9999;
        videoController.setData(videoPath: '');
      }
      if (dataFag["isFrist"] as bool) {
        dataFag["isFrist"] = false;
        refresh();
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //查询用户已使用的片段以及是否vip
  Future<bool> getVideosUsedShots(var arenaId, {int type = 0}) async {
    var param = {
      'arenaId': arenaId,
    };
    if (!(dataFag["isFrist"] as bool) && type != 1) {
      WxLoading.show();
    }
    final res =
        await Api().get(ApiUrl.getVideosUsedShots, queryParameters: param);
    if (!(dataFag["isFrist"] as bool) && type != 1) {
      WxLoading.dismiss();
    }
    if (res.isSuccessful()) {
      log("getVideosUsedShots=${res.data["vip"]}");
      //{vip: false, shotCount: 0, remainingCount: 50, shots: null}
      //是否vip      shotCount已经使用的次数    remainingCount 剩余次数  Shots今日合成进球id
      if (!((res.data["vip"] ?? false) as bool)) {
        if (type == 0) {
          if (!(dataFag["isFrist"] as bool)) {
            WxLoading.show();
          }
          final res2 = await Api()
              .get(ApiUrl.userIsNew, queryParameters: param, showError: false);
          if (!(dataFag["isFrist"] as bool)) {
            WxLoading.dismiss();
          }
          if (res2.isSuccessful()) {
            var userIsNewModel = UserIsNewModel.fromJson(res2.data);
            log("getCanDownLoadVideoUrl=${res2.data}-${userIsNewModel.isFirstRegister?.receivedVip == true}${userIsNewModel.isFirstRegister?.receivableActivityId}");
            if (!(userIsNewModel.isFirstRegister?.receivedVip == true) &&
                userIsNewModel.isFirstRegister?.isNew == true) {
              //领取vip
              getReciverVipDialog(S.current.Receive_now, S.current.Receive_no,
                  () async {
                AppPage.back();
                if (!(dataFag["isFrist"] as bool)) {
                  WxLoading.show();
                }
                var param22 = {
                  'activityId':
                      userIsNewModel.isFirstRegister?.receivableActivityId,
                };
                var url = await ApiUrl.getReceiveVip(
                    userIsNewModel.isFirstRegister?.receivableActivityId ?? "");
                await Api()
                    .get(url, queryParameters: param22, showError: false);
                if (!(dataFag["isFrist"] as bool)) {
                  WxLoading.dismiss();
                }
                await UserManager.instance.pullUserInfo().then((v) async {
                  await Future.delayed(const Duration(milliseconds: 200));
                  if ((UserManager.instance.userInfo.value?.vipLevel ?? 0) ==
                      0) {
                    WxLoading.showToast(S.current.Please_open_vip_first);
                  } else {
                    //   //ai选球
                    return true;
                  }
                });
                BusUtils.instance
                    .fire(EventAction(key: EventBusKey.receiveVip));
              }, () {
                AppPage.back();
              });
            } else {
              //开通vip
              getNeedVipDialog(
                  S.current.Open_immediately, S.current.Talk_to_you_next_time,
                  () {
                AppPage.back();
                AppPage.to(Routes.vipPage).then((v) async {
                  await Future.delayed(const Duration(milliseconds: 1000));
                  if ((UserManager.instance.userInfo.value?.vipLevel ?? 0) ==
                      0) {
                    WxLoading.showToast(S.current.Please_open_vip_first);
                  } else {
                    return true;
                  }
                });
              }, () {
                AppPage.back();
              });
            }
          } else {
            WxLoading.showToast(res.message);
            return false;
          }
        } else {
          return false;
        }
      } else {
        return true;
      }
    } else {
      if (type == 0) {
        WxLoading.showToast(res.message);
      }
      return false;
    }
    return false;
  }

  //查询用户已使用的片段以及是否vip 是否 分享获取权限 0 没有 1有 ；
  Future<bool> getVideosUsedShots2(var arenaId, {int type = 0}) async {
    var param = {
      'arenaId': arenaId,
    };
    if (!(dataFag["isFrist"] as bool) && type != 1) {
      WxLoading.show();
    }
    final res =
        await Api().get(ApiUrl.getVideosUsedShots, queryParameters: param);

    if (res.isSuccessful()) {
      log("getVideosUsedShots2=${res.data}-user=${UserManager.instance.user?.userId}");
      // log("getVideosUsedShots2=${res.data["shareRights"]}");
      // log("getVideosUsedShots21=${(res.data["shareRights"] ?? 0) != 1}");
      //{vip: false, shotCount: 0, remainingCount: 50, shots: null}
      //是否vip      shotCount已经使用的次数    remainingCount 剩余次数  Shots今日合成进球id
      if ((res.data["shareRights"] ?? 0) != 1) {
        //是否 分享获取权限 0 没有 1有
        return false;
      } else {
        return true;
      }
    } else {
      return false;
    }
  }

  //判断视频是否能下载  然后返回最新下载地址
  getCanDownLoadVideoUrl(String fragmentId, String videoId) async {
    var param = {
      'fragmentId': fragmentId,
      'videoId': videoId,
    };
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.show();
    }
    final res = await Api().get(ApiUrl.getCanDownLoadVideoUrl,
        queryParameters: param, showError: false);
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.dismiss();
    }
    log("getCanDownLoadVideoUrl=${res.data}-${res.code}");
    if (res.isSuccessful()) {
      downloadAndSaveVideo(res.data["videoPath"]);
    } else if (res.code == 400001) {
      if (!(dataFag["isFrist"] as bool)) {
        WxLoading.show();
      }
      final res2 = await Api()
          .get(ApiUrl.userIsNew, queryParameters: param, showError: false);
      if (!(dataFag["isFrist"] as bool)) {
        WxLoading.dismiss();
      }
      if (res2.isSuccessful()) {
        var userIsNewModel = UserIsNewModel.fromJson(res2.data);
        log("getCanDownLoadVideoUrl=${res2.data}-${userIsNewModel.isFirstRegister?.receivedVip == true}${userIsNewModel.isFirstRegister?.receivableActivityId}");
        if (!(userIsNewModel.isFirstRegister?.receivedVip == true)) {
          //领取vip
          getReciverVipDialog(S.current.Receive_now, S.current.Receive_no,
              () async {
            AppPage.back();
            if (!(dataFag["isFrist"] as bool)) {
              WxLoading.show();
            }
            var param22 = {
              'activityId':
                  userIsNewModel.isFirstRegister?.receivableActivityId,
            };
            var url = await ApiUrl.getReceiveVip(
                userIsNewModel.isFirstRegister?.receivableActivityId ?? "");
            await Api().get(url, queryParameters: param22, showError: false);
            if (!(dataFag["isFrist"] as bool)) {
              WxLoading.dismiss();
            }
            await UserManager.instance.pullUserInfo();
            await Future.delayed(const Duration(milliseconds: 200));
            isVip.value = await getVideosUsedShots(arenaID.value, type: 1);
            if ((UserManager.instance.userInfo.value?.vipLevel ?? 0) == 0) {
              // WxLoading.showToast(S.current.Please_open_vip_first);
            } else {
              getDownLoad();
            }
          }, () {
            AppPage.back();
          });
        } else {
          //开通vip
          getNeedVipDialog(
              S.current.Open_immediately, S.current.Talk_to_you_next_time, () {
            AppPage.back();
            AppPage.to(Routes.vipPage).then((v) async {
              await Future.delayed(const Duration(milliseconds: 1000));
              if ((UserManager.instance.userInfo.value?.vipLevel ?? 0) == 0) {
                WxLoading.showToast(S.current.Please_open_vip_first);
              } else {
                getDownLoad();
              }
            });
          }, () {
            AppPage.back();
          });
        }
      } else {
        WxLoading.showToast(res.message);
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }

  getDownLoad() {
    {
      var videoId = dataList[state.indexVideoLibrary.value].id ?? "";

      if ((dataList[state.indexVideoLibrary.value].videoPath ?? "").isEmpty ||
          state.indexVideoLibrary.value == 9999 ||
          videoId == "") {
        WxLoading.showToast(S.current.Please_select_video_click_Download);
        return;
      }
      if (UserManager.instance.isLogin) {
        getCanDownLoadVideoUrl(
            dataList[state.indexVideoLibrary.value].id ?? "", videoId);
      } else {
        AppPage.to(Routes.login).then((onValue) async {
          await Future.delayed(const Duration(milliseconds: 500));
          if (UserManager.instance.isLogin) {
            getCanDownLoadVideoUrl(
                dataList[state.indexVideoLibrary.value].id ?? "", videoId);
          } else {
            WxLoading.showToast(S.current.please_login);
          }
        });
      }
    }
  }

  @override
  void onClose() {
    isVip.close();
    videoController.dispose();
    eventbus?.cancel();
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  //改变选择视频index
  void changeVideoIndex(
    int index,
  ) {
    state.indexVideoLibrary.value = index;
    if (state.indexVideoLibrary.value != 9999) {
      log("betterPlayer-message11=3-${dataList[index].videoPath ?? ""}");
      reloadVideo(dataList[index].videoPath ?? "");
      currentSelectModel.value = dataList[index];
    }
  }

  //新增数据和删除数据
  Future<void> checkVideo(int index) async {
    dataList[index].checked = !(dataList[index].checked ?? false);
    if (dataList[index].checked == false) {
      selectAll.value = false;
    } else {
      selectAll.value = dataList.every((model) => model.checked == true);
    }
    dataList.refresh();
    log("checkVideo2-2");
    // if (dataList[index].checked == true) {
    //   if ((index + 1) < (dataList.length)) {
    //     changeVideoIndex(index + 1);
    //   }
    // }
  }

  //获取当前选中数组
  Future<void> getCheckedVideoList() async {
    selectDataList.value = dataList.where((value) {
      return value.checked == true;
    }).toList();
  }

  //新增数据和删除数据
  Future<void> selectCheckVideo(int index) async {
    ShootGoalModel curModel = selectDataList[index];
    int curIndex = dataList.indexWhere((item) => item.id == curModel.id);
    dataList[curIndex].checked = !(dataList[curIndex].checked ?? false);
    dataList.refresh();
    if (curModel.checked == false) {
      selectAll.value = false;
    } else {
      selectAll.value = selectDataList.every((model) => model.checked == true);
    }
    selectDataList.refresh();
  }

  //全选和取消全选
  selectAllOrCancelSelectAll(bool selectAll) {
    for (var item in dataList) {
      item.checked = selectAll;
    }
    dataList.refresh();
  }

  //弹框页面的全选和取消全选
  selectAllOrCancelSelectAllDialog(bool selectAll) {
    for (var item in selectDataList) {
      int curIndex = dataList.indexWhere((model) => model.id == item.id);
      dataList[curIndex].checked = selectAll;
      item.checked = selectAll;
    }
    dataList.refresh();
    selectDataList.refresh();
  }

  //下载视频
  void downloadAndSaveVideo(String path) {
    Utils.downloadAndSaveToPhotoAlbum(path);
  }

  @override
  void dispose() {
    super.dispose();
  }

//分享去邀请新人   0微信  1朋友圈
  Future<void> getShareWx(int i) async {
    var param = {
      'shareType': "1",
    };
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.show();
    }
    final res = await Api().get(ApiUrl.inviteMiniPath, queryParameters: param);
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.dismiss();
    }
    if (res.isSuccessful()) {
      if (res.data == null) {
        WxLoading.showToast(S.current.No_data_available);
        return;
      }
      log("getVideosUsedShots42=${jsonEncode(res.data)}-user=${UserManager.instance.user?.userId}");
      InviteMiniPathModel inviteMiniPathModel =
          InviteMiniPathModel.fromJson(res.data);
      // 下载网络图片并转换为 Uint8List
      Uint8List? imageBytes =
          await downloadImageToUint8List(inviteMiniPathModel.image ?? "");
      if (imageBytes == null || inviteMiniPathModel.image == "") {
        ByteData data = await rootBundle
            .load("assets/images/dialog_invitation.png"); //dialog_invitation
        imageBytes = data.buffer.asUint8List();
      }
      MyShareH5.shareMiniProgram(inviteMiniPathModel, imageBytes);
      // List list = res.data;
      // List<AiOptionModel> modelList =
      //     list.map((e) => AiOptionModel.fromJson(e)).toList();
      // log("getAiOptionGoal=${res.data}");
      // state.aiDataList.assignAll(modelList);
    } else {
      WxLoading.showToast(res.message);
    }

    if (i == 0) {
    } else {}
  }

  Future<Uint8List?> downloadImageToUint8List(String imageUrl) async {
    try {
      // 发起 HTTP GET 请求获取图片数据
      final response = await http.get(Uri.parse(imageUrl));

      // 检查响应状态码是否成功
      if (response.statusCode == 200) {
        // 将响应体的字节数据转换为 Uint8List
        return Uint8List.fromList(response.bodyBytes);
      } else {
        print("Failed to load image: ${response.statusCode}");
        return null;
      }
    } catch (e) {
      print("Error downloading image: $e");
      return null;
    }
  }
}
