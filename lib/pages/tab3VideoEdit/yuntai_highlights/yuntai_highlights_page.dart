// ignore_for_file: unused_element

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab3VideoEdit/yuntai_highlights/yuntai_highlights_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/keep_alive_widget.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

/// 赛程列表页面
class YuntaiHighlightsPage extends StatelessWidget {
  YuntaiHighlightsPage({super.key});
  final logic = Get.put(YuntaiHighlightsLogic());
  @override
  Widget build(BuildContext context) {
    return KeepAliveWidget(
      child: Obx(() {
        return SmartRefresher(
            controller: logic.refreshController,
            footer: buildFooter(),
            header: buildClassicHeader(),
            enablePullDown: true,
            enablePullUp: logic.dataList.isNotEmpty,
            onLoading: () {
              logic.getdataList();
            },
            onRefresh: () {
              logic.getdataList(isLoad: false);
            },
            physics: const AlwaysScrollableScrollPhysics(),
            //  physics: const NeverScrollableScrollPhysics(),
            child: (logic.dataFag["isFrist"] as bool)
                ? buildLoad()
                : logic.dataList.isEmpty
                    ? SizedBox(
                        height: 380.w,
                        child: myNoDataView(
                          context,
                          msg: S.current.No_data_available,
                          imagewidget: WxAssets.images.teamInfoNodata
                              .image(width: 150.w, height: 150.w),
                        ))
                    : ListView.builder(
                        // physics: const ClampingScrollPhysics(),
                        padding: EdgeInsets.symmetric(horizontal: 15.w),
                        // shrinkWrap: true,
                        itemCount: logic.dataList.length,
                        itemBuilder: (context, index) {
                          final group = logic.dataList[index];
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // 分组标题
                              Row(children: [
                                Expanded(
                                  child: GestureDetector(
                                    onTap: () {
                                      if (int.parse(group.venueId ?? "0") !=
                                          0) {
                                        AppPage.to(Routes.siteDetailPage,
                                            arguments: {
                                              'venueId': int.parse(
                                                  group.venueId ?? "0")
                                            });
                                      }
                                    },
                                    child: Text(
                                      group.venueName ?? "",
                                      style: TextStyles.display12.copyWith(
                                          color: Colours.colorA8A8BC,
                                          fontWeight: FontWeight.w400),
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 1,
                                    ),
                                  ),
                                ),
                                SizedBox(width: 10.w),
                                Text(
                                  (group.time?.replaceAll('.', '-') ?? "")
                                      .substring(
                                          0,
                                          (group.time?.replaceAll('.', '-') ??
                                                  "")
                                              .length
                                              .clamp(0, 10)),
                                  style: TextStyles.din
                                      .copyWith(color: Colours.color5C5C6E),
                                ),
                              ]),
                              SizedBox(
                                height: 15.w,
                              ),
                              // 分组内容
                              GridView.builder(
                                scrollDirection: Axis.vertical,
                                // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                                shrinkWrap: true,
                                physics:
                                    const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                                gridDelegate:
                                    SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 2,
                                  crossAxisSpacing: 15.w,
                                  mainAxisSpacing: 15.w,
                                  childAspectRatio: 165 / 124,
                                ),
                                padding: EdgeInsets.only(bottom: 20.w),
                                itemCount: group.items?.length,
                                itemBuilder: (context, itemIndex) {
                                  final video = group.items?[itemIndex];
                                  return Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      GestureDetector(
                                        behavior: HitTestBehavior.opaque,
                                        onTap: () async {
                                          final result = await AppPage.to(
                                              Routes.highlightsVideo,
                                              arguments: {
                                                'type': 1,
                                                'video': video,
                                                'group': group
                                              }).then((v) {
                                            if (v != null) {
                                              logic.getdataList(isLoad: false);
                                            }
                                          });
                                          if (result == true) {
                                            // 如果结果为 true，表示需要刷新列表
                                            group.items?.removeAt(itemIndex);
                                            if (group.items!.isEmpty) {
                                              logic.dataList.removeAt(index);
                                            }
                                            logic.dataList.refresh(); // 刷新数据
                                          }
                                        },
                                        child: SizedBox(
                                          height: 94.w,
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(8.w),
                                            child: Stack(
                                              children: [
                                                Positioned(
                                                  left: 0,
                                                  right: 0,
                                                  bottom: 0,
                                                  top: 0,
                                                  child: MyImage(
                                                    video?.cover ?? "",
                                                    width: 50.w,
                                                    height: 60.w,
                                                    isAssetImage: false,
                                                    radius: 4.r,
                                                    placeholderImage:
                                                        "ic_hl_pdz.png",
                                                    errorImage: "ic_hl_pdz.png",
                                                  ),
                                                ),
                                                Center(
                                                    child: WxAssets
                                                        .images.selfieShotPlay
                                                        .image(
                                                            width: 28.w,
                                                            height: 28.w)),
                                                if (video?.createTime != "")
                                                  Positioned(
                                                    bottom: 5.w,
                                                    right: 5.w,
                                                    child: Container(
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                              horizontal: 6.w,
                                                              vertical: 2.w),
                                                      decoration: BoxDecoration(
                                                        color:
                                                            Colours.color000000,
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(2.r),
                                                      ),
                                                      child: Text(
                                                        video?.createTime ?? "",
                                                        style: TextStyles.medium
                                                            .copyWith(
                                                                fontSize: 10.sp,
                                                                color: Colours
                                                                    .white),
                                                      ),
                                                    ),
                                                  ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(
                                        height: 15.w,
                                      ),
                                      SizedBox(
                                        height: 15.w,
                                        child: Text(
                                          textAlign: TextAlign.left,
                                          video?.name ?? "暂无",
                                          style: TextStyles.semiBold14,
                                        ),
                                      )
                                    ],
                                  );
                                },
                              ),
                            ],
                          );
                        },
                      ));
      }),
    );
  }
}
