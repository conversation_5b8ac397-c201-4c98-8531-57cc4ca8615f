import 'dart:convert';
import 'dart:developer';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/yuntai_highlights_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab1Home/highlights/models/highlights_model.dart';

class YuntaiHighlightsLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  var currentTabIndex = 0.obs;
  var dataFag = {
    "isFrist": true,
    "page": 1,
  }.obs;

  /// 是否正在加载数据
  var totalRows = 0;

  var userId = "";

  //数据列表
  var dataList = <YuntaiHighlightsModel>[].obs;
  @override
  void onInit() {
    super.onInit();
    userId = UserManager.instance.user?.userId ?? "";
    if (Get.arguments != null && Get.arguments.containsKey('userId')) {
      userId = Get.arguments['userId'];
    }
    getdataList(isLoad: false);
  }

  //获得最新列表
  getdataList({isLoad = true}) async {
    if (isLoad) {
      dataFag["page"] = (dataFag["page"] as int) + 1;
    } else {
      dataFag["page"] = 1;
    }

    Map<String, dynamic> param = {
      'pageIndex': (dataFag["page"] as int),
      'pageSize': "10",
      'userId': userId,
    };
    log("myYunTaiHighlights2-${param}");
    var res =
        await Api().get(ApiUrl.myYunTaiHighlights, queryParameters: param);
    if (res.isSuccessful()) {
      log("myYunTaiHighlights3-${jsonEncode(res.data)}");
      List<YuntaiHighlightsModel> modelList = (res.data['result'] as List)
          .map((e) => YuntaiHighlightsModel.fromJson(e))
          .toList();
      totalRows = res.data["totalRows"];
      if (isLoad) {
        dataList.addAll(modelList);
        dataList.refresh();
        if (modelList.length < 10) {
          refreshController.loadNoData();
        } else {
          refreshController.loadComplete();
        }
      } else {
        refreshController.resetNoData();
        dataList.assignAll(modelList);
        refreshController.refreshCompleted();
      }
    } else {
      refreshController.refreshCompleted();
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  @override
  void dispose() {
    super.dispose();
  }
}
