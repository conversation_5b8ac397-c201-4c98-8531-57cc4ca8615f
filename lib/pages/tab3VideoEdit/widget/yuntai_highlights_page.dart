// ignore_for_file: unused_element

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab3VideoEdit/widget/yuntai_highlights_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/more_widget.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

/// 赛程列表页面
class YuntaiHighlightsPage extends StatelessWidget {
  YuntaiHighlightsPage({super.key});
  final logic = Get.put(YuntaiHighlightsLogic());
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Column(
        children: [
          Expanded(
            child: NotificationListener(
                onNotification: (ScrollNotification note) {
                  if (note.metrics.pixels == note.metrics.maxScrollExtent) {
                    logic.loadMore();
                  }
                  return true;
                },
                child: RefreshIndicator(
                  onRefresh: logic.onRefresh,
                  child: logic.init.value
                      ? Obx(() {
                          return (logic.dataList.isEmpty
                              ? _buildEmptyView(context)
                              : _list(context));
                        })
                      : buildLoad(),
                )),
          )
        ],
      );
    });
  }

  Widget _refreshList(BuildContext context) {
    return NotificationListener(
      onNotification: (ScrollNotification note) {
        if (note.metrics.pixels == note.metrics.maxScrollExtent) {
          logic.loadMore();
        }
        return true;
      },
      child:
          RefreshIndicator(onRefresh: logic.onRefresh, child: _list(context)),
    );
  }

  Widget _list(BuildContext context) {
    return Obx(
      () => ListView.builder(
        // physics: const ClampingScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        // shrinkWrap: true,
        itemCount: logic.dataList.length + 1,
        itemBuilder: (context, index) {
          if (index == logic.dataList.length) {
            return SizedBox(); //_moreWidget();
          }
          final group = logic.dataList[index];
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 分组标题
              Row(children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () => AppPage.to(Routes.arenaDetailsPage,
                        arguments: {"id": int.parse(group.arenaId)}),
                    child: Text(
                      group.arenaName,
                      style: TextStyles.display12
                          .copyWith(color: Colours.colorA8A8BC),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                ),
                SizedBox(width: 10.w),
                Text(
                  group.date.replaceAll('.', '-'),
                  style: TextStyles.din.copyWith(color: Colours.color5C5C6E),
                ),
              ]),
              SizedBox(
                height: 15.w,
              ),
              // 分组内容
              GridView.builder(
                scrollDirection: Axis.vertical,
                // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                shrinkWrap: true,
                physics:
                    const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 15.w,
                  mainAxisSpacing: 15.w,
                  childAspectRatio: 165 / 124,
                ),
                padding: EdgeInsets.only(bottom: 20.w),
                itemCount: group.videos.length,
                itemBuilder: (context, itemIndex) {
                  final video = group.videos[itemIndex];
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () async {
                          if (!video.isFinish) {
                            return;
                          }
                          video.rxNew.value = false;
                          final result = await AppPage.to(
                                  Routes.highlightsVideo,
                                  arguments: {'video': video, 'group': group})
                              .then((v) {
                            if (v != null) {
                              logic.onRefresh();
                            }
                          });
                          ;
                          if (result == true) {
                            // 如果结果为 true，表示需要刷新列表
                            group.videos.removeAt(itemIndex);
                            if (group.videos.isEmpty) {
                              logic.dataList.removeAt(index);
                            }
                            logic.dataList.refresh(); // 刷新数据
                          }
                        },
                        child: SizedBox(
                          height: 94.w,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8.w),
                            child: Stack(
                              children: [
                                Positioned(
                                    left: 0,
                                    right: 0,
                                    bottom: 0,
                                    top: 0,
                                    child: video.isFinish
                                        ? CachedNetworkImage(
                                            imageUrl: video.videoCover,
                                            fit: BoxFit.cover,
                                          )
                                        : (video.status == 0
                                            ? WxAssets.images.icHlPdz
                                                .image(fit: BoxFit.fill)
                                            : WxAssets.images.icHlHcz
                                                .image(fit: BoxFit.fill))),
                                Center(
                                    child: WxAssets.images.selfieShotPlay
                                        .image(width: 28.w, height: 28.w))
                                // Obx(
                                //   () => Visibility(
                                //     visible: video.rxNew.value && video.isFinish,
                                //     child: Positioned(
                                //         top: 5,
                                //         left: 5,
                                //         child: WxAssets.images.icHlNew
                                //             .image(width: 38.w, height: 18.w)),
                                //   ),
                                // ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 15.w,
                      ),
                      SizedBox(
                        height: 15.w,
                        child: Text(
                          textAlign: TextAlign.left,
                          video.name,
                          style: TextStyles.semiBold14,
                        ),
                      )
                    ],
                  );
                },
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _moreWidget() {
    return Padding(
        padding: const EdgeInsets.only(bottom: 30, top: 5),
        child:
            MoreWidget(logic.dataList.length, logic.hasMore(), logic.pageSize));
  }

  /// 构建空状态视图
  Widget _buildEmptyView(BuildContext context) {
    return Center(
      child: myNoDataView(
        context,
        msg: '暂无生涯视频，快去创作吧',
        imagewidget: WxAssets.images.battleEmptyIcon.image(),
      ),
    );
  }

  _getStatusStr(int status) {
    switch (status) {
      case 0:
        return '未开始';
      case 1:
        return '报名中';
      case 2:
        return '待开赛';
      case 3:
        return '进行中';
      case 4:
        return '已结束';
      default:
        return '未开始';
    }
  }

  _getStatusColor(int status) {
    switch (status) {
      case 0:
        return Colours.color6435E9;
      case 1:
        return Colours.color6435E9;
      case 2:
        return Colours.color6435E9;
      case 3:
        return Colours.colorFF661A;
      case 4:
        return Colours.color262626;
      default:
        return Colours.color262626;
    }
  }
}
