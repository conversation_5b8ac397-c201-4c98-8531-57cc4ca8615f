import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab3Create/court_footage/venue_model.dart';

class SelectHalfcourtLogic extends GetxController {
  var halfCourtPics = [].obs;
  var selectHalfCourt = <VenueModelHalves>[].obs;
  var courtName = ''.obs;
  var venueId = 0;
  bool isAICreation = false;
  bool isCreateMatch = false;
  var init = false.obs;
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null) {
      if (Get.arguments.containsKey('courtName')) {
        courtName.value = Get.arguments['courtName'];
      }
      if (Get.arguments.containsKey('venueId')) {
        venueId = Get.arguments['venueId'];
      }
      if (Get.arguments.containsKey('halfCourtPics')) {
        halfCourtPics.value = Get.arguments['halfCourtPics'];
        init.value = true;
      } else {
        getVenueDetail();
      }
      if (Get.arguments.containsKey('isAICreation')) {
        isAICreation = Get.arguments['isAICreation'];
      }
      if (Get.arguments.containsKey('isCreateMatch')) {
        isCreateMatch = Get.arguments['isCreateMatch'];
      }
    }
  }

  Future<void> getVenueDetail() async {
    Map<String, dynamic> param = {'id': venueId};
    final res = await Api().get(ApiUrl.venueDetail, queryParameters: param);
    init.value = true;
    if (res.isSuccessful()) {
      VenueModel model = VenueModel.fromJson(res.data);
      List<VenueModelHalves?> halves = model.halves ?? [];
      halfCourtPics.value = halves;
    } else {
      WxLoading.showToast(res.message);
    }
  }

  @override
  void onReady() {
    super.onReady();
  }
}
