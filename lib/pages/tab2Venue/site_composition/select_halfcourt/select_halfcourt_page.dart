import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab2Venue/site_composition/select_halfcourt/select_halfcourt_logic.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/create_match_logic.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/model/competition_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/PhotoView.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

class SelectHalfcourtPage extends StatelessWidget {
  SelectHalfcourtPage({super.key});
  final logic = Get.put(SelectHalfcourtLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Obx(() => Text(logic.courtName.value)),
      ),
      body: SingleChildScrollView(child: Obx(() {
        return logic.init.value
            ? Column(
                children: [
                  ...logic.halfCourtPics.map((e) {
                    return Obx(() {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextButton(
                            onPressed: () {
                              if (logic.selectHalfCourt.contains(e)) {
                                logic.selectHalfCourt.remove(e);
                              } else {
                                if (logic.isAICreation &&
                                    logic.selectHalfCourt.length == 1) {
                                  WxLoading.showToast('最多只可以选择一个半场哦～');
                                  return;
                                }
                                if (logic.selectHalfCourt.length == 2) {
                                  WxLoading.showToast('最多只可以选择两个半场哦～');
                                  return;
                                }
                                logic.selectHalfCourt.add(e);
                              }
                            },
                            style: TextButton.styleFrom(
                              minimumSize: Size.zero,
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              padding: EdgeInsets.zero,
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                logic.selectHalfCourt.contains(e)
                                    ? WxAssets.images.selectIcon.image()
                                    : WxAssets.images.unselectIcon.image(),
                                SizedBox(width: 6.w),
                                Text('半场${e.name ?? ''}',
                                    style: TextStyles.titleSemiBold16),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 15.w,
                          ),
                          GridView.builder(
                            shrinkWrap:
                                true, //GridView 的大小会根据其内容动态调整，只占用内容所需的空间。
                            physics: const NeverScrollableScrollPhysics(),
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 2, // 每行两个 item
                              crossAxisSpacing: 15.w,
                              mainAxisSpacing: 15.w,
                              childAspectRatio: 150 / 84, // 控制每个 item 的宽高比例
                            ),
                            itemCount: (e.images ?? []).length,
                            padding: EdgeInsets.zero,
                            itemBuilder: (context, position) {
                              return GestureDetector(
                                onTap: () {
                                  Get.to(
                                    PhotoView(
                                      images: e.images ?? [],
                                      index: position,
                                      flag: 1,
                                    ),
                                  );
                                },
                                child: Container(
                                  width: double.infinity,
                                  height: 84.w,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8.r),
                                    image: DecorationImage(
                                      image: CachedNetworkImageProvider(
                                          e.images![position]!),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              );
                            },
                          )
                        ],
                      ).marginOnly(bottom: 15.w);
                    });
                  })
                ],
              ).marginAll(15.w)
            : buildLoad();
      })),
      bottomNavigationBar: InkWell(
        onTap: () {
          // logic.subscribeAIReport();
          List<Map<String, dynamic>> nameIdList =
              logic.selectHalfCourt.map((court) {
            return {
              'id': court.id,
              'name': court.name,
            };
          }).toList();
          if (nameIdList.isEmpty) {
            WxLoading.showToast("请先至少选择一个半场");
            return;
          }
          if (nameIdList.length > 2) {
            WxLoading.showToast("最多只能选择两个半场");
            return;
          }
          if (logic.isAICreation) {
            AppPage.to(Routes.selfieShotPage,
                arguments: {
                  'halfCourt': nameIdList,
                  'venueId': logic.venueId,
                  "venueName": logic.courtName.value
                },
                needLogin: true);
          } else if (logic.isCreateMatch) {
            if (nameIdList.length != 2) {
              WxLoading.showToast("要选择两个半场哦～");
              return;
            }
            final CreateMatchLogic targetController =
                Get.find<CreateMatchLogic>();
            targetController.venueName.value = logic.courtName.value;
            targetController.venueId = logic.venueId;

            if (targetController.isEdit) {
              for (var i = 0; i < logic.selectHalfCourt.length; i++) {
                var halfItem = targetController.halfCourtList[i];
                halfItem?.halfId = logic.selectHalfCourt[i].id;
                halfItem?.name = logic.selectHalfCourt[i].name;
              }
            } else {
              List<Map<String, dynamic>> halfCourtList =
                  logic.selectHalfCourt.map((court) {
                return {
                  'halfId': court.id,
                  'name': court.name,
                };
              }).toList();
              targetController.halfCourtList = halfCourtList
                  .map((e) => CompetitionModelHalf.fromJson(e))
                  .toList();
            }
            AppPage.back(page: Routes.createMatchPage, result: {
              'halfCourt': nameIdList,
              'venueId': logic.venueId,
              "venueName": logic.courtName.value
            });
          } else {
            //场地剪辑
            AppPage.to(Routes.VenueGoalPage,
                arguments: {
                  'halfCourt': nameIdList,
                  'venueId': logic.venueId,
                  "venueName": logic.courtName.value
                },
                needLogin: true);

            //    //场地剪辑
            // AppPage.to(Routes.VenueGoalPage,
            //     arguments: {
            //       "halfCourt": [
            //         {"id": "88", "name": "1-1"},
            //         {"id": "102", "name": "1-2"}
            //       ],
            //       "venueId": "29",
            //       "venueName": "8"
            //     },
            //     needLogin: true);
          }
        },
        child: Container(
          width: double.infinity,
          height: 50.w,
          alignment: Alignment.center,
          margin: EdgeInsets.only(
              left: 15.w, right: 15.w, bottom: ScreenUtil().bottomBarHeight),
          decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Colours.color7732ED, Colours.colorA555EF],
                begin: Alignment.bottomLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(25.r)),
          child: Text(
            logic.isCreateMatch ? '确定' : '下一步',
            style: TextStyles.semiBold14,
          ),
        ),
      ),
    );
  }
}
