import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab2Venue/arena_details/more_highlights/venue_highlights/venue_record_model.dart';
import 'package:shoot_z/pages/tab3Create/court_footage/venue_model.dart';
import 'package:shoot_z/utils/location_utils.dart';

class SiteDetailLogic extends GetxController {
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  var videoList = <VenueRecordModel>[].obs;
  var init = false.obs;
  var venueModel = VenueModel().obs;
  var venueId = 0;
  var distance = 0.0.obs;
  bool isAICreation = false;
  bool isCreateMatch = false;
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null) {
      if (Get.arguments.containsKey('distance')) {
        distance.value = Get.arguments['distance'];
      }
      if (Get.arguments.containsKey('venueId')) {
        venueId = Get.arguments['venueId'];
        getVenueDetail();
        getdataList(venueId);
      }
      if (Get.arguments.containsKey('isAICreation')) {
        isAICreation = Get.arguments['isAICreation'];
      }
      if (Get.arguments.containsKey('isCreateMatch')) {
        isCreateMatch = Get.arguments['isCreateMatch'];
      }
    }
  }

  Future<void> getVenueDetail() async {
    Map<String, dynamic> param = {'id': venueId};
    if (!Get.arguments.containsKey('distance')) {
      if (await LocationUtils.instance.checkPermission()) {
        await LocationUtils.instance.getCurrentPosition();
      }
      final position = LocationUtils.instance.position;
      if (position == null) {
        WxLoading.showToast(S.current.failed_location);
        return;
      }
      param["latitude"] = position.latitude;
      param["longitude"] = position.longitude;
    }

    // WxLoading.show();
    // var param = {
    //   'latitude': '${position.latitude}',
    //   'longitude': '${position.longitude}',
    //   'arenaID': arenaID.value,
    // };
    // }
    final res = await Api().get(ApiUrl.venueDetail, queryParameters: param);
    init.value = true;
    if (res.isSuccessful()) {
      venueModel.value = VenueModel.fromJson(res.data);
      if (!Get.arguments.containsKey('distance')) {
        distance.value = venueModel.value.distance ?? 0;
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }

//获得最新列表
  Future<void> getdataList(int arenaID) async {
    Map<String, dynamic> param = {
      'pageIndex': 1,
      'pageSize': 4,
      'venueId': arenaID,
    };
    var res = await Api().get(ApiUrl.venueAchievements, queryParameters: param);
    init.value = true;
    if (res.isSuccessful()) {
      final list = (res.data['result'] as List)
          .map((e) => VenueRecordModel.fromJson(e))
          .toList();
      videoList.value = list;
    } else {
      WxLoading.showToast(res.message);
    }
  }

  void share() async {
    // switch (type.value) {
    //   case "0": //普通半场
    //   case "1": //半场集锦
    //     MyShareH5.getShareH5(
    //       ShareVideosId(
    //           videosId: (featuredListModel.value.id ?? "").toString(),
    //           header: "0",
    //           type: "0"),
    //     ); // //0普通集锦  1半场集锦
    //     break;
    //   case "2": //教学分享
    //     MyShareH5.getShareH5(
    //       ShareVideosId2(
    //           videosId: (featuredListModel.value.id ?? "").toString(),
    //           header: "0",
    //           type: "1"),
    //     ); // //0普通集锦  1半场集锦
    //     break;
    // }
  }
  @override
  void onReady() {
    super.onReady();
  }
}
