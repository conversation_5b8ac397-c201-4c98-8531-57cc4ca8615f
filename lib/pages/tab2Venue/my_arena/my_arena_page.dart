import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab2Venue/my_arena/my_arena_logic.dart';
import 'package:shoot_z/pages/tab3Create/court_footage/venue_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

class MyArenaPage extends StatelessWidget {
  MyArenaPage({super.key});

  final logic = Get.put(MyArenaLogic());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.my_site),
      ),
      body: Obx(
        () => Column(
          children: [
            SizedBox(
              height: 15.w,
            ),
            Expanded(
              child: logic.init.value
                  ? NotificationListener(
                      onNotification: (ScrollNotification note) {
                        if (note.metrics.pixels ==
                            note.metrics.maxScrollExtent) {
                          logic.loadMore();
                        }
                        return true;
                      },
                      child: RefreshIndicator(
                        onRefresh: logic.onRefresh,
                        child: logic.allArenaList.isEmpty
                            ? _emptyView(context)
                            : Builder(builder: (context) {
                                return Obx(
                                  () => CustomScrollView(
                                    slivers: [
                                      if (logic.allArenaList.isNotEmpty)
                                        _allArenalist()
                                    ],
                                  ),
                                );
                              }),
                      ))
                  : buildLoad(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _emptyView(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: SizedBox(
              height: constraints.maxHeight,
              child: myNoDataView(
                context,
                msg: '暂无场地，快去创建吧～',
                imagewidget: WxAssets.images.battleEmptyIcon.image(),
              ),
            ));
      },
    );
  }

  SliverList _allArenalist() {
    return SliverList(
        delegate: SliverChildBuilderDelegate(
      (BuildContext context, int index) {
        final model = logic.allArenaList[index];
        return GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            if (model.auditStatus == 2) {
              if (logic.isAICreation || logic.isCreateMatch) {
                AppPage.to(Routes.selectHalfCourtPage, arguments: {
                  "courtName": model.name ?? "",
                  "venueId": model.id,
                  "isAICreation": logic.isAICreation,
                  "isCreateMatch": logic.isCreateMatch
                });
              } else {
                AppPage.to(Routes.siteDetailPage, arguments: {
                  'venueId': model.id,
                  'isAICreation': logic.isAICreation,
                  'isCreateMatch': logic.isCreateMatch
                });
              }
            }
          },
          child: Padding(
            padding: EdgeInsets.only(bottom: 15.w, left: 15.w, right: 15.w),
            child: Stack(children: [
              Container(
                padding: EdgeInsets.all(15.w),
                decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 96.w,
                          height: 96.w,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.r)),
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              MyImage(
                                model.coverUrl ?? '',
                                width: 96.w,
                                height: 96.w,
                                radius: 8.r,
                                errorImage: "error_image_width.png",
                                placeholderImage: "error_image_width.png",
                              ),
                              if (model.auditStatus != 2)
                                Positioned(
                                  bottom: 0.w,
                                  child: Container(
                                    width: 96.w,
                                    height: 96.w,
                                    alignment: Alignment.center,
                                    decoration: const BoxDecoration(
                                        color: Colours.color800F0F16),
                                  ),
                                ),
                              if (model.auditStatus == 3)
                                Positioned(
                                  top: 33.w,
                                  child: InkWell(
                                    onTap: () => AppPage.to(
                                            Routes.createArenaPage,
                                            arguments: {'venueId': model.id})
                                        .then((value) {
                                      if (value != null && value == true) {
                                        logic.onRefresh();
                                      }
                                    }),
                                    child: Container(
                                      width: 88.w,
                                      height: 30.w,
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                          gradient: const LinearGradient(
                                            colors: [
                                              Colours.color7732ED,
                                              Colours.colorA555EF,
                                            ],
                                            begin: Alignment.centerLeft,
                                            end: Alignment.centerRight,
                                          ),
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(15.r))),
                                      child: Text(
                                        '重新编辑',
                                        style: TextStyles.semiBold14
                                            .copyWith(fontSize: 12.sp),
                                      ),
                                    ),
                                  ),
                                )
                            ],
                          ),
                        ),
                        // ClipRRect(
                        //   borderRadius: BorderRadius.circular(8.r),
                        //   child: CachedNetworkImage(
                        //     imageUrl: model.coverUrl ?? '',
                        //     width: 96.w,
                        //     height: 96.w,
                        //     fit: BoxFit.cover,
                        //   ),
                        // ),
                        Expanded(
                          child: Padding(
                            padding: EdgeInsets.only(left: 15.w),
                            child: SizedBox(
                              height: 96.w,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    height: 5.w,
                                  ),
                                  Text(
                                    maxLines: 2,
                                    model.name ?? '',
                                    style: model.auditStatus == 2
                                        ? TextStyles.semiBold14
                                        : TextStyles.semiBold14.copyWith(
                                            color: Colours.color5C5C6E),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  SizedBox(
                                    height: 15.w,
                                  ),
                                  Expanded(
                                    child: Text(
                                      _getTagsStr(model)
                                          .whereType<String>()
                                          .join(' | '),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyles.display12.copyWith(
                                        color: Colours.color5C5C6E,
                                      ),
                                    ),
                                  ),
                                  const Spacer(),
                                  Text(
                                    _getStatusColor(
                                        model.auditStatus ?? 0)['title'],
                                    style: TextStyles.semiBold.copyWith(
                                        color: _getStatusColor(
                                            model.auditStatus ?? 0)['color'],
                                        fontSize: 12.sp),
                                  )
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    if ((model.remark ?? '').isNotEmpty)
                      Text(
                        '驳回原因：${model.remark ?? ''}',
                        style:
                            TextStyles.display12.copyWith(color: Colors.white),
                      ).marginOnly(top: 15.w)
                    //   Row(
                    //     children: [
                    //       Text(
                    //         '当前场馆人流量：',
                    //         style: TextStyles.display12,
                    //       ),
                    //       Container(
                    //         width: 10.w,
                    //         height: 10.w,
                    //         decoration: BoxDecoration(
                    //           shape: BoxShape.circle,
                    //           color: _getFlowColor(
                    //               model.pedestrianFlowStatus ?? 0)['color'],
                    //         ),
                    //       ),
                    //       SizedBox(
                    //         width: 5.w,
                    //       ),
                    //       Text(
                    //           _getFlowColor(
                    //               model.pedestrianFlowStatus ?? 0)['title'],
                    //           style: TextStyles.display12.copyWith(
                    //               color: _getFlowColor(
                    //                   model.pedestrianFlowStatus ??
                    //                       0)['color'])),
                    //     ],
                    //   ).marginOnly(top: 15.w)
                  ],
                ),
              ),
            ]),
          ),
        );
      },
      childCount: logic.allArenaList.length,
    ));
  }

  List<String> _getTagsStr(VenueModel model) {
    final floorMaterial = ['木地板', '塑胶', '悬浮拼接地板', '水泥地'];
    final hasLight = ['有灯光', '无灯光'];
    // final isFree = ['免费', '收费'];
    final openTime = ['不对外开放', '全天开放', '白天开放', '晚上开放'];
    final type = ['室内', '室外'];
    var result = <String>[];
    // final tagList = tags.split(',');
    result.add(type[(model.type ?? 1) - 1]);
    result.add(openTime[(model.openTime ?? 1) - 1]);
    result.add(floorMaterial[(model.floorMaterial ?? 1) - 1]);
    result.add(hasLight[(model.hasLight ?? 1) - 1]);

    return result;
  }

  Map<String, dynamic> _getStatusColor(int status) {
    switch (status) {
      case 1:
        return {'title': '待审核', 'color': Colours.white};
      case 2:
        return {'title': '已审核通过', 'color': Colours.color922BFF};
      case 3:
        return {'title': '审核未通过', 'color': Colours.colorFF3F3F};
      case 4:
        return {'title': '已失效', 'color': Colours.color5C5C6E};
      default:
        return {'title': '未知', 'color': Colours.color5C5C6E};
    }
  }
}
