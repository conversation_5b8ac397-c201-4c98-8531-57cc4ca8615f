import 'dart:developer';

import 'package:flutter_common/wx_loading.dart';
import 'package:intl/intl.dart';
import 'package:flutter_common/api.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/yuntai_highlights_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab2Venue/arena_details/more_highlights/record_model.dart';
import 'package:shoot_z/pages/tab2Venue/arena_details/more_highlights/venue_highlights/venue_record_model.dart';
import 'package:shoot_z/pages/tab5Mine/careerAndHighlights/model/venue_highlights_model.dart';
import 'package:shoot_z/utils/myShareH5.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/video/video_controller.dart';
import 'package:ui_packages/ui_packages.dart';
import '../../../../generated/l10n.dart';
import '../../../../routes/app.dart';
import '../models/highlights_model.dart';

class HighlightsVideoLogic extends GetxController {
  late VideoController videoController;
  late Videos video;
  late HighlightsModel group;
  late RecordModel record;
  late VenueRecordModel venueRecord;
  late VenueHighlightsModelItems venueItem;
  late VenueHighlightsModel venueGroup;
  var videoId = '';
  String videoPath = '';
  String arenaName = '';
  String arenaId = '';
  String videoCover = '';
  String videoName = '';
  String date = '';
  String week = '';
  int videoSize = 0;
  var isArenaDetail = false.obs;
  bool isVenue = false; //是否是半场投篮集锦
  var type = 0.obs;
  @override
  void onInit() async {
    final map = Get.arguments as Map;
    if (map.containsKey('type')) {
      type.value = map['type'];
      switch (type.value) {
        case 1: //云台的集锦播放
          isVenue = true;
          var video = map['video'] as YuntaiHighlightsModelItems;
          var group = map['group'] as YuntaiHighlightsModel;
          arenaName = group.venueName ?? "";
          arenaId = group.venueId ?? "";
          videoId = video.id!;
          videoPath = video.path ?? '';
          videoCover = video.cover ?? '';
          videoName = video.name ?? '';
          DateTime parsedStartDate = DateTime.parse(video.createTime ?? '');
          String formattedStartDate =
              DateFormat("yyyy.MM.dd").format(parsedStartDate);
          String chineseWeekday =
              DateFormat('EEEE', 'zh_CN').format(parsedStartDate);
          week = formattedStartDate;
          date = chineseWeekday;
          videoController =
              VideoController(videoPath: videoPath, videoCover: videoCover);
          break;
        default:
      }
    } else {
      if (map.containsKey('isVenue')) {
        isVenue = map['isVenue'];
      }
      if (map.containsKey('group')) {
        group = map['group'];
        arenaName = group.arenaName;
        arenaId = group.arenaId;
        week = group.week;
        date = group.date;
      }
      if (map.containsKey('video')) {
        video = map['video'];
        videoId = video.id;
        videoPath = video.videoPath;
        videoCover = video.videoCover;
        videoSize = video.videoSize;
        videoName = video.name;
      }

      if (map.containsKey('record')) {
        record = map['record'];
        videoId = record.id!;
        videoPath = record.videoPath ?? '';
        videoCover = record.cover ?? '';
        videoName = record.name ?? '';
        arenaId = record.arenaId ?? '';
        DateTime parsedStartDate = DateTime.parse(record.createdTime ?? '');
        String formattedStartDate =
            DateFormat("yyyy.MM.dd").format(parsedStartDate);
        String chineseWeekday =
            DateFormat('EEEE', 'zh_CN').format(parsedStartDate);
        week = chineseWeekday;
        date = formattedStartDate;
        isArenaDetail.value = true;
      }
      if (map.containsKey('venueRecord')) {
        venueRecord = map['venueRecord'];
        videoId = venueRecord.id!;
        videoPath = venueRecord.videoPath ?? '';
        videoCover = venueRecord.coverPath ?? '';
        videoName = venueRecord.title ?? '';
        arenaId = venueRecord.venueId ?? '';
        DateTime parsedStartDate =
            DateTime.parse(venueRecord.createdTime ?? '');
        String formattedStartDate =
            DateFormat("yyyy.MM.dd").format(parsedStartDate);
        String chineseWeekday =
            DateFormat('EEEE', 'zh_CN').format(parsedStartDate);
        week = formattedStartDate;
        date = chineseWeekday;
        isArenaDetail.value = true;
      }
      if (map.containsKey('venueItem')) {
        venueItem = map['venueItem'];
        videoId = venueItem.id ?? '0';
        videoPath = venueItem.videoPath ?? '';
        videoCover = venueItem.coverPath ?? '';
        videoName = venueItem.title ?? '';
      }
      if (map.containsKey('venueGroup')) {
        venueGroup = map['venueGroup'];
        arenaName =
            venueGroup.venueId == "0" ? "即刻创作" : venueGroup.venueName ?? '';
        arenaId = venueGroup.venueId ?? '0';
        week = venueGroup.week ?? '';
        date = venueGroup.mergeTime ?? '';
        isArenaDetail.value = false;
      }
      if (map.containsKey('arenaName')) {
        arenaName = map['arenaName'];
      }

      Api().post(ApiUrl.highlightsPlay(videoId));
      videoController =
          VideoController(videoPath: videoPath, videoCover: videoCover);
    }

    super.onInit();
  }

  @override
  void onClose() {
    videoController.dispose();
    super.onClose();
  }

  void share() async {
    if (isVenue) {
      MyShareH5.getShareH5(ShareVideosVenue(
          fragmentId: videoId,
          sharedFrom: UserManager.instance.userInfo.value?.userId ?? ""));
    } else {
      MyShareH5.getShareH5(ShareHighlights(
          sharedFrom: UserManager.instance.userInfo.value?.userId ?? "",
          highlightId: videoId,
          type: "0"));
    }
  }

  void downloadAndSaveVideo() {
    Utils.downloadAndSaveToPhotoAlbum(videoPath);
  }

  void showDeleteDialog() {
    Get.dialog(CustomAlertDialog(
      title: S.current.confirm_deletion,
      content: S.current.video_removal_tips,
      onPressed: () async {
        AppPage.back();
        if (isVenue) {
          Map<String, dynamic> param2 = {"id": videoId};
          var url = await ApiUrl.getDeleteVideo(videoId);
          var res = await Api().delete(url, data: param2);
          log("showDeleteDialog ${param2}--${res.data}");
          if (res.isSuccessful()) {
            AppPage.back(result: true);
          } else {
            WxLoading.showToast(res.message);
          }
        } else {
          final res = await Api().delete(ApiUrl.deleteHighlights + videoId);
          if (res.isSuccessful()) {
            AppPage.back(result: true);
          }
        }
      },
    ));
  }
}
