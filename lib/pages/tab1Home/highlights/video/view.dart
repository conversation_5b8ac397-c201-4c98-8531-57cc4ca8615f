import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab1Home/highlights/video/logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/video/video_view.dart';
import 'package:ui_packages/ui_packages.dart';

class HighlightsVideoPage extends StatefulWidget {
  const HighlightsVideoPage({super.key});

  @override
  State<HighlightsVideoPage> createState() => _HighlightsVideoPageState();
}

class _HighlightsVideoPageState extends State<HighlightsVideoPage> {
  late final HighlightsVideoLogic logic;
  late final String _controllerTag;

  @override
  void initState() {
    super.initState();
    // 使用唯一的tag来确保每次都创建新的Controller实例
    final arguments = Get.arguments as Map?;
    _controllerTag = arguments?['record']?.id ??
        DateTime.now().millisecondsSinceEpoch.toString();
    logic = Get.put(HighlightsVideoLogic(), tag: _controllerTag);
  }

  @override
  void dispose() {
    // 清理Controller实例
    Get.delete<HighlightsVideoLogic>(tag: _controllerTag);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            _title(context),
            SizedBox(
              height: 47.w,
            ),
            _video(context),
            SizedBox(
              height: 47.w,
            ),
            _action(context),
          ],
        ),
      ),
    );
  }

  Widget _title(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 20, left: 20, right: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                  child: Text(
                logic.arenaName,
                style: TextStyles.titleSemiBold16.copyWith(fontSize: 20.sp),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              )),
              // Flexible(
              //   child: GestureDetector(
              //     behavior: HitTestBehavior.opaque,
              //     onTap: () {
              //       final map = Get.arguments as Map;
              //       if (logic.isArenaDetail.value) {
              //         if (map.containsKey('venueRecord')) {
              //           AppPage.back(page: Routes.siteDetailPage);
              //         } else {
              //           AppPage.back(page: Routes.arenaDetailsPage);
              //         }
              //       } else {
              //         if (map.containsKey('venueItem')) {
              //           log("siteDetailPage2=${logic.arenaName}-${logic.arenaId}");
              //           if (logic.arenaName == "即刻创作" && logic.arenaId == "0") {
              //           } else {
              //             AppPage.to(Routes.siteDetailPage,
              //                 arguments: {'venueId': int.parse(logic.arenaId)});
              //           }
              //         } else {
              //           AppPage.to(Routes.arenaDetailsPage,
              //               arguments: {"id": int.parse(logic.arenaId)});
              //         }
              //       }
              //     },
              //     child: Row(mainAxisSize: MainAxisSize.min, children: [
              //       Flexible(
              //           child: Text(
              //         logic.arenaName,
              //         style:
              //             TextStyles.titleSemiBold16.copyWith(fontSize: 20.sp),
              //         overflow: TextOverflow.ellipsis,
              //         maxLines: 1,
              //       )),
              //       SizedBox(
              //         width: 10.w,
              //       ),
              //       if (!((logic.arenaName == "即刻创作" && logic.arenaId == "0") &&
              //           !(logic.isArenaDetail.value)))
              //         WxAssets.images.icHighlightsArrow
              //             .image(width: 24.w, fit: BoxFit.fill),
              //     ]),
              //   ),
              // ),

              SizedBox(
                width: 10.w,
              ),
              GestureDetector(
                  onTap: () => AppPage.back(),
                  child:
                      WxAssets.images.icClose.image(width: 20.w, height: 20.w)),
            ],
          ),
        ),
        Padding(
            padding: EdgeInsets.only(top: 20.w, left: 20),
            child: Text(
              '${logic.week} ${logic.date.replaceAll('星期', '周')} ${logic.time.value}',
              style: TextStyles.regular.copyWith(color: Colours.color5C5C6E),
            )),
      ],
    );
  }

  Widget _action(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 26),
      child: Wrap(
        spacing: 64.w,
        children: [
          if (logic.isArenaDetail.value != false)
            GestureDetector(
              onTap: () => logic.share(),
              child: Container(
                width: 46.w,
                height: 46.w,
                decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.circular(23.w),
                ),
                child: WxAssets.images.icShare.image(width: 24.w, height: 24.w),
              ),
            ),
          GestureDetector(
            onTap: () => logic.downloadAndSaveVideo(),
            child: Container(
              width: 46.w,
              height: 46.w,
              decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(23.w),
              ),
              child: WxAssets.images.icDownload.image(width: 22.w, height: 2.w),
            ),
          ),
          if (logic.type.value != 1)
            GestureDetector(
              onTap: () => logic.showDeleteDialog(),
              child: Container(
                width: 46.w,
                height: 46.w,
                decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.circular(23.w),
                ),
                child:
                    WxAssets.images.icDelete.image(width: 22.w, height: 22.w),
              ),
            ),
        ],
      ),
    );
  }

  Widget _video(BuildContext context) {
    return Expanded(
      child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
        Padding(
            padding: EdgeInsets.symmetric(horizontal: 30.w),
            child: Text(
              logic.videoName,
              style: TextStyles.regular,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            )),
        SizedBox(
          height: 20.w,
        ),
        logic.videoSize == 1
            ? Expanded(
                child: AspectRatio(
                  aspectRatio: 260 / 461, // 宽高比
                  child: VideoView(
                    controller: logic.videoController,
                  ),
                ),
              )
            : AspectRatio(
                aspectRatio: 375 / 211, // 宽高比
                child: VideoView(
                  controller: logic.videoController,
                ),
              ),
      ]),
    );
  }
}
