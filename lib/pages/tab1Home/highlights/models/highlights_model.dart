import 'package:get/get.dart';
import 'package:json_annotation/json_annotation.dart';

part 'highlights_model.g.dart';

@JsonSerializable()
class HighlightsResponse extends Object {
  @JsonKey(name: 'currentPage')
  int currentPage;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'isEnd')
  bool isEnd;

  @Json<PERSON>ey(name: 'result')
  List<HighlightsModel> result;

  @JsonKey(name: 'totalPages')
  int totalPages;

  @<PERSON>son<PERSON><PERSON>(name: 'totalRows')
  int totalRows;

  @JsonKey(name: 'totalCount')
  int totalCount;

  HighlightsResponse(this.currentPage, this.isEnd, this.result, this.totalPages,
      this.totalRows, this.totalCount);

  factory HighlightsResponse.fromJson(Map<String, dynamic> srcJson) =>
      _$HighlightsResponseFromJson(srcJson);

  Map<String, dynamic> toJson() => _$HighlightsResponseToJson(this);
}

@JsonSerializable()
class HighlightsModel extends Object {
  @J<PERSON><PERSON><PERSON>(name: 'arenaId')
  String arenaId;

  @JsonK<PERSON>(name: 'arenaName')
  String arenaName;

  @JsonKey(name: 'date')
  String date;

  @JsonKey(name: 'videos')
  List<Videos> videos;

  @JsonKey(name: 'week')
  String week;

  HighlightsModel(
    this.arenaId,
    this.arenaName,
    this.date,
    this.videos,
    this.week,
  );

  factory HighlightsModel.fromJson(Map<String, dynamic> srcJson) =>
      _$HighlightsModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$HighlightsModelToJson(this);
}

@JsonSerializable()
class Videos extends Object {
  @JsonKey(name: 'compressedVideoPath')
  String compressedVideoPath;

  @JsonKey(name: 'id')
  String id;

  @JsonKey(name: 'name')
  String name;

  @JsonKey(name: 'no')
  int no;

  @JsonKey(name: 'queueSize')
  int queueSize;

  @JsonKey(name: 'status')
  int status;

  @JsonKey(name: 'videoCover')
  String videoCover;

  @JsonKey(name: 'videoPath')
  String videoPath;

  @JsonKey(name: 'videoSize')
  int videoSize; //0 常规； 1 竖屏； 2 宽屏

  @JsonKey(name: 'new')
  bool isNew;

  @JsonKey(includeFromJson: false, includeToJson: false)
  RxBool rxNew = false.obs;

  Videos(this.compressedVideoPath, this.id, this.name, this.no, this.queueSize,
      this.status, this.videoCover, this.videoPath, this.videoSize, this.isNew);

  factory Videos.fromJson(Map<String, dynamic> srcJson) {
    final video = _$VideosFromJson(srcJson);
    video.rxNew.value = video.isNew;
    return video;
  }

  Map<String, dynamic> toJson() => _$VideosToJson(this);

  bool get isFinish => status == 2;
}
