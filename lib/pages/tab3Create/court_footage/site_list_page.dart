import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab3Create/court_footage/site_list_logic.dart';
import 'package:shoot_z/pages/tab3Create/court_footage/venue_list_item.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/location_utils.dart';
import 'package:shoot_z/widgets/text_with_icon.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:shoot_z/widgets/filter_type_bottom_sheet.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:shoot_z/gen/assets.gen.dart';

class SiteListPage extends StatelessWidget {
  final bool showCreateBtn;
  final bool isAICreation;
  final bool isCreateMatch;
  SiteListPage(
      {super.key,
      this.showCreateBtn = true,
      this.isAICreation = false,
      this.isCreateMatch = false});

  final logic = Get.put(SiteListLogic());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(
        () => Stack(
          children: [
            Column(
              children: [
                SizedBox(
                  height: 2.w,
                ),
                // 搜索框
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 15.w),
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  height: 44.w,
                  alignment: Alignment.centerLeft,
                  decoration: BoxDecoration(
                    color: Colours.color191921,
                    borderRadius: BorderRadius.circular(22.r),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      WxAssets.images.icSearch.image(),
                      SizedBox(
                        width: 10.w,
                      ),
                      Expanded(
                          child: TextField(
                        onTap: () => AppPage.to(Routes.searchSitePage),
                        controller: logic.searchController,
                        keyboardType: TextInputType.text,
                        readOnly: true,
                        // onChanged: logic.onTextChanged,
                        decoration: InputDecoration(
                          // isDense: true,//isDense 为 true 会让 TextField 的高度变紧凑，同时调整光标和文本的位置。
                          contentPadding:
                              EdgeInsets.only(top: 0.w, bottom: 3.w),
                          border: InputBorder.none,
                          hintText: "请输入要查找的球场名称",
                          hintStyle: TextStyles.display14
                              .copyWith(color: Colours.color5C5C6E),
                        ),
                      )),
                      GestureDetector(
                          onTap: () {
                            logic.searchController.text = '';
                            logic.searchText.value = '';
                          },
                          child: Obx(() => Visibility(
                              visible: logic.searchText.value.isNotEmpty,
                              child: WxAssets.images.icSearchDelete.image()))),
                    ],
                  ),
                ),
                SizedBox(
                  height: 20.w,
                ),
                logic.init.value
                    ? Expanded(
                        child: NotificationListener(
                            onNotification: (ScrollNotification note) {
                              if (note.metrics.pixels ==
                                  note.metrics.maxScrollExtent) {
                                logic.loadMore();
                              }
                              return true;
                            },
                            child: RefreshIndicator(
                              onRefresh: logic.onRefresh,
                              child: Builder(builder: (context) {
                                return Obx(
                                  () => Padding(
                                    padding: EdgeInsets.only(bottom: 20.w),
                                    child: CustomScrollView(
                                      slivers: [
                                        if (logic.myArenaList.isNotEmpty)
                                          SliverToBoxAdapter(
                                            child: Container(
                                              alignment: Alignment.centerLeft,
                                              child: const TextWithIcon(
                                                  title: '近期创作'),
                                            ).marginOnly(
                                                left: 15.w, bottom: 15.w),
                                          ),
                                        LocationUtils
                                                .instance.havePermission.value
                                            ? _myArenalist()
                                            : _location(),
                                        // if (logic.allArenaList.isNotEmpty)
                                        SliverPersistentHeader(
                                            pinned:
                                                true, // 设置为 true 可以让它在滚动时固定在顶部
                                            delegate: _StickyHeaderDelegate(
                                              minHeight: 50.w, // 最小高度
                                              maxHeight: 50.w, // 最大高度
                                              child: Container(
                                                color: Colours.color0F0F16,
                                                child: SingleChildScrollView(
                                                        scrollDirection:
                                                            Axis.horizontal,
                                                        child: Row(
                                                          children: [
                                                            _createFilterBtn(
                                                                logic.filterItem1[
                                                                        'title']
                                                                    as String,
                                                                () {
                                                              FilterTypeBottomSheet
                                                                  .show(
                                                                context,
                                                                filterList: logic
                                                                    .filterList1,
                                                                onItemSelected:
                                                                    (index) {
                                                                  // 处理选择结果
                                                                  logic
                                                                      .filterItem1
                                                                      .value = logic
                                                                          .filterList1[
                                                                      index];
                                                                  logic
                                                                      .filterVenueList();
                                                                },
                                                              );
                                                            }),
                                                            SizedBox(
                                                              width: 10.w,
                                                            ),
                                                            _createFilterBtn(
                                                                logic.filterItem2[
                                                                        'title']
                                                                    as String,
                                                                () {
                                                              FilterTypeBottomSheet
                                                                  .show(
                                                                context,
                                                                filterList: logic
                                                                    .filterList2,
                                                                onItemSelected:
                                                                    (index) {
                                                                  // 处理选择结果
                                                                  logic
                                                                      .filterItem2
                                                                      .value = logic
                                                                          .filterList2[
                                                                      index];
                                                                  logic
                                                                      .filterVenueList();
                                                                },
                                                              );
                                                            }),
                                                            SizedBox(
                                                              width: 10.w,
                                                            ),
                                                            _createFilterBtn(
                                                                logic.filterItem3[
                                                                        'title']
                                                                    as String,
                                                                () {
                                                              FilterTypeBottomSheet
                                                                  .show(
                                                                context,
                                                                filterList: logic
                                                                    .filterList3,
                                                                onItemSelected:
                                                                    (index) {
                                                                  // 处理选择结果
                                                                  logic
                                                                      .filterItem3
                                                                      .value = logic
                                                                          .filterList3[
                                                                      index];
                                                                  logic
                                                                      .filterVenueList();
                                                                },
                                                              );
                                                            }),
                                                            SizedBox(
                                                              width: 10.w,
                                                            ),
                                                            _createFilterBtn(
                                                                logic.filterItem4[
                                                                        'title']
                                                                    as String,
                                                                () {
                                                              FilterTypeBottomSheet
                                                                  .show(
                                                                context,
                                                                filterList: logic
                                                                    .filterList4,
                                                                onItemSelected:
                                                                    (index) {
                                                                  // 处理选择结果
                                                                  logic
                                                                      .filterItem4
                                                                      .value = logic
                                                                          .filterList4[
                                                                      index];
                                                                  logic
                                                                      .filterVenueList();
                                                                },
                                                              );
                                                            }),
                                                          ],
                                                        ))
                                                    .marginOnly(
                                                        left: 15.w,
                                                        bottom: 15.w,
                                                        right: 15.w),
                                              ),
                                            )),
                                        _allArenalist(),
                                        SliverPadding(
                                          padding:
                                              EdgeInsets.only(bottom: 60.w),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              }),
                            )),
                      )
                    : buildLoad(),
              ],
            ),
            if (showCreateBtn)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: SafeArea(
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 8.w),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          padding: EdgeInsets.only(bottom: 2.w),
                          width: 90.w,
                          height: 27.w,
                          decoration: BoxDecoration(
                              image: DecorationImage(
                                  image: WxAssets.images.pointBgArrowDown
                                      .provider(),
                                  fit: BoxFit.fill)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            // crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              WxAssets.images.points2
                                  .image(width: 16.w, height: 16.w),
                              SizedBox(
                                width: 3.w,
                              ),
                              Text(
                                '+${logic.pointStr}积分',
                                style: TextStyles.display12
                                    .copyWith(color: Colours.color7732ED),
                              )
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 8.w,
                        ),
                        Container(
                          width: double.infinity,
                          height: 50.w,
                          alignment: Alignment.center,
                          margin: EdgeInsets.symmetric(horizontal: 15.w),
                          decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [
                                  Colours.color7732ED,
                                  Colours.colorA555EF
                                ],
                                begin: Alignment.bottomLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(25.r)),
                          child: InkWell(
                            onTap: () => AppPage.to(Routes.createArenaPage),
                            borderRadius: BorderRadius.circular(25.r),
                            child: Container(
                              width: double.infinity,
                              height: double.infinity,
                              alignment: Alignment.center,
                              child: Text(
                                '创建场地',
                                style: TextStyles.semiBold14,
                              ),
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              )
          ],
        ),
      ),
    );
  }

  Widget _createFilterBtn(String title, VoidCallback onClick) {
    return InkWell(
      onTap: onClick,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 14.w),
        alignment: Alignment.center,
        height: 28.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(14.r),
          border: Border.all(color: Colours.colorA8A8BC, width: 1.w),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              title,
              style: TextStyles.display12.copyWith(color: Colours.white),
            ),
            SizedBox(
              width: 6.w,
            ),
            WxAssets.images.arrowDownLinear.image()
          ],
        ),
      ),
    );
  }

  SliverToBoxAdapter _location() {
    return SliverToBoxAdapter(
      child: Column(
        children: [
          SizedBox(
            height: 71.w,
          ),
          WxAssets.images.icHomeLocationHint
              .image(width: 104.w, fit: BoxFit.fill),
          SizedBox(
            height: 30.w,
          ),
          Text(
            S.current.location_tips,
            style: TextStyles.regular.copyWith(color: Colours.color5C5C6E),
          ),
          SizedBox(
            height: 30.w,
          ),
          WxButton(
            width: 125.w,
            height: 40.w,
            borderRadius: BorderRadius.circular(20.w),
            backgroundColor: Colours.color22222D,
            text: S.current.open_now,
            textStyle: TextStyles.regular,
            onPressed: logic.openSettings,
          ),
        ],
      ),
    );
  }

  SliverList _myArenalist() {
    return SliverList(
        delegate: SliverChildBuilderDelegate(
      (BuildContext context, int index) {
        return VenueListItem(
          isAICreation: isAICreation,
          isCreateMatch: isCreateMatch,
          model: logic.myArenaList[index],
        );
      },
      childCount: logic.myArenaList.isNotEmpty ? logic.myArenaList.length : 0,
    ));
  }

  SliverList _allArenalist() {
    return SliverList(
        delegate: SliverChildBuilderDelegate(
      (BuildContext context, int index) {
        return logic.allArenaList.isEmpty
            ? _buildEmptyView(context)
            : VenueListItem(
                isAICreation: isAICreation,
                isCreateMatch: isCreateMatch,
                model: logic.allArenaList[index],
              );
      },
      childCount: logic.allArenaList.isNotEmpty ? logic.allArenaList.length : 1,
    ));
  }

  Widget _buildEmptyView(BuildContext context) {
    return SizedBox(
      height: 300.w,
      child: myNoDataView(context,
          msg: '暂无数据', imagewidget: WxAssets.images.battleEmptyIcon.image()),
    );
  }

  Widget allPlace() {
    return Padding(
        padding: const EdgeInsets.only(bottom: 30, top: 5),
        child: Center(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            height: 30,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              border: Border.all(color: Colours.color2F2F3B, width: 1),
            ),
            child: GestureDetector(
              onTap: () => logic.toAll(),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  WxAssets.images.icMoreAll.image(),
                  const SizedBox(
                    width: 4,
                  ),
                  Text(
                    S.current.all_arenas,
                    style: TextStyles.display12.copyWith(color: Colors.white),
                  )
                ],
              ),
            ),
          ),
        ));
  }
}

class _StickyHeaderDelegate extends SliverPersistentHeaderDelegate {
  final double minHeight;
  final double maxHeight;
  final Widget child;

  _StickyHeaderDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  double get maxExtent => maxHeight;

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(_StickyHeaderDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
}
