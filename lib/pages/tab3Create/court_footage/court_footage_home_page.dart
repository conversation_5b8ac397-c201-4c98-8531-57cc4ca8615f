import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab3Create/court_footage/court_footage_home_logic.dart';
import 'package:shoot_z/pages/tab3Create/court_footage/site_list_page.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:ui_packages/ui_packages.dart';

//球场剪辑
class CourtFootageHomePage extends StatelessWidget {
  CourtFootageHomePage({super.key});
  final logic = Get.put(CourtFootageHomeLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.Site_selection),
        actions: [
          InkWell(
            onTap: () => AppPage.to(Routes.myArenaPage, arguments: {
              "isAICreation": logic.isCreateMatch ? false : true,
              "isCreateMatch": logic.isCreateMatch
            }),
            child: Column(
              children: [
                SizedBox(
                  height: 8.w,
                ),
                WxAssets.images.mySiteIcon.image(),
                SizedBox(
                  height: 8.w,
                ),
                Text(
                  S.current.my_site,
                  style: TextStyles.display10,
                )
              ],
            ),
          ),
          SizedBox(
            width: 15.w,
          ),
          InkWell(
            child: Column(
              children: [
                SizedBox(
                  height: 8.w,
                ),
                WxAssets.images.stadiumMap.image(),
                SizedBox(
                  height: 8.w,
                ),
                Text(
                  S.current.stadium_map,
                  style: TextStyles.display10,
                )
              ],
            ),
            onTap: () {
              AppPage.to(Routes.SiteMapPage, arguments: {
                'onlyShowVenue': true,
                'isCreateMatch': logic.isCreateMatch
              });
            },
          ),
          SizedBox(
            width: 15.w,
          ),
        ],
      ),
      body: Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
        SizedBox(
          height: 8.w,
        ),
        Expanded(
          child: SiteListPage(
              showCreateBtn: false,
              isAICreation: !logic.isCreateMatch,
              isCreateMatch: logic.isCreateMatch),
        ),
      ]),
    );
  }
}
