import 'package:flutter/material.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:ui_packages/ui_packages.dart';

class SelectSitePage extends StatelessWidget {
  const SelectSitePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            child: WxAssets.images.pageTopBg.image(
                width: ScreenUtil().screenWidth,
                height: 260.w,
                fit: BoxFit.fitWidth),
          ),
          Column(
            children: [
              _topBar(context),
              SizedBox(
                height: 159.w,
              ),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  AppPage.to(Routes.courtFootageHomePage);
                },
                child: Container(
                  decoration: BoxDecoration(
                      image: DecorationImage(
                          image: WxAssets.images.aiBg.provider(),
                          fit: BoxFit.fill)),
                  width: double.infinity,
                  margin: EdgeInsets.symmetric(horizontal: 15.w),
                  padding:
                      EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.w),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ShaderMask(
                              shaderCallback: (bounds) => const LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: [
                                  Colours.colorFFECC1,
                                  Colours.colorE7CEFF,
                                  Colours.colorD1EAFF,
                                ],
                              ).createShader(bounds),
                              child: Text(
                                S.current.Site_selection,
                                style: TextStyles.display12.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16.sp),
                              ),
                            ),
                            SizedBox(
                              height: 10.w,
                            ),
                            Text(
                              '选择已创建的场地进行拍摄',
                              style: TextStyles.display12.copyWith(
                                  color: Colours.colorA8A8BC, fontSize: 12.sp),
                            ),
                            SizedBox(
                              height: 15.w,
                            ),
                            WxButton(
                              text: '开始',
                              textStyle:
                                  TextStyles.semiBold.copyWith(fontSize: 12.sp),
                              linearGradient: GradientUtils.mainGradient,
                              height: 32.w,
                              width: 100.w,
                              borderRadius: BorderRadius.circular(16.w),
                              onPressed: () {
                                AppPage.to(Routes.courtFootageHomePage);
                              },
                            ),
                          ],
                        ),
                      ),
                      WxAssets.images.ai2.image(width: 78.w, height: 60.w),
                    ],
                  ),
                ),
              ),
              SizedBox(
                height: 15.w,
              ),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  AppPage.to(Routes.selfieShotPage);
                },
                child: Container(
                  decoration: BoxDecoration(
                      image: DecorationImage(
                          image: WxAssets.images.aiBg.provider(),
                          fit: BoxFit.fill)),
                  width: double.infinity,
                  margin: EdgeInsets.symmetric(horizontal: 15.w),
                  padding:
                      EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.w),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ShaderMask(
                              shaderCallback: (bounds) => const LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: [
                                  Colours.colorFFECC1,
                                  Colours.colorE7CEFF,
                                  Colours.colorD1EAFF,
                                ],
                              ).createShader(bounds),
                              child: Text(
                                '即刻创作',
                                style: TextStyles.display12.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16.sp),
                              ),
                            ),
                            SizedBox(
                              height: 10.w,
                            ),
                            Text(
                              '不选择场地直接开始创作',
                              style: TextStyles.display12.copyWith(
                                  color: Colours.colorA8A8BC, fontSize: 12.sp),
                            ),
                            SizedBox(
                              height: 15.w,
                            ),
                            WxButton(
                              text: '开始',
                              textStyle:
                                  TextStyles.semiBold.copyWith(fontSize: 12.sp),
                              linearGradient: GradientUtils.mainGradient,
                              height: 32.w,
                              width: 100.w,
                              borderRadius: BorderRadius.circular(16.w),
                              onPressed: () {
                                AppPage.to(Routes.selfieShotPage);
                              },
                            ),
                          ],
                        ),
                      ),
                      WxAssets.images.createImmediatelyIcon
                          .image(width: 78.w, height: 60.w),
                    ],
                  ),
                ),
              ),

              // Positioned(
              //   top: 128.w,
              //   left: 43.w,
              //   child: Container(
              //     width: 90.w,
              //     height: 27.w,
              //     padding: EdgeInsets.only(top: 3.w),
              //     decoration: BoxDecoration(
              //         image: DecorationImage(
              //             image: WxAssets.images.pointBgArrowUp.provider(),
              //             fit: BoxFit.fill)),
              //     child: Row(
              //       mainAxisAlignment: MainAxisAlignment.center,
              //       // crossAxisAlignment: CrossAxisAlignment.center,
              //       children: [
              //         WxAssets.images.points2
              //             .image(width: 16.w, height: 16.w),
              //         SizedBox(
              //           width: 3.w,
              //         ),
              //         Text(
              //           '+50积分',
              //           style: TextStyles.display12
              //               .copyWith(color: Colours.color7732ED),
              //         )
              //       ],
              //     ),
              //   ),
              // )
            ],
          )
        ],
      ),
      bottomNavigationBar: SizedBox(
          height: 87.w + ScreenUtil().bottomBarHeight,
          child: Column(
            children: [
              FutureBuilder<String>(
                future: getPointStr(),
                builder: (context, snapshot) {
                  if (snapshot.hasData) {
                    return Container(
                      padding: EdgeInsets.only(bottom: 2.w),
                      width: 90.w,
                      height: 27.w,
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          image: WxAssets.images.pointBgArrowDown.provider(),
                          fit: BoxFit.fill,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          WxAssets.images.points2
                              .image(width: 16.w, height: 16.w),
                          SizedBox(width: 3.w),
                          Text(
                            '+${snapshot.data!}积分',
                            style: TextStyles.display12.copyWith(
                              color: Colours.color7732ED,
                            ),
                          ),
                        ],
                      ),
                    );
                  } else {
                    return Container(); // 或者加载指示器
                  }
                },
              ),
              SizedBox(
                height: 8.w,
              ),
              InkWell(
                onTap: () {
                  AppPage.to(Routes.createArenaPage,
                      arguments: {"from": "AICreation"});
                },
                child: Container(
                  width: double.infinity,
                  height: 50.w,
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(
                      left: 15.w,
                      right: 15.w,
                      bottom: ScreenUtil().bottomBarHeight),
                  decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(25.r)),
                  child: Text(
                    '创建场地',
                    style: TextStyles.semiBold14,
                  ),
                ),
              )
            ],
          )),
    );
  }

  Widget _topBar(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(top: 4.w),
      margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 60.w,
            padding: EdgeInsets.only(
              left: 8.w,
              right: 0.w,
            ),
            child: IconButton(
              icon: Icon(
                Icons.arrow_back_ios,
                size: 20.w,
                color: Colors.white,
              ),
              onPressed: () {
                AppPage.back();
              },
            ),
          ),
          Text(
            S.current.Site_selection,
            style: TextStyles.titleSemiBold16,
          ),
          Container(
            width: 60.w,
            padding: EdgeInsets.only(left: 0.w, right: 8.w),
          ),
        ],
      ),
    );
  }

  Future<String> getPointStr() async {
    return await WxStorage.instance.getString("createSitePoint") ?? "0";
  }
}
