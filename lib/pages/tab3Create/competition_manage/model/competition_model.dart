import 'package:intl/intl.dart';

class CompetitionModelLocation {
/*
{
  "address": "湖南省长沙市岳麓区杜鹃路靠近小时代",
  "lat": 28.2287,
  "lng": 112.9059,
  "cityId": 0
} 
*/

  String? address;
  double? lat;
  double? lng;
  String? cityId;

  CompetitionModelLocation({
    this.address,
    this.lat,
    this.lng,
    this.cityId,
  });
  CompetitionModelLocation.fromJson(Map<String, dynamic> json) {
    address = json['address']?.toString();
    lat = json['lat']?.toDouble();
    lng = json['lng']?.toDouble();
    cityId = json['cityId']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['address'] = address;
    data['lat'] = lat;
    data['lng'] = lng;
    data['cityId'] = cityId;
    return data;
  }
}

class CompetitionModelHalf {
/*
{
  "halfId": 0,
  "id": 0,
  "name": "string"
} 
*/

  int? halfId;
  int? id;
  String? name;

  CompetitionModelHalf({
    this.halfId,
    this.id,
    this.name,
  });
  CompetitionModelHalf.fromJson(Map<String, dynamic> json) {
    halfId = json['halfId']?.toInt();
    id = json['id']?.toInt();
    name = json['name']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['halfId'] = halfId;
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class CompetitionModel {
/*
{
  "half": [
    {
      "halfId": 0,
      "id": 0,
      "name": "string"
    }
  ],
  "id": "0",
  "leftTeamColor": "string",
  "leftTeamId": "0",
  "leftTeamLogo": "string",
  "leftTeamName": "string",
  "leftTeamScore": 0,
  "location": {
    "address": "string",
    "cityId": "string",
    "lat": 0,
    "lng": 0
  },
  "matchDate": "string",
  "matchReport": true,
  "matchTime": "string",
  "rightTeamColor": "string",
  "rightTeamId": "0",
  "rightTeamLogo": "string",
  "rightTeamName": "string",
  "rightTeamScore": 0,
  "status": 0,
  "venueId": 0,
  "venueLatitude": "string",
  "venueLongitude": "string",
  "venueName": "string",
  "week": "string"
} 
*/

  List<CompetitionModelHalf?>? half;
  String? id;
  String? leftTeamColor;
  String? leftTeamId;
  String? leftTeamLogo;
  String? leftTeamName;
  int? leftTeamScore;
  CompetitionModelLocation? location;
  String? matchDate;
  String? matchDateStr;
  bool? matchReport;
  String? matchTime;
  String? matchTimeStr;
  String? rightTeamColor;
  String? rightTeamId;
  String? rightTeamLogo;
  String? rightTeamName;
  int? rightTeamScore;
  int? status;
  int? markStatus;
  int? venueId;
  String? venueLatitude;
  String? venueLongitude;
  String? venueName;
  String? week;

  CompetitionModel({
    this.half,
    this.id,
    this.leftTeamColor,
    this.leftTeamId,
    this.leftTeamLogo,
    this.leftTeamName,
    this.leftTeamScore,
    this.location,
    this.matchDate,
    this.matchDateStr,
    this.matchReport,
    this.matchTime,
    this.matchTimeStr,
    this.rightTeamColor,
    this.rightTeamId,
    this.rightTeamLogo,
    this.rightTeamName,
    this.rightTeamScore,
    this.status,
    this.markStatus,
    this.venueId,
    this.venueLatitude,
    this.venueLongitude,
    this.venueName,
    this.week,
  });
  CompetitionModel.fromJson(Map<String, dynamic> json) {
    if (json['half'] != null) {
      final v = json['half'];
      final arr0 = <CompetitionModelHalf>[];
      v.forEach((v) {
        arr0.add(CompetitionModelHalf.fromJson(v));
      });
      half = arr0;
    }
    id = json['id']?.toString();
    leftTeamColor = json['leftTeamColor']?.toString();
    leftTeamId = json['leftTeamId']?.toString();
    leftTeamLogo = json['leftTeamLogo']?.toString();
    leftTeamName = json['leftTeamName']?.toString();
    leftTeamScore = json['leftTeamScore']?.toInt();
    location = (json['location'] != null)
        ? CompetitionModelLocation.fromJson(json['location'])
        : null;

    matchReport = json['matchReport'];
    matchTime = json['matchTime']?.toString();
    matchDate = json['matchDate']?.toString();
    DateTime parsedStartDate = DateTime.parse(matchDate ?? '2024-01-01');
    matchDateStr = DateFormat("MM.dd").format(parsedStartDate);
    matchTimeStr = (matchTime ?? "09:00:00").split(':').take(2).join(':');
    rightTeamColor = json['rightTeamColor']?.toString();
    rightTeamId = json['rightTeamId']?.toString();
    rightTeamLogo = json['rightTeamLogo']?.toString();
    rightTeamName = json['rightTeamName']?.toString();
    rightTeamScore = json['rightTeamScore']?.toInt();
    status = json['status']?.toInt();
    markStatus = json['markStatus']?.toInt();
    venueId = json['venueId']?.toInt();
    venueLatitude = json['venueLatitude']?.toString();
    venueLongitude = json['venueLongitude']?.toString();
    venueName = json['venueName']?.toString();
    week = json['week']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (half != null) {
      final v = half;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['half'] = arr0;
    }
    data['id'] = id;
    data['leftTeamColor'] = leftTeamColor;
    data['leftTeamId'] = leftTeamId;
    data['leftTeamLogo'] = leftTeamLogo;
    data['leftTeamName'] = leftTeamName;
    data['leftTeamScore'] = leftTeamScore;
    if (location != null) {
      data['location'] = location!.toJson();
    }
    data['matchDate'] = matchDate;
    data['matchReport'] = matchReport;
    data['matchTime'] = matchTime;
    data['rightTeamColor'] = rightTeamColor;
    data['rightTeamId'] = rightTeamId;
    data['rightTeamLogo'] = rightTeamLogo;
    data['rightTeamName'] = rightTeamName;
    data['rightTeamScore'] = rightTeamScore;
    data['status'] = status;
    data['markStatus'] = markStatus;
    data['venueId'] = venueId;
    data['venueLatitude'] = venueLatitude;
    data['venueLongitude'] = venueLongitude;
    data['venueName'] = venueName;
    data['week'] = week;
    return data;
  }
}
