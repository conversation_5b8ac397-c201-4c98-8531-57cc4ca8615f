///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class RightsModel {
/*
{
  "session": "单场",
  "price": 59,
} 
*/

  String? session;
  int? price;

  RightsModel({
    this.session,
    this.price,
  });
  RightsModel.fromJson(Map<String, dynamic> json) {
    session = json['session']?.toString();
    price = json['price']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['session'] = session;
    data['price'] = price;
    return data;
  }
}
