///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class MatchModel {
/*
{
  "logo": "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/banner/zxf.png",
  "count": 21,
  "title": "篮球火赛事"
} 
*/

  String? logo;
  int? matchCount;
  String? name;
  String? id;
  MatchModel({
    this.logo,
    this.matchCount,
    this.name,
    this.id,
  });
  MatchModel.fromJson(Map<String, dynamic> json) {
    logo = json['logo']?.toString();
    matchCount = json['matchCount']?.toInt();
    name = json['name']?.toString();
    id = json['id']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['logo'] = logo;
    data['matchCount'] = matchCount;
    data['name'] = name;
    data['id'] = id;
    return data;
  }
}
