///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class ReportPackageModel {
/*
{
  "appleProductId": "string",
  "description": "string",
  "discountPrice": "string",
  "id": 0,
  "matchNum": 0,
  "name": "string",
  "price": "string"
} 
*/

  String? appleProductId;
  String? description;
  String? discountPrice;
  int? id;
  int? matchNum;
  String? name;
  String? price;

  ReportPackageModel({
    this.appleProductId,
    this.description,
    this.discountPrice,
    this.id,
    this.matchNum,
    this.name,
    this.price,
  });
  ReportPackageModel.fromJson(Map<String, dynamic> json) {
    appleProductId = json['appleProductId']?.toString();
    description = json['description']?.toString();
    discountPrice = json['discountPrice']?.toString();
    id = json['id']?.toInt();
    matchNum = json['matchNum']?.toInt();
    name = json['name']?.toString();
    price = json['price']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['appleProductId'] = appleProductId;
    data['description'] = description;
    data['discountPrice'] = discountPrice;
    data['id'] = id;
    data['matchNum'] = matchNum;
    data['name'] = name;
    data['price'] = price;
    return data;
  }
}
