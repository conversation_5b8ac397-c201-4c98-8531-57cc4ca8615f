import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/model/report_package_model.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/report_rights_buy_logic.dart';
import 'package:ui_packages/ui_packages.dart';

///更多比赛
class ReportRightsBuyPage extends StatelessWidget {
  ReportRightsBuyPage({super.key});

  final logic = Get.put(ReportRightsBuyLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('赛事报告权益购买'),
      ),
      body: Center(
        child: Column(
          children: [
            SizedBox(
              height: 20.w,
            ),
            Image.asset('assets/images/match_report_rights_icon.png'),
            SizedBox(
              height: 20.w,
            ),
            Container(
              margin: EdgeInsets.only(left: 36.w, right: 36.w, bottom: 20.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: logic.topTagList.map((item) {
                  return _getTopWidget(item['iconPath']!, item['iconDesc']!);
                }).toList(),
              ),
            ),
            Image.asset('assets/images/session_buy_icon.png'),
            SizedBox(
              height: 20.w,
            ),
            Obx(() => Container(
                  margin: EdgeInsets.only(left: 7.5.w, right: 7.5.w),
                  height: 110.w, // 设置固定高度
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: logic.packageList.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: EdgeInsets.symmetric(horizontal: 7.5.w),
                        child:
                            _getSessionWidget(logic.packageList[index], index),
                      );
                    },
                  ),
                ))
          ],
        ),
      ),
      bottomNavigationBar: InkWell(
        onTap: () {
          logic.buyNow();
        },
        child: Container(
          width: double.infinity,
          height: 50.w,
          alignment: Alignment.center,
          margin: EdgeInsets.only(
              left: 15.w, right: 15.w, bottom: ScreenUtil().bottomBarHeight),
          decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Colours.color7732ED, Colours.colorA555EF],
                begin: Alignment.bottomLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(25.r)),
          child: Text(
            '立即购买',
            style: TextStyles.semiBold14,
          ),
        ),
      ),
    );
  }

  Widget _getTopWidget(String iconPath, String iconDesc) {
    return Column(
      children: [
        Container(
          width: 60.w,
          height: 60.w,
          decoration: BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.all(Radius.circular(20.r))),
          child: Image.asset(iconPath),
        ),
        SizedBox(
          height: 15.w,
        ),
        Text(
          iconDesc,
          maxLines: 2,
          textAlign: TextAlign.center,
          style: TextStyles.display12.copyWith(color: Colors.white),
        )
      ],
    );
  }

  Widget _getSessionWidget(ReportPackageModel model, int index) {
    return Obx(() {
      final image = WxAssets.images.icVipSel;
      return GestureDetector(
        onTap: () {
          if (index != logic.currentSelectIndex.value) {
            logic.currentSelectIndex.value = index;
          }
        },
        child: Container(
          width: 95.w,
          height: 110.w,
          padding: EdgeInsets.only(left: 13.w, top: 13.w, bottom: 6.w),
          decoration: BoxDecoration(
              color: Colours.color191921,
              image: index == logic.currentSelectIndex.value
                  ? DecorationImage(image: image.provider(), fit: BoxFit.fill)
                  : null,
              borderRadius: index != logic.currentSelectIndex.value
                  ? BorderRadius.only(
                      topLeft: Radius.circular(8.r),
                      topRight: Radius.circular(30.r),
                      bottomLeft: Radius.circular(8.r),
                      bottomRight: Radius.circular(8.r))
                  : BorderRadius.only(topRight: Radius.circular(20.r))),
          child: Stack(
            children: [
              Positioned(
                  top: 0,
                  left: 0,
                  child: Text(
                    model.name ?? '',
                    style: TextStyles.semiBold14,
                  )),
              Positioned(
                  bottom: 0,
                  left: 0,
                  child: Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                            text: '¥',
                            style:
                                TextStyles.numberDin16.copyWith(fontSize: 14)),
                        TextSpan(
                            text: model.price.toString(),
                            style:
                                TextStyles.numberDin16.copyWith(fontSize: 26)),
                      ],
                    ),
                  ))
            ],
          ),
        ),
      );
    });
  }
}
