import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/match_team_list_logic.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/model/match_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

class MatchTeamListPage extends StatelessWidget {
  MatchTeamListPage({super.key});
  final logic = Get.put(MatchTeamListLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        return Column(
          children: [
            _topBarWidget(context),
            _topSearchWidget(context),
            Expanded(
              child: SmartRefresher(
                controller: logic.refreshController,
                footer: buildFooter(),
                header: buildClassicHeader(),
                enablePullDown: true,
                enablePullUp: true,
                onRefresh: () {
                  logic.request();
                },
                onLoading: () {
                  // WxLoading.showToast("onLoading");
                  logic.request(isLoad: true);
                },
                physics: const AlwaysScrollableScrollPhysics(),
                //   child:
                child: (logic.dataFag["isFrist"] as bool)
                    ? buildLoad()
                    : (logic.items.isEmpty
                        ? _emptyView(context)
                        : _listView(context)),
              ),
            ),
          ],
        );
      }),
      bottomNavigationBar: InkWell(
        onTap: () async {
          final result = await AppPage.to(Routes.createTeamPage);
          if (result == true) {
            // 如果结果为 true，表示需要刷新列表
            logic.request();
          }
        },
        child: Container(
          width: double.infinity,
          height: 50.w,
          alignment: Alignment.center,
          margin: EdgeInsets.only(
              left: 15.w, right: 15.w, bottom: ScreenUtil().bottomBarHeight),
          decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Colours.color7732ED, Colours.colorA555EF],
                begin: Alignment.bottomLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(25.r)),
          child: Text(
            '新建球队',
            style: TextStyles.semiBold14,
          ),
        ),
      ),
    );
  }

  Widget _listView(BuildContext context) {
    return ListView.builder(
        padding: EdgeInsets.only(top: 15.w),
        itemCount: logic.items.length,
        itemBuilder: (context, index) {
          final item = logic.items[index];
          // 列表项
          return _buildListItem(item);
        });
  }

  /// 构建列表项
  Widget _buildListItem(MatchModel item) {
    return InkWell(
      onTap: () {
        if (item.id == logic.selectedTeamId) {
          WxLoading.showToast('不能选重复的球队哦');
          return;
        }
        AppPage.back(result: item);
      },
      child: Container(
          margin: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 15.w),
          child: Container(
            height: 72.w,
            padding: const EdgeInsets.only(left: 15, right: 15),
            decoration: BoxDecoration(
              color: const Color(0xFF191921), // 设置背景颜色
              borderRadius: BorderRadius.circular(8), // 设置圆角
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      width: 32.w,
                      height: 32.w,
                      //超出部分，可裁剪
                      clipBehavior: Clip.hardEdge,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: CachedNetworkImage(
                        imageUrl: item.logo ??
                            "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/banner/zxf.png",
                        fit: BoxFit.cover,
                      ),
                    ),
                    const SizedBox(
                      width: 15,
                    ),
                    SizedBox(
                      width: 200,
                      child: Text(item.name ?? '',
                          style: TextStyles.semiBold14,
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1),
                    )
                  ],
                ),
                const Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white,
                  size: 15,
                )
              ],
            ),
          )),
    );
  }

  Widget _emptyView(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // double topDistance = (constraints.maxHeight -
        //         ScreenUtil().statusBarHeight -
        //         ScreenUtil().bottomBarHeight -
        //         250) /
        //     2;
        return SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: SizedBox(
              height: constraints.maxHeight,
              child: myNoDataView(
                context,
                msg: '暂无球队，快去创建吧',
                imagewidget:
                    WxAssets.images.icGameNo.image(width: 150.w, height: 150.w),
              ),
            ));
      },
    );
  }

  Widget _topBarWidget(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 50.w,
      alignment: Alignment.center,
      margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: 60.w,
            padding: EdgeInsets.only(left: 15.w),
            child: IconButton(
                onPressed: () {
                  AppPage.back();
                },
                icon: const Icon(
                  Icons.arrow_back_ios,
                  color: Colors.white,
                )),
          ),
          Text(
            '球队列表',
            textAlign: TextAlign.center,
            style: TextStyles.titleSemiBold16,
            overflow: TextOverflow.ellipsis,
          ),
          Container(
            width: 60.w,
            padding: EdgeInsets.only(right: 15.w),
          )
        ],
      ),
    );
  }

  Widget _topSearchWidget(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 50.w,
      alignment: Alignment.center,
      margin: const EdgeInsets.fromLTRB(15, 20, 15, 5),
      padding: const EdgeInsets.only(left: 20, right: 20),
      decoration: BoxDecoration(
        color: const Color(0xFF191921), // 设置背景颜色
        borderRadius: BorderRadius.circular(25), // 设置圆角
      ),
      child: Row(
        children: [
          Image.asset('assets/images/ic_search.png'),
          const SizedBox(
            width: 12,
          ),
          Expanded(
              child: TextField(
            controller: logic.txtController1,
            style: TextStyles.regular,
            onSubmitted: (value) {
              logic.searchKey = value;
              logic.request();
            },
            decoration: InputDecoration(
              hintText: '请输入球队名称',
              hintStyle:
                  TextStyles.regular.copyWith(color: Colours.color5C5C6E),
              contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
              //让文字垂直居中,
              border: InputBorder.none,
            ),
            keyboardType: TextInputType.name,
          ))
        ],
      ),
    );
  }
}
