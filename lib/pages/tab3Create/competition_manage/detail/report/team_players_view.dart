import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/game/report/models/team_players_model.dart';
import 'package:shoot_z/pages/game/report/player_score_view.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';
import 'logic.dart';

class PTZTeamPlayersView extends StatelessWidget {
  PTZTeamPlayersView({super.key});

  final logic = Get.find<MatchTeamReportLogic>();
  final state = Get.find<MatchTeamReportLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => state.teamPlayers.value != null
          ? Column(
              children: [
                Container(
                  height: 32.w,
                  margin: EdgeInsets.only(top: 10.w, bottom: 12.w),
                  padding: EdgeInsets.only(left: 24.w, right: 29.w),
                  decoration: BoxDecoration(
                    color: Colours.color15151D,
                    borderRadius: BorderRadius.circular(8.w),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '球员',
                        style: TextStyles.regular.copyWith(
                            fontSize: 12.sp, color: Colours.color9393A5),
                      ),
                      Text(
                        '得分',
                        style: TextStyles.regular.copyWith(
                            fontSize: 12.sp, color: Colours.color9393A5),
                      ),
                      Text(
                        '篮板',
                        style: TextStyles.regular.copyWith(
                            fontSize: 12.sp, color: Colours.color9393A5),
                      ),
                      Text(
                        '助攻',
                        style: TextStyles.regular.copyWith(
                            fontSize: 12.sp, color: Colours.color9393A5),
                      ),
                      Text(
                        '命中率',
                        style: TextStyles.regular.copyWith(
                            fontSize: 12.sp, color: Colours.color9393A5),
                      ),
                    ],
                  ),
                ),
                Expanded(
                    child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: state.teamPlayers.value!.players.length,
                        itemBuilder: (context, index) {
                          return _listItem(context, index);
                        })),
              ],
            ).paddingSymmetric(horizontal: 15.w)
          : buildLoad(),
    );
  }

  Widget _listItem(BuildContext context, int index) {
    final model = state.teamPlayers.value!.players[index];
    return Column(
      children: [
        GestureDetector(
          onTap: () => logic.unlockPlayer(model.playerId, model.locking),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Stack(
                children: [
                  ClipRRect(
                      borderRadius: BorderRadius.circular(4.w),
                      child: Stack(children: [
                        CachedNetworkImage(
                          imageUrl: model.photo,
                          width: 60.w,
                          height: 87.w,
                          fit: BoxFit.cover,
                        ),
                        Visibility(
                            visible: model.locking,
                            child: Positioned.fill(
                                child: Container(
                              color: Colors.black.withOpacity(0.5),
                              alignment: Alignment.center,
                              child: WxAssets.images.icGameLock
                                  .image(width: 18.w, fit: BoxFit.fill),
                            ))),
                      ])),
                  Positioned(
                      left: 0,
                      top: 0,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            vertical: 2.w, horizontal: 4.w),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.only(
                            bottomRight: Radius.circular(4.w),
                          ),
                        ),
                        child: Text(
                          model.number,
                          style: TextStyles.din
                              .copyWith(color: Colours.white, fontSize: 12.sp),
                        ),
                      )),
                ],
              ),
              SizedBox(
                width: 6.w,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // SizedBox(
                    //   height: 6.w,
                    // ),
                    Row(
                      children: [
                        if (model.mvp)
                          WxAssets.images.MVPIcon.image(width: 45.w),
                        // Container(
                        //   width: 22.w,
                        //   height: 22.w,
                        //   alignment: Alignment.center,
                        //   decoration: BoxDecoration(
                        //       image: DecorationImage(
                        //           image: WxAssets.images.icTeamMvpQy.provider(),
                        //           fit: BoxFit.fill)),
                        //   child: Text(model.number,
                        //       style: GoogleFonts.oswald(
                        //           fontSize: 10.sp,
                        //           color: Colours.white,
                        //           fontWeight: AppFontWeight.semiBold())),
                        // ),
                        // if (model.playerName?.isNotEmpty ?? false)
                        //   Text(
                        //     model.playerName ?? '',
                        //     style: TextStyles.regular.copyWith(
                        //         fontSize: 12.sp, color: Colours.color5C5C6E),
                        //   ).marginOnly(left: 5.w),
                        SizedBox(
                          width: 3.w,
                        ),
                        Wrap(
                          spacing: 6.w,
                          runSpacing: 6.w,
                          children: _getTagListWithModel(model)
                              .map((e) => Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 8.w, vertical: 5.w),
                                    decoration: BoxDecoration(
                                      color: Colours.color2E1575,
                                      border: Border.all(
                                        width: 1,
                                        color: Colours.color6435E9,
                                      ),
                                      borderRadius: BorderRadius.circular(10.r),
                                    ),
                                    child: Text(
                                      textAlign: TextAlign.center,
                                      e,
                                      style: TextStyles.medium.copyWith(
                                          fontSize: 10.sp,
                                          color: Colours.white),
                                    ),
                                  ))
                              .toList(),
                        ),
                        // if (model.scoreKing)
                        //   WxAssets.images.icTeamDfw
                        //       .image(width: 18.w, fit: BoxFit.fill)
                        //       .marginOnly(right: 5.w),
                        // if (model.reboundKing)
                        //   WxAssets.images.icTeamLbw
                        //       .image(width: 18.w, fit: BoxFit.fill)
                        //       .marginOnly(right: 5.w),
                        // if (model.assistKing)
                        //   WxAssets.images.icTeamZgw
                        //       .image(width: 18.w, fit: BoxFit.fill)
                        //       .marginOnly(right: 5.w),
                        // if (model.threePointKing)
                        //   WxAssets.images.icTeamSfw
                        //       .image(width: 18.w, fit: BoxFit.fill)
                        //       .marginOnly(right: 5.w),
                        // if (model.freeThrowKing)
                        //   WxAssets.images.icTeamFqw
                        //       .image(width: 18.w, fit: BoxFit.fill)
                        //       .marginOnly(right: 5.w),
                        const Spacer(),
                        Row(
                          children: [
                            Text(
                              '比赛报告',
                              style: TextStyles.regular.copyWith(
                                  fontSize: 12.sp, color: Colours.color5C5C6E),
                            ),
                            WxAssets.images.icArrowRight.image(
                                width: 14.w,
                                color: Colours.color9393A5,
                                fit: BoxFit.fill),
                          ],
                        ),
                        // SizedBox(
                        //   width: 8.w,
                        // ),
                      ],
                    ),
                    Container(
                      height: 20.w,
                      margin: EdgeInsets.only(top: 10.w, bottom: 5.w),
                      padding: EdgeInsets.symmetric(horizontal: 4.w),
                      decoration: BoxDecoration(
                          color: Colours.color2E1575,
                          borderRadius: BorderRadius.all(Radius.circular(10.w)),
                          border: Border.all(
                            width: 1,
                            color: Colours.color6435E9,
                          )),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          WxAssets.images.contributeIcon
                              .image(width: 12.w, height: 12.w),
                          SizedBox(
                            width: 3.w,
                          ),
                          SizedBox(
                              // width: 50.w,
                              child: Text(
                            '贡献值${model.contributionValue}',
                            style: TextStyles.medium.copyWith(
                                color: Colours.white, fontSize: 10.sp),
                          ))
                        ],
                      ),
                    ),
                    Row(
                      children: [
                        Expanded(
                            child: PlayerScoreView(
                                    locking: model.locking,
                                    text: model.score.toString(),
                                    percentage: model.scoreRate,
                                    color: Colours.color474CA4)
                                .marginOnly(right: 10.w)),
                        Expanded(
                            child: PlayerScoreView(
                                    locking: model.locking,
                                    text: model.reboundCount.toString(),
                                    percentage: model.reboundRate,
                                    color: Colours.color513663)
                                .marginOnly(right: 10.w)),
                        Expanded(
                            child: PlayerScoreView(
                                    locking: model.locking,
                                    text: model.assistCount.toString(),
                                    percentage: model.assistRate,
                                    color: Colours.color505583)
                                .marginOnly(right: 10.w)),
                        Expanded(
                            child: PlayerScoreView(
                          locking: model.locking,
                          text: Utils.formatToPercentage(
                              double.parse(model.rate) / 100),
                          percentage: model.rate,
                          color: Colours.color432B8A,
                          showPercentageText: false,
                        ).marginOnly(right: 10.w)),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ).marginOnly(top: 5.w, left: 5.w),
          //   Visibility(
          //       visible: model.mvp,
          //       child: Positioned(
          //         left: 2.w,
          //         top: 2.w,
          //         child: WxAssets.images.icTeamMvp2
          //             .image(width: 25.w, fit: BoxFit.fill),
          //       )),
          // ]),
        ),
        SizedBox(
          height: 10.w,
        ),
        buildDashedLine(
          thickness: 1,
          color: Colours.color231F2E,
          dashWidth: 5,
          dashSpace: 3,
          totalWidth: ScreenUtil().screenWidth - 35.w,
        ),
        SizedBox(
          height: 6.w,
        ),
      ],
    );
  }

  Widget buildDashedLine({
    double thickness = 1,
    Color color = Colors.grey,
    double dashWidth = 4,
    double dashSpace = 3,
    double totalWidth = double.infinity,
  }) {
    return SizedBox(
      width: totalWidth,
      height: thickness,
      child: LayoutBuilder(
        builder: (context, constraints) {
          return ListView.builder(
            scrollDirection: Axis.horizontal,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: (constraints.maxWidth / (dashWidth + dashSpace)).ceil(),
            itemBuilder: (context, index) {
              return Container(
                width: dashWidth,
                height: thickness,
                color: color,
                margin: EdgeInsets.only(right: dashSpace),
              );
            },
          );
        },
      ),
    );
  }

  List<String> _getTagListWithModel(Players playsModel) {
    List<String> tagList = [];
    if (playsModel.scoreKing) {
      tagList.add('得分王');
    }
    if (playsModel.reboundKing) {
      tagList.add('篮板王');
    }
    if (playsModel.assistKing) {
      tagList.add('助攻王');
    }
    if (playsModel.threePointKing) {
      tagList.add('三分王');
    }
    if (playsModel.freeThrowKing) {
      tagList.add('罚球王');
    }
    return tagList;
  }
}
