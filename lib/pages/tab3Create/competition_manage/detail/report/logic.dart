import 'package:carousel_slider/carousel_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/main.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/game/report/models/team_players_model.dart';
import 'package:shoot_z/pages/game/report/models/team_report_model.dart';
import 'package:shoot_z/pages/game/report/state.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/myShareH5.dart';
import 'package:shoot_z/widgets/video/video_controller.dart';

class MatchTeamReportLogic extends GetxController
    with GetTickerProviderStateMixin, WidgetsBindingObserver, RouteAware {
  final state = TeamReportState();
  AnimationController? _animationController;
  Animation<double>? arrowAnimation;
  final CarouselSliderController carouselController =
      CarouselSliderController();
  TabController? tabController;
  var tabbarIndex = 0.obs;

  @override
  void didPopNext() {
    if (!state.videoController.fromFullScreen) {
      getData(true);
      if (state.teamPlayers.value != null) {
        getTeamData();
      }
    }
    super.didPopNext();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState s) {
    super.didChangeAppLifecycleState(s);
    if (s == AppLifecycleState.resumed && state.init.value) {
      getData(true);
      if (state.teamPlayers.value != null) {
        getTeamData();
      }
    }
  }

  var isSubscribe = false;
  void subscribe(BuildContext context) {
    if (isSubscribe) return;
    final ModalRoute? route = ModalRoute.of(context);
    if (route is PageRoute) {
      routeObserver.subscribe(this, route);
      isSubscribe = true;
    }
  }

  late String teamId;
  @override
  void onInit() async {
    WidgetsBinding.instance.addObserver(this);
    final arguments = Get.arguments as Map;
    state.matchId = arguments['matchId'];
    teamId = arguments['teamId'];
    if (arguments['notDetails'] != null) {
      state.notDetails = arguments['notDetails'];
    }
    state.showReportHint =
        (await WxStorage.instance.getBool('showReportHint') == null).obs;
    if (state.showReportHint.value) {
      _animationController = AnimationController(
        vsync: this,
        duration: const Duration(seconds: 1), // 动画持续时间
      )..repeat(reverse: true); // 动画重复

      arrowAnimation = Tween<double>(begin: 0, end: -10).animate(
        CurvedAnimation(
          parent: _animationController!,
          curve: Curves.easeInOut,
        ),
      );
    }
    tabController = TabController(length: 2, vsync: this);
    tabController?.addListener(
      () {
        tabbarIndex.value = tabController?.index ?? 0;
        if (tabbarIndex.value == 1 && state.teamPlayers.value == null) {
          WxStorage.instance.setBool('showReportHint', true);
          state.showReportHint.value = false;
          _animationController?.dispose();
          _animationController = null;
          getTeamData();
        }
      },
    );
    getData(false);
    state.videoController = VideoController();
    super.onInit();
  }

  @override
  void onClose() {
    WidgetsBinding.instance.removeObserver(this);
    routeObserver.unsubscribe(this);
    tabController?.dispose();
    _animationController?.dispose();
    disposeVideo();
    super.onClose();
  }

  void getTeamData() async {
    final res = await Api().get(ApiUrl.getPTZMatchIdPlayers(state.matchId));
    if (res.isSuccessful()) {
      state.teamPlayersList = (res.data as List)
          .map((e) => TeamPlayersModel.fromJson(e))
          .toList() as List<TeamPlayersModel>?;
      state.teamPlayers.value =
          state.teamPlayersList![state.currentIndex.value];
      state.teamPlayers.refresh();
    } else {
      // if(state.teamPlayers.value == null) {
      //   getTeamData();
      // }
    }
  }

  void getData(bool refresh) async {
    // final results = await Future.wait([getPayInfo(), getTeamReport(refresh)]);
    final results = await Future.wait([getTeamReport(refresh)]);
    if (!refresh) {
      if (results.first && results.last) {
        state.init.value = true;
      } else {
        AppPage.back();
      }
    }
  }

//   Future<bool> getPayInfo() async {
//     if (!UserManager.instance.isLogin) return true;
//     if (state.closeHint.value) return true;
//     final res = await Api()
//         .get(ApiUrl.getMatchIdPayInfo(state.matchId), queryParameters: {
//       'matchId': state.matchId,
//       'clientType': Platform.isAndroid ? 1 : 2,
// //  clientType query int false "客户端类型 1: android, 2: ios"
//     });
//     if (res.isSuccessful()) {
//       state.payInfo.value = MatchPayInfoModel.fromJson(res.data);
//       state.payInfo.refresh();
//       return true;
//     }
//     return false;
//   }
//申请加入球队
  postTeamApply(String teamIdStr) async {
    Map<String, dynamic> param = {
      'teamId': teamIdStr,
    };
    WxLoading.show();
    var url = await ApiUrl.postTeamApply(teamIdStr);
    var res = await Api().post(url, data: param);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      WxLoading.showToast("申请成功");
      state.model.update((item) {
        item?.applyStatus = 1;
      });
    } else {
      WxLoading.showToast(res.message);
    }
  }

  Future<bool> getTeamReport(bool refresh) async {
    final res = await Api().get(ApiUrl.getMatchTeamReport(),
        queryParameters: {'matchId': state.matchId});
    if (res.isSuccessful()) {
      state.modelList =
          (res.data as List).map((e) => TeamReportModel.fromJson(e)).toList();
      // final sectionRes = await Api().get(ApiUrl.getSectionScore(state.matchId));
      // if (sectionRes.isSuccessful()) {
      //   state.sectionScoreList = (sectionRes.data as List)
      //       .map((e) => SectionScoreModel.fromJson(e))
      //       .toList();
      // }
      if (!refresh) {
        switchTeam(teamId == state.modelList.first.teamId ? 0 : 1);
      } else {
        switchTeam(state.currentIndex.value);
      }
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (carouselController.ready) {
          carouselController.animateToPage(state.currentIndex.value,
              duration: const Duration(microseconds: 1));
        }
      });
      return true;
    }
    return false;
  }

  void switchTeam(int index) {
    state.currentIndex.value = index;
    if (state.init.value) {
      final oldVideoPath = state.model.value.videoPath;
      state.model.value = state.modelList[index];
      state.model.refresh();
      // 如果视频路径改变，需要重新初始化视频
      if (oldVideoPath != state.model.value.videoPath) {
        state.videoController.setData(
            videoPath: state.model.value.videoPath,
            videoCover: state.model.value.videoCover,
            highlightVideoId: state.model.value.highlightVideoId.toString());
      }
    } else {
      state.model = state.modelList[index].obs;
      state.videoController.setData(
          videoPath: state.model.value.videoPath,
          videoCover: state.model.value.videoCover,
          highlightVideoId: state.model.value.highlightVideoId.toString());
    }
    state.teamPlayers.value = state.teamPlayersList?[state.currentIndex.value];

    initPlayerDatalist();
  }

  void disposeVideo() {
    state.videoController.dispose();
  }

  void unlockPlayer(String playerId, bool locking) {
    if (locking) {
      // UnlockPayUtils.pay(state.matchId,
      //     teamId: state.model.value.teamId, playerId: playerId);
      //解锁报告 type 0整场解锁  1单队解锁 2个人解锁
      AppPage.to(Routes.unlockDataPage,
          arguments: {
            "type": 2,
            "matchId": state.matchId,
            "teamId": state.model.value.teamId,
            "playerId": playerId
          },
          needLogin: true);
      UserManager.instance.postApmTracking(0,
          nowPage: Routes.matchTeamReportPage,
          toPage: Routes.unlockDataPage,
          subPage: "UnlockCount2",
          remark: "单人解锁报告",
          content: "进入单人数据页面");
    } else {
      AppPage.to(Routes.ptzPlayerReportPage, arguments: {
        "teamId": state.model.value.teamId,
        "playerId": playerId,
        "matchId": state.matchId
      });
    }
  }

  void unlockTeam() {
    if (!state.model.value.locking) {
      share();
      return;
    } else {
      //解锁报告 type 0整场解锁  1单队解锁 2个人解锁
      AppPage.to(Routes.unlockDataPage,
          arguments: {
            "type": 1,
            "matchId": state.matchId,
            "teamId": state.model.value.teamId,
          },
          needLogin: true);
      UserManager.instance.postApmTracking(0,
          nowPage: Routes.matchTeamReportPage,
          toPage: Routes.unlockDataPage,
          subPage: "UnlockCount1",
          remark: "单队解锁报告",
          content: "进入单队数据页面");
    }
  }

  void share() {
    MyShareH5.getShareH5(ShareTeamReport(
        matchId: state.matchId, teamId: state.model.value.teamId));
  }

  var playerDatalist = [
    {"data": "", "name": S.current.score, "index": "0"},
    {"data": "", "name": S.current.player_report_tips6, "index": "1"},
    {"data": "", "name": S.current.shotRate, "index": "2"},
    {"data": "", "name": S.current.player_report_tips7, "index": "3"},
    {"data": "", "name": S.current.player_report_tips8, "index": "4"},
    {"data": "", "name": S.current.assist, "index": "5"},
    {"data": "", "name": S.current.player_report_tips9, "index": "6"},
    {"data": "", "name": S.current.player_report_tips10, "index": "7"},
    {"data": "", "name": S.current.player_report_tips11, "index": "8"},
  ].obs;

  initPlayerDatalist() {
    final teamScoreDetail = state.model.value.teamScoreDetail;
    playerDatalist[0]["data"] = "${teamScoreDetail.totalScore}"; //得分
    playerDatalist[1]["data"] =
        "${teamScoreDetail.shootHit}/${teamScoreDetail.shootCount}"; //投篮
    playerDatalist[2]["data"] =
        "${teamScoreDetail.shootRate}${(teamScoreDetail.shootRate).contains("%") ? "" : "%"}"; //命中率
    playerDatalist[3]["data"] =
        "${teamScoreDetail.threePointShootHit}/${teamScoreDetail.threePointShootCount}"; //三分
    playerDatalist[4]["data"] =
        '${teamScoreDetail.threePointShootRate}%'; //三分命中率
    playerDatalist[5]["data"] = "${teamScoreDetail.assistCount}"; //助攻
    playerDatalist[6]["data"] = "${teamScoreDetail.freeThrowShootCount}"; //罚球
    playerDatalist[7]["data"] =
        '${teamScoreDetail.freeThrowShootRate}%'; //罚球命中率
    playerDatalist[8]["data"] = "${teamScoreDetail.reboundCount}"; //篮板
    playerDatalist.refresh();
  }
}
