import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/player_report_model.dart';
import 'package:shoot_z/utils/AndroidDevicePerformance.dart';

class PTZPlayerReportLogic extends GetxController {
  var playerReportModel = PlayerReportModel().obs;
  var isFrist = true.obs;
  var playerTaglist = [
    {
      "img": "img_player_defen.png",
      "name": S.current.player_report_tips1,
      "select": "0"
    },
    {
      "img": "img_player_lanban.png",
      "name": S.current.player_report_tips2,
      "select": "0"
    },
    {
      "img": "img_player_zhugong.png",
      "name": S.current.player_report_tips3,
      "select": "0"
    },
    {
      "img": "img_player_sanfen.png",
      "name": S.current.player_report_tips4,
      "select": "0"
    },
    {
      "img": "img_player_faqiu.png",
      "name": S.current.player_report_tips5,
      "select": "0"
    }
  ].obs;
  var playerDatalist = [
    {"data": "0", "name": S.current.score, "index": "0"},
    {"data": "0", "name": S.current.player_report_tips6, "index": "1"},
    {"data": "0", "name": S.current.shotRate, "index": "2"},
    {"data": "0", "name": S.current.player_report_tips7, "index": "3"},
    {"data": "0", "name": S.current.player_report_tips8, "index": "4"},
    {"data": "0", "name": S.current.assist, "index": "5"},
    {"data": "0", "name": S.current.player_report_tips9, "index": "6"},
    {"data": "0", "name": S.current.player_report_tips10, "index": "7"},
    {"data": "0", "name": S.current.player_report_tips11, "index": "8"},
  ].obs;
  var playerScorelist = [
    {"data": "0", "name": S.current.player_report_tips17(1), "index": "0"},
    {"data": "0", "name": S.current.player_report_tips17(2), "index": "1"},
    {"data": "0", "name": S.current.player_report_tips17(3), "index": "2"},
    {"data": "0", "name": S.current.player_report_tips17(4), "index": "3"},
    {"data": "0", "name": S.current.player_report_tips13, "index": "4"},
  ].obs;
  var teamId = "".obs; // "3423".obs;
  var playerId = "".obs; //"53408".obs;
  var matchId = "".obs; //"3811".obs;

  var isPaintingComplete = false.obs;
  DeviceTier deviceTier = DeviceTier.lowEnd;
  late final double containerWidth = ScreenUtil().screenWidth - 30.w;
  late final double containerHeight = containerWidth * 14 / 15;
  @override
  void onInit() {
    super.onInit();
    teamId.value = Get.arguments["teamId"];
    playerId.value = Get.arguments["playerId"];
    matchId.value = Get.arguments["matchId"];
  }

  @override
  void onReady() {
    super.onReady();
    getTeamInfo();
  }

  // 转换数据到绘制需要的格式
  List<PlayerReportModelShootLocation> get paintPoints {
    return (playerReportModel.value.shootLocation?.map((point) {
              return PlayerReportModelShootLocation(
                x: point?.x,
                y: point?.y,
                hit: point?.hit ?? false,
              );
            }).toList() ??
            [])
        .take(800)
        .toList();
  }

  //获得球队资料
  getTeamInfo() async {
    Map<String, dynamic> param = {
      'matchId': matchId.value,
      'teamId': teamId.value,
      'playerId': playerId.value
    };
    //WxLoading.show();
    var url = await ApiUrl.getPTZTeamPlayerReport(
        matchId.value, teamId.value, playerId.value);
    var res = await Api().get(url, queryParameters: param);
    //WxLoading.dismiss();
    if (res.isSuccessful()) {
      playerReportModel.value = PlayerReportModel.fromJson(res.data);
      playerReportModel.refresh();

      playerDatalist[0]["data"] =
          "${playerReportModel.value.singlePlayerDetail?.score ?? "0"}"; //得分
      playerDatalist[1]["data"] =
          "${playerReportModel.value.singlePlayerDetail?.shootHit ?? "0"}/${playerReportModel.value.singlePlayerDetail?.shootCount ?? "0"}"; //投篮
      playerDatalist[2]["data"] =
          "${(playerReportModel.value.singlePlayerDetail?.shootRate ?? "0") == "" ? "0" : playerReportModel.value.singlePlayerDetail?.shootRate ?? "0"}${(playerReportModel.value.singlePlayerDetail?.shootRate ?? "0").contains("%") ? "" : "%"}"; //命中率
      playerDatalist[3]["data"] =
          "${playerReportModel.value.singlePlayerDetail?.threePointShootHit ?? "0"}/${playerReportModel.value.singlePlayerDetail?.threePointShootCount ?? "0"}"; //三分
      playerDatalist[4]["data"] =
          playerReportModel.value.singlePlayerDetail?.threePointShootRate ??
              "0"; //三分命中率
      playerDatalist[5]["data"] =
          "${playerReportModel.value.singlePlayerDetail?.assistCount ?? "0"}"; //助攻
      playerDatalist[6]["data"] =
          "${playerReportModel.value.singlePlayerDetail?.freeThrowShootCount ?? "0"}"; //罚球
      playerDatalist[7]["data"] =
          playerReportModel.value.singlePlayerDetail?.freeThrowShootRate ??
              "0"; //罚球命中率
      playerDatalist[8]["data"] =
          "${playerReportModel.value.singlePlayerDetail?.reboundCount ?? "0"}"; //篮板
      playerDatalist.refresh();
      //小节比分
      if (playerReportModel.value.sectionsScore?.isNotEmpty ?? false) {
        playerScorelist[0]["data"] =
            "${playerReportModel.value.sectionsScore?.first?.secScore1 ?? "0"}";
        playerScorelist[1]["data"] =
            "${playerReportModel.value.sectionsScore?.first?.secScore2 ?? "0"}";
        playerScorelist[2]["data"] =
            "${playerReportModel.value.sectionsScore?.first?.secScore3 ?? "0"}";
        playerScorelist[3]["data"] =
            "${playerReportModel.value.sectionsScore?.first?.secScore4 ?? "0"}";
        playerScorelist[4]["data"] =
            "${playerReportModel.value.sectionsScore?.first?.totalScore ?? "0"}";
        playerScorelist.refresh();
      }
      var list1 = playerTaglist.where((value) {
        return playerReportModel.value.title?.contains(value["name"] ?? "") ??
            false;
      }).toList();
      var list2 = playerTaglist.where((value) {
        return !(playerReportModel.value.title?.contains(value["name"] ?? "") ??
            false);
      }).toList();

      playerTaglist.assignAll(list1);
      playerTaglist.addAll(list2);
    } else {
      WxLoading.showToast(res.message);
    }
    if (isFrist.value) {
      isFrist.value = false;
    }
  }

  //认领报告
  Future<void> bindReport() async {
    Map<String, dynamic> param = {
      'matchId': matchId.value,
      'teamId': teamId.value,
      'playerId': playerId.value
    };
    WxLoading.show();
    var url = await ApiUrl.getPTZTeamPlayerReportBind(
        matchId.value, teamId.value, playerId.value);
    var res = await Api().PUT(url, queryParameters: param);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      getTeamInfo();
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //解绑
  Future<void> unBindReport() async {
    Map<String, dynamic> param = {
      'matchId': matchId.value,
      'teamId': teamId.value,
      'playerId': playerId.value
    };
    WxLoading.show();
    var url = await ApiUrl.getPTZTeamPlayerReportUnbind(
        matchId.value, teamId.value, playerId.value);
    var res = await Api().PUT(url, queryParameters: param);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      WxLoading.showToast("解绑成功");
      getTeamInfo();
    } else {
      WxLoading.showToast(res.message);
    }
  }

  String formatDuration(int seconds) {
    // int hours = seconds ~/ 3600; // 计算小时数
    // int minutes = (seconds % 3600) ~/ 60; // 计算分钟数
    int minutes = (seconds) ~/ 60; // 计算分钟数
    int remainingSeconds = seconds % 60; // 计算剩余秒数
    return " ${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}";
  }
}
