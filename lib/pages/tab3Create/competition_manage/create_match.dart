import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/create_match_logic.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/widget/custom_calendar_date_picker.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/model/match_model.dart';
import 'package:shoot_z/widgets/text_with_icon.dart';
import 'package:shoot_z/widgets/time_picker_bottom_sheet.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:intl/intl.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 我的球队列表->新增球队
class CreateMatchPage extends StatelessWidget {
  CreateMatchPage({super.key});

  final logic = Get.put(CreateMatchLogic());
  final bool showBuy = false;
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
        appBar: AppBar(
          title: Text(
              (Get.arguments != null && Get.arguments.containsKey('edit'))
                  ? "编辑比赛"
                  : '创建比赛'),
        ),
        body: _createTeamWidget(context),
        bottomNavigationBar: InkWell(
          onTap: () {
            if (logic.matchId.isNotEmpty) {
              logic.createMatch(matchId: logic.matchId);
              return;
            }
            logic.createMatch();
          },
          child: Container(
            width: double.infinity,
            height: 50.w,
            alignment: Alignment.center,
            margin: EdgeInsets.only(
                left: 15.w, right: 15.w, bottom: ScreenUtil().bottomBarHeight),
            decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Colours.color7732ED, Colours.colorA555EF],
                  begin: Alignment.bottomLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(25.r)),
            child: Text(
              (Get.arguments != null && Get.arguments.containsKey('edit'))
                  ? "编辑比赛"
                  : '创建比赛',
              style: TextStyles.semiBold14,
            ),
          ),
        ),
      );
    });
  }

  String formatChineseTime(String timeStr) {
    return timeStr.replaceAll("时", ":").replaceAll("分", "");
  }

  /// 列表数据
  _createTeamWidget(BuildContext context) {
    DateTime selectDate = DateTime.now();
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            height: 36.w,
          ),
          _createCellWidget('请选择比赛日期', 'assets/images/calendar_icon.png',
              () async {
            DateTime currentDate = DateTime.now();
            DateTime threeYearsLater =
                currentDate.copyWith(year: currentDate.year + 3);
            final dateResult = await showDialog(
                context: context,
                builder: (BuildContext context) {
                  return CustomCalendarDatePicker(
                    title: '日期选择',
                    firstDay: currentDate,
                    lastDay: threeYearsLater,
                    curFocusedDay: selectDate,
                  );
                });
            selectDate = dateResult;
            String formattedDate = DateFormat('yyyy-MM-dd').format(dateResult);
            logic.dateStr.value = formattedDate;
          }, logic.dateStr, fontFamily: 'DIN'),
          SizedBox(
            height: 15.w,
          ),
          _createCellWidget('请选择比赛时间', 'assets/images/time_icon.png', () async {
            final result = await showModalBottomSheet(
                context: context,
                builder: (BuildContext context) {
                  return TimePickerBottomSheet();
                });
            logic.timeStr.value = formatChineseTime(result);
          }, logic.timeStr, fontFamily: 'DIN'),
          SizedBox(
            height: 15.w,
          ),
          _createCellWidget('请选择比赛地点', 'assets/images/ic_location.png', () {
            AppPage.to(Routes.courtFootageHomePage,
                arguments: {'isCreateMatch': true});
          }, logic.venueName),
          Container(
            margin: const EdgeInsets.only(top: 30, left: 30, right: 30),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: (ScreenUtil().screenWidth - 90) / 2,
                  alignment: Alignment.center,
                  child: _createSelectTeamWidget(() {
                    AppPage.to(Routes.matchTeamListPage,
                            arguments: logic.rightTeamModel.value.id)
                        .then((onValue) {
                      if (onValue != null) {
                        logic.leftTeamModel.value = onValue;
                      }
                    });
                  }, logic.leftTeamModel.value),
                ),
                Container(
                    width: (ScreenUtil().screenWidth - 90) / 2,
                    alignment: Alignment.center,
                    child: _createSelectTeamWidget(() {
                      AppPage.to(Routes.matchTeamListPage,
                              arguments: logic.leftTeamModel.value.id)
                          .then((onValue) {
                        if (onValue != null) {
                          logic.rightTeamModel.value = onValue;
                        }
                      });
                    }, logic.rightTeamModel.value))
              ],
            ),
          ),
          Container(
            margin: const EdgeInsets.only(top: 44, left: 30, right: 30),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                    width: (ScreenUtil().screenWidth - 90) / 2,
                    alignment: Alignment.center,
                    child: GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return _createColorAlert(context, (value) {
                              logic.leftColorMap.value = value;
                            });
                          },
                        );
                      },
                      child: _createSelectColorWidget(
                          colorName: logic.leftColorMap["colorName"],
                          color: logic.leftColorMap["colorValue"]),
                    )),
                Container(
                    width: (ScreenUtil().screenWidth - 90) / 2,
                    alignment: Alignment.center,
                    child: GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return _createColorAlert(context, (value) {
                              logic.rightColorMap.value = value;
                            });
                          },
                        );
                      },
                      child: _createSelectColorWidget(
                          colorName: logic.rightColorMap["colorName"],
                          color: logic.rightColorMap["colorValue"]),
                    ))
              ],
            ),
          ),
          SizedBox(
            height: 20.w,
          ),
          Container(
              margin: EdgeInsets.only(left: 15.w),
              alignment: Alignment.centerLeft,
              child: const TextWithIcon(title: '赛事服务')),
          SizedBox(
            height: 20.w,
          ),
          Row(
            children: [
              SizedBox(
                width: 15.w,
              ),
              const Text(
                '是否需要赛事报告',
                style: TextStyle(color: Colors.white, fontSize: 14),
              ),
              SizedBox(
                width: 8.w,
              ),
              TextButton(
                onPressed: () {
                  if (!logic.needReport.value) {
                    logic.needReport.value = true;
                  }
                },
                style: TextButton.styleFrom(
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  padding: EdgeInsets.zero,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    logic.needReport.value
                        ? WxAssets.images.selectIcon.image()
                        : WxAssets.images.unselectIcon.image(),
                    SizedBox(width: 6.w),
                    Text("是", style: TextStyles.display14),
                  ],
                ),
              ),
              SizedBox(
                width: 20.w,
              ),
              TextButton(
                onPressed: () {
                  if (logic.needReport.value) {
                    logic.needReport.value = false;
                  }
                },
                style: TextButton.styleFrom(
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  padding: EdgeInsets.zero,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    logic.needReport.value
                        ? WxAssets.images.unselectIcon.image()
                        : WxAssets.images.selectIcon.image(),
                    SizedBox(width: 6.w),
                    Text("否", style: TextStyles.display14),
                  ],
                ),
              )
            ],
          ),
          SizedBox(
            height: 24.w,
          ),
          if (logic.needReport.value)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                    height: 30.w,
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(left: 15.w),
                    padding: EdgeInsets.only(left: 15.w, right: 15.w),
                    decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [
                            Colours.colorFFECC1,
                            Colours.colorE7CEFF,
                            Colours.colorD1EAFF
                          ],
                          begin: Alignment.bottomLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(15.r)),
                    child: Text.rich(
                      TextSpan(
                        children: [
                          TextSpan(
                              text: '赛事报告捆绑包剩余场次：',
                              style: TextStyles.semiBold14
                                  .copyWith(color: Colours.color191921)),
                          TextSpan(
                              text: '${logic.reportNum.value}场',
                              style: TextStyles.semiBold14
                                  .copyWith(color: Colours.color922BFF)),
                        ],
                      ),
                    )),
                InkWell(
                  onTap: () => AppPage.to(Routes.reportRightsBuyPage),
                  child: Container(
                    height: 30.w,
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(right: 15.w),
                    padding: EdgeInsets.only(left: 25.w, right: 25.w),
                    decoration: BoxDecoration(
                        border: Border.all(color: Colors.white, width: 1),
                        borderRadius: BorderRadius.circular(15.r)),
                    child: Text(
                      '去购买',
                      style: TextStyles.semiBold14,
                    ),
                  ),
                )
              ],
            )
        ],
      ),
    );
  }

  _createCellWidget(String hintText, String iconStr,
      GestureTapCallback cellClick, RxString textStr,
      {String fontFamily = 'regular'}) {
    return InkWell(
      onTap: cellClick,
      child: Container(
        margin: EdgeInsets.only(left: 20.w, right: 20.w),
        width: double.infinity,
        padding: EdgeInsets.only(left: 20.w, right: 20.w),
        height: 50.w,
        decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.circular(25.r)),
        child: Row(
          children: [
            // Expanded(
            //     child: TextField(
            //   controller: txtController,
            //   style: TextStyle(
            //       color: Colors.white,
            //       fontSize: 14,
            //       fontWeight: FontWeight.bold,
            //       fontFamily: fontFamily),
            //   readOnly: true,
            //   decoration: InputDecoration(
            //     hintText: hintText,
            //     hintStyle:
            //         TextStyles.regular.copyWith(color: Colours.color5C5C6E),
            //     contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
            //     //让文字垂直居中,
            //     border: InputBorder.none,
            //   ),
            //   keyboardType: TextInputType.name,
            // )),
            Expanded(
                child: Text(textStr.value == "" ? hintText : textStr.value,
                    style: textStr.value == ""
                        ? TextStyles.regular
                            .copyWith(color: Colours.color5C5C6E)
                        : TextStyles.semiBold14)),
            Image.asset(
              iconStr,
              width: 20.w,
              height: 20.w,
              fit: BoxFit.fitWidth,
            ),
          ],
        ),
      ),
    );
  }

  _createSelectTeamWidget(GestureTapCallback teamClick, MatchModel model) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: teamClick,
          child: Container(
            decoration: BoxDecoration(
                color: const Color(0xFF191921),
                borderRadius: BorderRadius.circular(50.r)),
            width: 60.w,
            height: 60.w,
            child: model.logo == null
                ? WxAssets.images.halfAdd.image(width: 20.w, height: 20.w)
                : MyImage(
                    bgColor: Colors.transparent,
                    model.logo ?? '',
                    // width: 100.w,
                    // height: 100.w,
                    errorImage: "my_team_head.png",
                    placeholderImage: "my_team_head.png",
                    radius: 30.r,
                  ),
          ),
        ),
        const SizedBox(
          height: 15,
        ),
        Text(
          textAlign: TextAlign.center,
          model.name ?? '选择球队',
          style: const TextStyle(color: Color(0xFF5C5C6E), fontSize: 14),
        )
      ],
    );
  }

  _createSelectColorWidget(
      {String colorName = '', Color color = const Color(0xFF191921)}) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(10.r),
              border: Border.all(color: Colors.white, width: 1)),
          width: 20.w,
          height: 20.w,
        ),
        const SizedBox(
          height: 15,
        ),
        Text(
          colorName == '' ? '球衣颜色' : colorName,
          style: const TextStyle(color: Color(0xFF5C5C6E), fontSize: 14),
        )
      ],
    );
  }

  Widget _createColorAlert(BuildContext context, Function callBack) {
    List<Map<String, dynamic>> colorList = [
      {'colorValue': const Color(0xFFFFFFFF), 'colorName': '白色'},
      {'colorValue': const Color(0xFF000000), 'colorName': '黑色'},
      {'colorValue': const Color(0xFFFFCC00), 'colorName': '黄色'},
      {'colorValue': const Color(0xFFFF6200), 'colorName': '橙色'},
      {'colorValue': const Color(0xFF004DFF), 'colorName': '蓝色'},
      {'colorValue': const Color(0xFF00FFFF), 'colorName': '青色'},
      {'colorValue': const Color(0xFFD92BD0), 'colorName': '粉色'},
      {'colorValue': const Color(0xFF922BFF), 'colorName': '紫色'},
      {'colorValue': const Color(0xFFE11A1A), 'colorName': '红色'},
      {'colorValue': const Color(0xFF00C213), 'colorName': '绿色'},
      {'colorValue': const Color(0xFF5C5C6E), 'colorName': '灰色'},
      {'colorValue': const Color(0xFFA8A8BC), 'colorName': '未知'},
    ];
    return Center(
      child: Container(
        margin: const EdgeInsets.only(left: 15, right: 15),
        decoration: BoxDecoration(
          color: const Color(0xFF191921),
          borderRadius: BorderRadius.circular(8.r),
        ),
        height: 250.w,
        width: ScreenUtil().screenWidth - 30.w,
        child: Column(
          children: [
            SizedBox(
              width: ScreenUtil().screenWidth - 70.w,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // 居中标题
                  const Padding(
                    padding: EdgeInsets.only(top: 30),
                    child: Text(
                      '选择颜色',
                      style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  // 右上角关闭按钮
                  Positioned(
                    top: 10,
                    right: -10,
                    child: IconButton(
                      icon: const Icon(
                        Icons.close,
                        color: Colors.white,
                      ),
                      onPressed: () => Navigator.of(context).pop(),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      iconSize: 24,
                    ),
                  ),
                ],
              ),
            ),
            GridView.builder(
              padding: const EdgeInsets.only(
                  top: 30, bottom: 0, left: 20, right: 20),
              shrinkWrap: true, //GridView 的大小会根据其内容动态调整，只占用内容所需的空间。
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 6, // 每行两个 item
                crossAxisSpacing: 10.w,
                mainAxisSpacing: 20.w,
                childAspectRatio: 0.7, // 控制每个 item 的宽高比例
              ),
              itemCount: colorList.length,
              itemBuilder: (context, itemIndex) {
                final colorItem = colorList[itemIndex];
                return GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    callBack(colorItem);
                    Navigator.of(context).pop();
                  },
                  child: _createSelectColorWidget(
                      color: colorItem["colorValue"],
                      colorName: colorItem["colorName"]),
                );
              },
            )
          ],
        ),
      ),
    );
  }
}
