import 'dart:developer' as cc;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/model/competition_model.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/match_home_logic.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/match_list_item.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/text_with_icon.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 我的球队列表->新增球队
class MatchHomePage extends StatelessWidget {
  MatchHomePage({super.key});

  final logic = Get.put(MatchHomeLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('赛事主页'),
        actions: [
          IconButton(
              onPressed: () => AppPage.to(Routes.matchSettingPage,
                  arguments: logic.matchItem.value),
              icon: Image.asset(
                'assets/images/setting_icon.png',
                width: 24,
                height: 24,
              ))
        ],
      ),
      body: _createBodyWidget(context),
    );
  }

  /// 主页头部
  _createBodyWidget(BuildContext context) {
    return Obx(() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 20, left: 15, right: 15),
            padding:
                const EdgeInsets.only(top: 20, left: 15, right: 15, bottom: 20),
            decoration: BoxDecoration(
                color: const Color(0xFF191921),
                borderRadius: BorderRadius.circular(8.r)),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      width: 60.w,
                      height: 60.w,
                      //超出部分，可裁剪
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 1),
                        image: DecorationImage(
                          image: CachedNetworkImageProvider(
                              logic.matchItem.value.logo ?? ""),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 15,
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          logic.matchItem.value.name ?? '',
                          style: TextStyles.titleSemiBold16,
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        Row(
                          children: [
                            Container(
                              height: 20.w,
                              margin: const EdgeInsets.only(right: 5),
                              padding: const EdgeInsets.only(left: 8, right: 8),
                              decoration: BoxDecoration(
                                  color: const Color(0xFF2E1575),
                                  borderRadius: BorderRadius.circular(10.r),
                                  border: Border.all(
                                      color: const Color(0xff6435E9),
                                      width: 1)),
                              child: const Center(
                                child: Text(
                                  '高光视频',
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                            ),
                            Container(
                              height: 20.w,
                              margin: const EdgeInsets.only(right: 5),
                              padding: const EdgeInsets.only(left: 8, right: 8),
                              decoration: BoxDecoration(
                                  color: const Color(0xFF2E1575),
                                  borderRadius: BorderRadius.circular(10.r),
                                  border: Border.all(
                                      color: const Color(0xff6435E9),
                                      width: 1)),
                              child: const Center(
                                child: Text(
                                  '视频回放',
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                            ),
                            Container(
                              height: 20.w,
                              margin: const EdgeInsets.only(right: 5),
                              padding: const EdgeInsets.only(left: 8, right: 8),
                              decoration: BoxDecoration(
                                  color: const Color(0xFF2E1575),
                                  borderRadius: BorderRadius.circular(10.r),
                                  border: Border.all(
                                      color: const Color(0xff6435E9),
                                      width: 1)),
                              child: const Center(
                                child: Text(
                                  '赛事报告',
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                            )
                          ],
                        )
                      ],
                    )
                  ],
                ),
                InkWell(
                  onTap: () {
                    AppPage.to(Routes.createMatchPage,
                            arguments: {'create': logic.matchItem.value})
                        .then((onValue) {
                      if (onValue != null) {
                        logic.getdataList(isLoad: false);
                      }
                    });
                  },
                  child: Container(
                    width: double.infinity,
                    height: 44.w,
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(top: 26.w),
                    decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Colours.color7732ED, Colours.colorA555EF],
                          begin: Alignment.bottomLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(25.r)),
                    child: Text(
                      '创建比赛',
                      style: TextStyles.semiBold14,
                    ),
                  ),
                )
              ],
            ),
          ),
          logic.dataList.isNotEmpty
              ? Container(
                  margin: EdgeInsets.only(
                      left: 15.w, right: 15.w, top: 20.w, bottom: 15.w),
                  // height: 50,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const TextWithIcon(title: '比赛列表'),
                      InkWell(
                        onTap: () => AppPage.to(Routes.moreMatchPage,
                            arguments: logic.matchItem.value.id),
                        child: Row(
                          children: [
                            Text('更多比赛', style: TextStyles.display14),
                            SizedBox(
                              width: 8.w,
                            ),
                            Icon(
                              size: 14.w,
                              Icons.arrow_forward_ios,
                              color: Colors.white,
                            )
                          ],
                        ),
                      )
                    ],
                  ),
                )
              : const SizedBox(),
          Expanded(child: _listWidget1(context))
          // _listWidget1(context)
        ],
      );
    });
  }

  /// 列表数据
  _listWidget1(BuildContext context) {
    return SmartRefresher(
      controller: logic.refreshController,
      footer: buildFooter(),
      header: buildClassicHeader(),
      enablePullDown: true,
      enablePullUp: logic.dataList.isNotEmpty,
      onRefresh: () {
        logic.getdataList(isLoad: false);
      },
      onLoading: () {
        logic.getdataList();
      },
      physics: const AlwaysScrollableScrollPhysics(),
      //  physics: const NeverScrollableScrollPhysics(),
      child: (logic.dataFag["isFrist"] as bool)
          ? buildLoad()
          : logic.dataList.isEmpty
              ? SizedBox(
                  height: 300.w,
                  child: myNoDataView(
                    context,
                    msg: S.current.no_matches_yet,
                    imagewidget: WxAssets.images.icGameNo
                        .image(width: 150.w, height: 150.w),
                  ))
              : ListView.builder(
                  scrollDirection: Axis.vertical,
                  // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                  shrinkWrap: true,
                  padding: EdgeInsets.only(bottom: 40.w),
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: logic.dataList.length,
                  itemBuilder: (context, position) {
                    return _listItemWidget(logic.dataList[position]);
                  }),
    );
  }

  /// 构建列表项
  Widget _listItemWidget(CompetitionModel item) {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 15.w),
      child: MatchListItem(
        item: item,
      ),
    );
  }
}
