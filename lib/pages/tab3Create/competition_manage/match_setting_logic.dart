import 'dart:io';

import 'package:dio/dio.dart' as dio;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime_type/mime_type.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/match_home_logic.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/competition_list_logic.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/model/match_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/permission_dialog.dart';
import 'package:utils_package/utils_package.dart';

class MatchSettingLogic extends GetxController with WidgetsBindingObserver {
  TextEditingController txtController1 = TextEditingController();
  final ImagePicker _picker = ImagePicker();
  var textLength1 = 0.obs;
  var modelItem = MatchModel().obs;
  final MatchHomeLogic dataController = Get.find(); // 获取已存在的 Controller
  final CompetitionListLogic matchController = Get.find(); // 获取已存在的 Controller
  @override
  void onInit() {
    super.onInit();
    modelItem.value = Get.arguments;
    txtController1.text = modelItem.value.name ?? '';
    txtController1.addListener(() {
      textLength1.value = txtController1.text.length;
    });
  }

  @override
  void onReady() {
    super.onReady();
  }

  Future<void> getImage(int imgType) async {
    try {
      var permission = await WxPermissionUtils.photo2();
      if (!permission) {
        // Get.dialog(CustomAlertDialog(title: S.current.hint,content: S.current.photo_hint,sureText: S.current.de_authorization,onPressed: () {
        //   AppPage.back();
        //   openAppSettings();
        // },));
        Get.dialog(PermissionDialog(
          text: S.current.enable_permissions,
          contentDes:
              imgType == 0 ? S.current.photo_hint1 : S.current.photo_hint2,
          icon: "ic_photo_permission",
        ));
        return;
      }
      XFile? pickedFile = await _picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        WxLoading.show();
        File file = File(pickedFile.path);
        String path = file.path;
        var fileName = path.substring(path.lastIndexOf("/") + 1, path.length);
        String mimeType = mime(fileName) ?? '';
        String mimee = mimeType.split('/')[0];
        String type = mimeType.split('/')[1];
        dio.FormData formData = dio.FormData.fromMap(<String, dynamic>{
          "file": await dio.MultipartFile.fromFile(
            path,
            filename: fileName,
            contentType: MediaType(mimee, type),
          ),
        });
        var res = await Api().post(ApiUrl.upload,
            data: formData, headers: {"contentType": 'multipart/form-data'});
        if (res.isSuccessful()) {
          if (imgType == 0) {
            WxLoading.dismiss();
            modelItem.update((val) {
              val?.logo = res.data['path']; // 修改数据，自动触发 Obx 刷新
            });
            editCompetitionRequest();
          } else {}
        }
      }
    } catch (e) {
      if (e is MissingPluginException) {
        WxLoading.showToast('当前平台暂不支持！');
      }
    }
  }

  //修改赛事
  Future<void> editCompetitionRequest() async {
    WxLoading.show();
    Map<String, dynamic> param = {
      'logo': modelItem.value.logo,
      'name': txtController1.text
    };
    var url = await ApiUrl.editPtzSeries(modelItem.value.id ?? '');
    var res = await Api().PUT(url, data: param);
    if (res.isSuccessful()) {
      WxLoading.showToast("修改成功");
      modelItem.value.name = txtController1.text;
      dataController.updateData(modelItem.value);
      matchController.onRefresh();
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //删除赛事
  Future<void> deleteCompetitionRequest() async {
    WxLoading.show();
    var url = await ApiUrl.editPtzSeries(modelItem.value.id ?? '');
    var res = await Api().delete(url);
    if (res.isSuccessful()) {
      WxLoading.showToast("删除成功");
      AppPage.back(page: Routes.competitionListPage);
      matchController.onRefresh();
    } else {
      WxLoading.showToast(res.message);
    }
  }
}
