import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/widget/custom_calendar_date_logic.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:ui_packages/ui_packages.dart';
import 'dart:developer' as cc;
import 'package:intl/intl.dart';

class CustomCalendarDatePicker extends StatelessWidget {
  final String title;
  final DateTime firstDay;
  final DateTime lastDay;
  final DateTime curFocusedDay;
  final VoidCallback? onClose;

  CustomCalendarDatePicker({
    super.key,
    required this.title,
    required this.firstDay,
    required this.lastDay,
    required this.curFocusedDay,
    this.onClose,
  });
  final logic = Get.put(CustomCalendarDateLogic());
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(20),
        margin: const EdgeInsets.only(left: 15, right: 15),
        decoration: BoxDecoration(
          color: const Color(0xff191921),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Obx(() {
          logic.selectedDay.value = curFocusedDay;
          String formattedDate =
              DateFormat('yyyy年MM月').format(logic.focusedDay.value);
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 标题栏
              _buildHeader(context),
              // const SizedBox(
              //   height: 30,
              // ),
              Center(
                child: Container(
                  margin: const EdgeInsets.only(top: 20),
                  child: Text(
                    formattedDate,
                    style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16),
                  ),
                ),
              ),
              TableCalendar(
                daysOfWeekHeight: 55,
                headerVisible: false,
                focusedDay: logic.focusedDay.value,
                firstDay: firstDay,
                lastDay: lastDay,
                calendarFormat: logic.calendarFormat.value,
                // daysOfWeekStyle: const DaysOfWeekStyle(
                //   weekdayStyle:
                //       TextStyle(color: Color(0xFFA8A8BC), fontSize: 12),
                //   weekendStyle:
                //       TextStyle(color: Color(0xFFA8A8BC), fontSize: 12),
                // ),
                calendarBuilders: CalendarBuilders(
                  dowBuilder: (context, day) {
                    final texts = ['日', '一', '二', '三', '四', '五', '六'];
                    return Center(
                      child: Text(
                        texts[day.weekday % 7],
                        textAlign: TextAlign.start,
                        style: const TextStyle(
                            color: Color(0xFFA8A8BC), fontSize: 12),
                      ),
                    );
                  },
                ),
                calendarStyle: const CalendarStyle(
                    isTodayHighlighted: false,
                    disabledTextStyle: TextStyle(
                        color: Colors.white,
                        fontFamily: 'DIN',
                        // fontWeight: FontWeight.bold,
                        fontSize: 16),
                    weekNumberTextStyle:
                        TextStyle(color: Colors.white, fontSize: 12),
                    outsideTextStyle: TextStyle(
                        color: Colors.white,
                        fontFamily: 'DIN',
                        fontWeight: FontWeight.bold,
                        fontSize: 16),
                    defaultTextStyle: TextStyle(
                        color: Colors.white,
                        fontFamily: 'DIN',
                        fontWeight: FontWeight.bold,
                        fontSize: 16), // 默认日期颜色
                    weekendTextStyle: TextStyle(
                        color: Colors.white,
                        fontFamily: 'DIN',
                        fontWeight: FontWeight.bold,
                        fontSize: 16), // 周末日期颜色
                    selectedTextStyle: TextStyle(
                        color: Colors.white,
                        fontFamily: 'DIN',
                        fontWeight: FontWeight.bold,
                        fontSize: 16),
                    selectedDecoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                      shape: BoxShape.circle,
                    ) // 选中日期文字颜色
                    ),
                selectedDayPredicate: (day) {
                  return isSameDay(logic.selectedDay.value, day);
                },
                onDaySelected: (selectedDay, focusedDay) {
                  if (!isSameDay(logic.selectedDay.value, selectedDay)) {
                    logic.selectedDay.value = selectedDay;
                    logic.focusedDay.value = focusedDay;
                  }
                  Navigator.pop(context, logic.selectedDay.value);
                },
                onFormatChanged: (format) {
                  if (logic.calendarFormat.value != format) {
                    logic.calendarFormat.value = format;
                  }
                },
                onPageChanged: (focusedDay) {
                  logic.focusedDay.value = focusedDay;
                },
              )
            ],
          );
        }),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // 居中标题
        Padding(
          padding: const EdgeInsets.only(top: 10),
          child: Text(
            title,
            style: const TextStyle(
                fontSize: 16, fontWeight: FontWeight.bold, color: Colors.white),
            textAlign: TextAlign.center,
          ),
        ),
        // 右上角关闭按钮
        Positioned(
          top: 0,
          right: 10,
          child: IconButton(
            icon: const Icon(
              Icons.close,
              color: Colors.white,
            ),
            onPressed: onClose ?? () => Navigator.of(context).pop(),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            iconSize: 24,
          ),
        ),
      ],
    );
  }
}
