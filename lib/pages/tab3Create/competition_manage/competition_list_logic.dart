import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/model/competition_model.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/match_home_logic.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/model/match_model.dart';
import 'dart:developer' as cc;

class CompetitionListLogic extends GetxController {
  /// 是否正在加载数据
  bool _isLoading = false;
  var init = false.obs;
  var items = <MatchModel>[].obs; // 数据源
  var page = 1;
  var pageSize = 20;
  var totalRows = 0;
  @override
  void onInit() {
    super.onInit();
    onRefresh();
  }

  @override
  void onReady() {
    super.onReady();
  }

  Future<void> loadMore() async {
    if (_isLoading) {
      return;
    }
    if (!hasMore()) {
      return;
    }
    await request(false);
  }

  Future<void> onRefresh() async {
    if (_isLoading) {
      return;
    }
    page = 1;
    await request(true);
    // init.value = true;
  }

  Future<void> request(bool isRefresh) async {
    Map<String, dynamic> param = {
      'pageIndex': page,
      'pageSize': pageSize,
    };
    _isLoading = true;
    var res = await Api().get(ApiUrl.getPtzSeries, queryParameters: param);
    _isLoading = false;
    init.value = true;
    cc.log("message!!!!!!${res.data}");
    if (res.isSuccessful()) {
      page += 1;
      totalRows = res.data['totalCount'];
      final list = (res.data['result'] as List)
          .map((e) => MatchModel.fromJson(e))
          .toList();
      if (isRefresh) {
        items.value = list;
      } else {
        items.addAll(list);
      }
    } else {
      if (isRefresh) {
        items.value = [];
        totalRows = 0;
      }
      WxLoading.showToast(res.message);
    }
  }

  //删除比赛
  deleteMatch(String? competitionId, CompetitionModel model,
      MatchHomeLogic controller) async {
    cc.log("request$model");
    var url = await ApiUrl.editMatch(competitionId ?? '', model.id ?? '');
    var res = await Api().delete(url);
    cc.log("result${res.data}");
    if (res.isSuccessful()) {
      controller.getdataList(isLoad: false);
      WxLoading.showToast('删除成功');
    } else {
      WxLoading.showToast(res.message);
    }
  }

  bool hasMore() {
    return items.length < totalRows;
  }
}
