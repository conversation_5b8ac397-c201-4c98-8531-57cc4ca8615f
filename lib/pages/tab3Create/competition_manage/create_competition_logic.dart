import 'dart:io';

import 'package:dio/dio.dart' as dio;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime_type/mime_type.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/widgets/permission_dialog.dart';
import 'package:utils_package/utils_package.dart';
import 'dart:developer' as cc;
class CreateCompetitionLogic extends GetxController with WidgetsBindingObserver {
  TextEditingController txtController1 = TextEditingController();
  final ImagePicker _picker = ImagePicker();
  var textLength1 = 0.obs;
  var headImgPath = "".obs;
  var contentImgPath = "".obs;
  @override
  void onInit() {
    super.onInit();
    txtController1.addListener(() {
      textLength1.value = txtController1.text.length;
    });
  }

  @override
  void onReady() {
    super.onReady();
  }
  Future<void> getImage(int imgType) async {
    try {
      var permission = await WxPermissionUtils.photo2();
      if (!permission) {
        // Get.dialog(CustomAlertDialog(title: S.current.hint,content: S.current.photo_hint,sureText: S.current.de_authorization,onPressed: () {
        //   AppPage.back();
        //   openAppSettings();
        // },));
        Get.dialog(PermissionDialog(
          text: S.current.enable_permissions,
          contentDes:
              imgType == 0 ? S.current.photo_hint1 : S.current.photo_hint2,
          icon: "ic_photo_permission",
        ));
        return;
      }
      XFile? pickedFile = await _picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        WxLoading.show();
        File file = File(pickedFile.path);
        String path = file.path;
        var fileName = path.substring(path.lastIndexOf("/") + 1, path.length);
        String mimeType = mime(fileName) ?? '';
        String mimee = mimeType.split('/')[0];
        String type = mimeType.split('/')[1];
        dio.FormData formData = dio.FormData.fromMap(<String, dynamic>{
          "file": await dio.MultipartFile.fromFile(
            path,
            filename: fileName,
            contentType: MediaType(mimee, type),
          ),
        });
        var res = await Api().post(ApiUrl.upload,
            data: formData, headers: {"contentType": 'multipart/form-data'});
        if (res.isSuccessful()) {
          if (imgType == 0) {
            WxLoading.dismiss();
            headImgPath.value = res.data['path'];
          } else {
            contentImgPath.value = res.data['path'];
          }
        }
      }
    } catch (e) {
      if (e is MissingPluginException) {
        WxLoading.showToast('当前平台暂不支持！');
      }
    }
  }
  Future<void> createMatchRequest() async {
    if (headImgPath.value == '') {
      WxLoading.showToast('请上传赛事图标');
      return;
    }
    if (txtController1.text == '') {
      WxLoading.showToast('请输入赛事名称');
      return;
    }
    Map<String, dynamic> param = {
      'logo':headImgPath.value,
      'name': txtController1.text,
    };
    var res = await Api().post(ApiUrl.getPtzSeries, data: param);
    if (res.isSuccessful()) {
      cc.log("message!!!!!!${res.data}");
      AppPage.back(result: true);
      WxLoading.showToast("创建成功");
    } else {
      WxLoading.showToast(res.message);
    }
  }
}
