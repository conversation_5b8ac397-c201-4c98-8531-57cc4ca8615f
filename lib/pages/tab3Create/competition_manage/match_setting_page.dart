import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/match_setting_logic.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 我的球队列表->新增球队
class MatchSettingPage extends StatelessWidget {
  MatchSettingPage({super.key});

  final logic = Get.put(MatchSettingLogic());
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
        appBar: AppBar(
          title: const Text('赛事设置'),
        ),
        body: _createTeamWidget(context),
        bottomNavigationBar: InkWell(
          onTap: () {
            logic.deleteCompetitionRequest();
          },
          child: Container(
            width: double.infinity,
            height: 50.w,
            alignment: Alignment.center,
            margin: EdgeInsets.only(
                left: 15.w, right: 15.w, bottom: ScreenUtil().bottomBarHeight),
            decoration: BoxDecoration(
                color: const Color(0xFF0F0F16),
                border: Border.all(color: Colors.white, width: 1),
                borderRadius: BorderRadius.circular(25.r)),
            child: const Text(
              '删除赛事',
              style: TextStyle(
                  color: Color(0xFFFF3F3F),
                  fontSize: 14,
                  fontWeight: FontWeight.bold),
            ),
          ),
        ),
      );
    });
  }

  /// 列表数据
  _createTeamWidget(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            height: 20.w,
          ),
          Container(
            margin: EdgeInsets.only(left: 20.w, right: 20.w),
            width: double.infinity,
            padding: EdgeInsets.only(left: 20.w, right: 20.w),
            height: 52.w,
            decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(26.r)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "赛事图标：",
                  style:
                      TextStyles.regular.copyWith(color: Colours.color5C5C6E),
                ),
                InkWell(
                  onTap: () {
                    logic.getImage(0);
                  },
                  child: Row(
                    children: [
                      Container(
                        width: 24.w,
                        height: 24.w,
                        margin: const EdgeInsets.only(right: 10),
                        //超出部分，可裁剪
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 1),
                          image: DecorationImage(
                            image: CachedNetworkImageProvider(
                                logic.modelItem.value.logo ?? ""),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      const Icon(
                        size: 14,
                        Icons.arrow_forward_ios,
                        color: Colors.white,
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
          SizedBox(
            height: 15.w,
          ),
          Container(
            margin: EdgeInsets.only(left: 20.w, right: 20.w),
            width: double.infinity,
            padding: EdgeInsets.only(left: 20.w, right: 20.w),
            height: 52.w,
            decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(26.r)),
            child: Row(
              children: [
                Text(
                  "赛事名称：",
                  style:
                      TextStyles.regular.copyWith(color: Colours.color5C5C6E),
                ),
                Expanded(
                    child: TextField(
                  textAlign: TextAlign.right,
                  controller: logic.txtController1,
                  style: TextStyles.regular,
                  onSubmitted: (str) {
                    logic.editCompetitionRequest();
                  },
                  decoration: InputDecoration(
                    hintText: '赛事名称',
                    hintStyle:
                        TextStyles.regular.copyWith(color: Colours.color5C5C6E),
                    contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
                    //让文字垂直居中,
                    border: InputBorder.none,
                  ),
                  keyboardType: TextInputType.name,
                )),
                SizedBox(
                  width: 10.w,
                ),
                const Icon(
                  size: 14,
                  Icons.arrow_forward_ios,
                  color: Colors.white,
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class CustomLengthLimitingTextInputFormatter extends TextInputFormatter {
  CustomLengthLimitingTextInputFormatter({required this.maxLength});

  final int maxLength;

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    // 计算输入值的有效长度：英文字符计为1，中文字符计为2
    int count = 0;
    for (final rune in newValue.text.runes) {
      if (rune >= 0x4E00 && rune <= 0x9FFF) {
        // 中文字符范围
        count += 2;
      } else {
        count += 1;
      }
      // 如果超过最大长度，则不允许更新
      if (count > maxLength) {
        return oldValue;
      }
    }
    return newValue;
  }
}
