import 'dart:developer' as cc;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/model/competition_model.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/widget/custom_calendar_date_picker.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/match_list_item.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/more_match_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:intl/intl.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///更多比赛
class MoreMatchPage extends StatelessWidget {
  MoreMatchPage({super.key});

  final logic = Get.put(MoreMatchLogic());
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
        body: SizedBox(
          height: double.infinity,
          child: Stack(
            children: [
              Container(
                decoration: BoxDecoration(gradient: GradientUtils.mainGradient),
                width: ScreenUtil().screenWidth,
                height: 184.w,
              ),
              Column(
                children: [
                  _topBarWidget(context),
                  SizedBox(
                    height: 20.w,
                  ),
                  Row(
                    children: [
                      _dateSel(),
                      Container(
                        height: 46.w,
                        width: 1.w,
                        margin: const EdgeInsets.only(left: 8, right: 13),
                        decoration: BoxDecoration(
                            color: const Color(0x034C1DCF),
                            borderRadius: BorderRadius.circular(0),
                            boxShadow: const [
                              BoxShadow(
                                color: Color(0xA6240943),
                                offset: Offset(0, 1),
                                blurRadius: 5,
                                spreadRadius: 0,
                              ),
                            ]),
                      ),
                      InkWell(
                        onTap: () async {
                          DateTime currentDate = DateTime.now();
                          DateTime threeYearsLater =
                              currentDate.copyWith(year: currentDate.year + 3);
                          final dateResult = await showDialog(
                              context: context,
                              builder: (BuildContext context) {
                                return CustomCalendarDatePicker(
                                    title: '日期选择',
                                    firstDay: currentDate,
                                    lastDay: threeYearsLater,
                                    curFocusedDay: logic.currentDate.value);
                              });
                          logic.selectedDate(dateResult);
                        },
                        child: Column(
                          children: [
                            WxAssets.images.calendarIcon
                                .image(color: Colors.white),
                            SizedBox(
                              height: 2.w,
                            ),
                            Text(
                              '日历',
                              style: TextStyles.semiBold14,
                            )
                          ],
                        ),
                      )
                    ],
                  )
                ],
              ),
              Positioned(
                  top: ScreenUtil().screenWidth * 184 / 375,
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: _listWidget1(context)),
            ],
          ),
        ),
      );
    });
  }

  Widget _topBarWidget(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 50.w,
      alignment: Alignment.center,
      margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 60.w,
            padding: EdgeInsets.only(left: 8.w, right: 10.w, top: 6.w),
            child: IconButton(
                onPressed: () {
                  AppPage.back();
                },
                icon: const Icon(
                  Icons.arrow_back_ios,
                  color: Colors.white,
                )),
          ),
          Container(
            padding: EdgeInsets.only(top: 6.w),
            child: Text(
              '更多比赛',
              style: TextStyles.titleSemiBold16,
            ),
          ),
          Container(
            width: 60.w,
            padding: EdgeInsets.only(left: 10.w, right: 8.w),
          ),
        ],
      ),
    );
  }

  Widget _dateSel() {
    return Container(
      height: 56,
      width: ScreenUtil().screenWidth - 65.w,
      padding: EdgeInsets.symmetric(horizontal: 4.w),
      // color: Colors.red,
      child: Obx(
        () => ScrollablePositionedList.builder(
          scrollDirection: Axis.horizontal,
          itemCount: logic.dateList.length,
          itemScrollController: logic.scrollController,
          initialScrollIndex: 12,
          itemBuilder: (context, index) {
            Map<String, dynamic> dateDic = logic.dateList[index];
            return GestureDetector(
              onTap: () {
                logic.selectedDate(dateDic['date']);
              },
              child: Container(
                width: 50.w,
                height: double.infinity,
                decoration: BoxDecoration(
                  color: dateDic["isCenterDate"]
                      ? Colors.white
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(10.w),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      dateDic['weekday'] ?? "",
                      style: TextStyle(
                        color: dateDic["isCenterDate"]
                            ? const Color(0xFF4C1DCF)
                            : Colors.white,
                        fontWeight: AppFontWeight.regular(),
                        fontSize: 12.sp,
                      ),
                    ),
                    SizedBox(
                      height: 6.w,
                    ),
                    Text(
                      dateDic['dateFormatStr'] ?? "",
                      style: TextStyle(
                        fontFamily: 'DIN',
                        color: dateDic["isCenterDate"]
                            ? const Color(0xFF4C1DCF)
                            : Colors.white,
                        fontWeight: AppFontWeight.semiBold(),
                        fontSize: 14.sp,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// 列表数据
  _listWidget1(BuildContext context) {
    return SmartRefresher(
      controller: logic.refreshController,
      footer: buildFooter(),
      header: buildClassicHeader(),
      enablePullDown: true,
      enablePullUp: logic.dataList.isNotEmpty,
      onRefresh: () {
        logic.getdataList(isLoad: false, controller: logic.refreshController);
      },
      onLoading: () {
        logic.getdataList(controller: logic.refreshController);
      },
      physics: const AlwaysScrollableScrollPhysics(),
      //  physics: const NeverScrollableScrollPhysics(),
      child: (logic.dataFag["isFrist"] as bool)
          ? buildLoad()
          : logic.dataList.isEmpty
              ? Expanded(
                  child: myNoDataView(
                  context,
                  msg: S.current.no_matches_yet,
                  imagewidget: WxAssets.images.battleEmptyIcon.image(),
                ))
              : ListView.builder(
                  scrollDirection: Axis.vertical,
                  // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                  shrinkWrap: true,
                  padding: EdgeInsets.only(bottom: 40.w),
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: logic.dataList.length,
                  itemBuilder: (context, position) {
                    return _listItemWidget(logic.dataList[position]);
                  }),
    );
  }

  /// 构建列表项
  Widget _listItemWidget(CompetitionModel item) {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.w),
      child: MatchListItem(
        item: item,
      ),
    );
  }
}
