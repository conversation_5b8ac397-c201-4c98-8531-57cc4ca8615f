import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/model/report_package_model.dart';
import 'dart:developer' as cc;

import 'package:shoot_z/utils/pay/pay_utils.dart';

class ReportRightsBuyLogic extends GetxController {
  var topTagList = [
    {
      'iconPath': 'assets/images/rights_report.png',
      'iconDesc': '创建比赛后可\n生成赛事报告'
    },
    {
      'iconPath': 'assets/images/rights_upload.png',
      'iconDesc': '比赛视频可自\n动保存至云端'
    },
    {'iconPath': 'assets/images/rights_time.png', 'iconDesc': '自购买起一年\n时间内有效'}
  ];
  var packageList = <ReportPackageModel>[].obs;
  var currentSelectIndex = 0.obs;
  @override
  void onInit() {
    super.onInit();
    _getReportPackage();
  }

  _getReportPackage() async {
    var res = await Api().get(ApiUrl.getPtzPackage);
    if (res.isSuccessful()) {
      cc.log("!!!!!!!${res.data}");
      packageList.value = (res.data as List)
          .map((e) => ReportPackageModel.fromJson(e))
          .toList();
    }
  }

  buyNow() async {
    final clientType = GetPlatform.isIOS ? "2" : "1";
    final orderRes = await Api().post(ApiUrl.buyPtzMatchNum, data: {
      'orderChan': 2,
      'clientType': clientType,
      'userId': UserManager.instance.user?.userId,
      'packageId': packageList[currentSelectIndex.value].id ?? 0
    });
    if (!orderRes.isSuccessful()) {
      WxLoading.dismiss();
      return;
    }
    final orderId = orderRes.data['orderId'];
    // if (GetPlatform.isIOS) {
    //   WxLoading.dismiss();
    //   cc.log('!!!!!!!!${orderRes.data}');
    //   final appProductId = orderRes.data['appProductId'];
    //   PayUtils.instance.applePay(appProductId, orderId);
    // } else {
    final res =
        await Api().post(ApiUrl.pay, data: {'orderId': orderId, 'channel': 1});
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      final channelPayParams = res.data['channelPayParams'];
      if (channelPayParams != null) {
        PayUtils.instance.wxPay(channelPayParams);
      } else {
        WxLoading.showToast('获取支付参数出错');
      }
      // }
    }
  }

  @override
  void onReady() {
    super.onReady();
  }
}
