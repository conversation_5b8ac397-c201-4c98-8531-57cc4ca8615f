import 'package:get/get.dart';
import 'dart:developer' as cc;

import 'package:shoot_z/pages/tab3Create/competition_manage/model/rights_model.dart';

class ReportRightsBuyLogic extends GetxController {
  var topTagList = [
    {
      'iconPath': 'assets/images/rights_report.png',
      'iconDesc': '创建比赛后可\n生成赛事报告'
    },
    {
      'iconPath': 'assets/images/rights_upload.png',
      'iconDesc': '比赛视频可自\n动保存至云端'
    },
    {'iconPath': 'assets/images/rights_time.png', 'iconDesc': '自购买起一年\n时间内有效'}
  ];
  var sessionList = [
    {
      "session": "单场",
      "price": 59,
    },
    {
      "session": "100场",
      "price": 2999,
    },
    {
      "session": "300场",
      "price": 6999,
    },
    {
      "session": "600场",
      "price": 8999,
    }
  ];
  var dataList = <RightsModel>[].obs;
  var currentSelectIndex = 0.obs;
  @override
  void onInit() {
    super.onInit();
    dataList.value = sessionList.map((e) => RightsModel.fromJson(e)).toList();
  }

  @override
  void onReady() {
    super.onReady();
  }
}
