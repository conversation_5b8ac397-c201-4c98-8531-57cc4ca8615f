import 'dart:developer';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/model/competition_model.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/model/match_model.dart';

class MatchHomeLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  var contentImgPath = "".obs;
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  //数据列表
  var dataList = <CompetitionModel>[].obs;
  var matchItem = MatchModel().obs;
  @override
  void onInit() {
    super.onInit();
    matchItem.value = Get.arguments;
    getdataList(isLoad: false);
  }

  @override
  void onReady() {
    super.onReady();
  }

  updateData(MatchModel model) {
    matchItem.update((val) {
      val?.name = model.name;
      val?.logo = model.logo;
    });
  }

  //获得最新列表
  getdataList({isLoad = true}) async {
    if (isLoad) {
      dataFag["page"] = (dataFag["page"] as int) + 1;
    } else {
      dataFag["page"] = 1;
    }

    Map<String, dynamic> param = {
      'pageIndex': dataFag["page"] ?? 1,
      'pageSize': 20,
      'seriesId': matchItem.value.id ?? 0,
    };
    var url = await ApiUrl.getMatchList(matchItem.value.id ?? '');

    var res = await Api().get(url, queryParameters: param);
    if (res.isSuccessful()) {
      log("zzzzzz12removeAt-${res.data}");
      List list = res.data["result"] ?? [];
      List<CompetitionModel> modelList =
          list.map((e) => CompetitionModel.fromJson(e)).toList();
      if (isLoad) {
        dataList.addAll(modelList);
        dataList.refresh();
        if (modelList.length < 20) {
          refreshController.loadNoData();
          //  controller.loadComplete();
        } else {
          refreshController.loadComplete();
        }
      } else {
        refreshController.resetNoData();
        dataList.assignAll(modelList);
        refreshController.refreshCompleted();
      }
    } else {
      refreshController.refreshCompleted();
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
