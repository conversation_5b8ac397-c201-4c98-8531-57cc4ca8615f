import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/model/match_model.dart';
import 'dart:developer' as cc;

class MatchTeamListLogic extends GetxController with WidgetsBindingObserver {
  TextEditingController txtController1 = TextEditingController();
  var textLength1 = 0.obs;
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  var items = <MatchModel>[].obs; // 数据源
  var selectedTeamId = '';
  var searchKey = '';
  var dataFag = {
    "isFrist": true,
    "page": 1,
  }.obs;
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null) {
      selectedTeamId = Get.arguments;
    }
    txtController1.addListener(() {
      textLength1.value = txtController1.text.length;
    });
    request();
  }

  @override
  void onReady() {
    super.onReady();
  }

  request({
    isLoad = false,
  }) async {
    var page = dataFag["page"] as int;
    if (!isLoad) {
      page = 1;
    }
    Map<String, dynamic> param = {
      'pageIndex': page,
      'pageSize': 10,
    };
    if (searchKey.isNotEmpty) {
      param["name"] = searchKey;
    }
    var res = await Api().get(ApiUrl.getPtzTeamList, queryParameters: param);
    if (res.isSuccessful()) {
      log("!!!!!!${res.data}");
      List<MatchModel> modelList = (res.data['result'] as List)
          .map((e) => MatchModel.fromJson(e))
          .toList();
      dataFag["page"] = page + 1;
      if (isLoad) {
        items.addAll(modelList);
        items.refresh();
        if (items.length == res.data["totalCount"]) {
          refreshController.loadNoData();
        } else {
          refreshController.loadComplete();
        }
      } else {
        refreshController.resetNoData();
        items.assignAll(modelList);
        refreshController.refreshCompleted();
      }
      if (dataFag["isFrist"] as bool) {
        dataFag["isFrist"] = false;
        dataFag.refresh();
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }

  // Future<void> request(bool isRefresh) async {
  //   Map<String, dynamic> param = {
  //     'pageIndex': page,
  //     'pageSize': pageSize,
  //   };
  //   if (searchKey.isNotEmpty) {
  //     param["name"] = searchKey;
  //   }
  //   _isLoading = true;
  //   var res = await Api().get(ApiUrl.getPtzTeamList, queryParameters: param);
  //   _isLoading = false;
  //   init.value = true;
  //   cc.log("message!!!!!!${res.data}");
  //   if (res.isSuccessful()) {
  //     page += 1;
  //     totalRows = res.data['totalCount'];
  //     final list = (res.data['result'] as List)
  //         .map((e) => MatchModel.fromJson(e))
  //         .toList();
  //     if (isRefresh) {
  //       items.value = list;
  //     } else {
  //       items.addAll(list);
  //     }
  //   } else {
  //     if (isRefresh) {
  //       items.value = [];
  //       totalRows = 0;
  //     }
  //     WxLoading.showToast(res.message);
  //   }
  // }
}
