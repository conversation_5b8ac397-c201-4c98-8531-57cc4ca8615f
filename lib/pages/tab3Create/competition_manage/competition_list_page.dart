import 'dart:developer' as cc;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/competition_list_logic.dart';
import 'package:shoot_z/pages/tab3Create/competition_manage/model/match_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

class CompetitionListPage extends StatelessWidget {
  CompetitionListPage({super.key});
  final logic = Get.put(CompetitionListLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        return Column(
          children: [
            _topBarWidget(context),
            const SizedBox(
              height: 10,
            ),
            Expanded(
                child: NotificationListener(
                    onNotification: (ScrollNotification note) {
                      if (note.metrics.pixels == note.metrics.maxScrollExtent) {
                        logic.loadMore();
                      }
                      return true;
                    },
                    child: RefreshIndicator(
                      onRefresh: logic.onRefresh,
                      child: Container(
                        color: Colours.bg_color,
                        child: logic.init.value
                            ? (logic.items.isEmpty
                                ? _emptyView(context)
                                : _listView(context))
                            : buildLoad(),
                      ),
                    ))),
            logic.init.value && logic.items.isNotEmpty
                ? InkWell(
                    onTap: () async {
                      final result =
                          await AppPage.to(Routes.createCompetitionPage);
                      if (result == true) {
                        // 如果结果为 true，表示需要刷新列表
                        logic.onRefresh();
                      }
                    },
                    child: Container(
                      width: double.infinity,
                      height: 50.w,
                      alignment: Alignment.center,
                      margin: EdgeInsets.only(
                          left: 15.w,
                          right: 15.w,
                          bottom: ScreenUtil().bottomBarHeight),
                      decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Colours.color7732ED, Colours.colorA555EF],
                            begin: Alignment.bottomLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(25.r)),
                      child: Text(
                        '创建我的赛事',
                        style: TextStyles.semiBold14,
                      ),
                    ),
                  )
                : const SizedBox()
          ],
        );
      }),
    );
  }

  Widget _listView(BuildContext context) {
    return ListView.builder(
        padding: EdgeInsets.only(top: 15.w),
        itemCount: logic.items.length,
        itemBuilder: (context, index) {
          final item = logic.items[index];
          // 列表项
          return _buildListItem(item);
        });
  }

  /// 构建列表项
  Widget _buildListItem(MatchModel item) {
    return InkWell(
      onTap: () async {
        final result = await AppPage.to(Routes.matchHomePage, arguments: item);
        cc.log("&&&&&&&&$result");
        if (result == true) {
          // 如果结果为 true，表示需要刷新列表
          logic.onRefresh();
        }
      },
      child: Container(
          margin: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 15.w),
          child: Container(
            height: 100.w,
            padding: const EdgeInsets.only(left: 15, right: 15),
            decoration: BoxDecoration(
              color: const Color(0xFF191921), // 设置背景颜色
              borderRadius: BorderRadius.circular(8), // 设置圆角
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      width: 60.w,
                      height: 60.w,
                      //超出部分，可裁剪
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 1),
                        image: DecorationImage(
                          image: CachedNetworkImageProvider(item.logo ?? ""),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 15,
                    ),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 200,
                          child: Text(item.name ?? '',
                              style: TextStyles.semiBold14,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1),
                        ),
                        const SizedBox(
                          height: 15,
                        ),
                        Text(
                          '已创建：${item.matchCount}场比赛',
                          style: const TextStyle(
                              color: Color(0xFFA8A8BC), fontSize: 14),
                        ),
                      ],
                    )
                  ],
                ),
                const Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white,
                )
              ],
            ),
          )),
    );
  }

  Widget _emptyView(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double topDistance = (constraints.maxHeight -
                ScreenUtil().statusBarHeight -
                ScreenUtil().bottomBarHeight -
                250) /
            2;
        return SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: SizedBox(
              height: constraints.maxHeight,
              child: Padding(
                  padding: EdgeInsets.only(top: topDistance),
                  child: myNoDataViewMatch(context, msg: '暂无赛事，快去创建吧',
                      onTap: () async {
                    final result =
                        await AppPage.to(Routes.createCompetitionPage);
                    if (result == true) {
                      // 如果结果为 true，表示需要刷新列表
                      logic.onRefresh();
                    }
                  })),
            ));
      },
    );
  }

  Widget _topBarWidget(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 50.w,
      alignment: Alignment.center,
      margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: 60.w,
            padding: EdgeInsets.only(left: 15.w),
            child: IconButton(
                onPressed: () {
                  AppPage.back();
                },
                icon: const Icon(
                  Icons.arrow_back_ios,
                  color: Colors.white,
                )),
          ),
          Text(
            '赛事列表',
            textAlign: TextAlign.center,
            style: TextStyles.titleSemiBold16,
            overflow: TextOverflow.ellipsis,
          ),
          Container(
            width: 60.w,
            padding: EdgeInsets.only(right: 15.w),
          )
        ],
      ),
    );
  }
}
