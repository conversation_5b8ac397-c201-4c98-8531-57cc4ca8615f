import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

enum CoursStep {
  placePtz, //放置云台
  openBluetooth, //请开启蓝牙
  connectPtz, //开始连接云台
  showDevice, //展示设备
  connectingPtz, //正在连接云台
  permissionNotEnabled, //未开启权限
  permissionGranted //已开启权限
}

class MyTripodHeadLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  var checked = false.obs; //选中下次不再弹出
  var cameraOpen = false.obs; //相机权限是否打开
  var photoOpen = false.obs; //相册权限是否打开
  var microOpen = false.obs; //麦克风权限是否打开
  var locationOpen = false.obs; //位置权限是否打开
  var bluetoothOpen = false.obs; //蓝牙权限是否打开
  var permissionChecked = false.obs; //权限是否已检查过，避免重复检查
  var coursStep = CoursStep.placePtz.obs;
  PermissionStatus permissionStatus = PermissionStatus.denied;
  late AnimationController animationController;
  late List<Animation<double>> animations;
  final List<String> imageUrls = [
    'assets/images/blueTooth_gif_01.png',
    'assets/images/blueTooth_gif_02.png',
    'assets/images/blueTooth_gif_03.png',
    'assets/images/blueTooth_gif_04.png',
  ];
  final List<String> linkImageUrls = [
    'assets/images/link_gif_01.png',
    'assets/images/blueTooth_gif_02.png',
    'assets/images/blueTooth_gif_03.png',
    'assets/images/blueTooth_gif_04.png',
  ];
  @override
  void onInit() {
    super.onInit();
    // 重置权限检查状态，允许重新检查权限
    permissionChecked.value = false;

    animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    // 为每张图片创建间隔动画
    animations = List.generate(4, (index) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: animationController,
          curve: Interval(
            index * 0.1, // 每张图片开始时间
            (index + 1) * 0.1, // 每张图片结束时间
            curve: Curves.easeInOut,
          ),
        ),
      );
    });
  }

  @override
  void onClose() {
    animationController.dispose();
    super.onClose();
  }
}
