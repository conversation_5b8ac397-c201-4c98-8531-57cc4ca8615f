import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab3Create/ptz/my_tripod_head_logic.dart';
import 'package:shoot_z/pages/tab3Create/ptz/widget/custom_dialog.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:developer' as cc;

class MyTripodHead extends StatelessWidget {
  MyTripodHead({super.key});
  final logic = Get.put(MyTripodHeadLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            child: WxAssets.images.pageTopBg.image(
                width: ScreenUtil().screenWidth,
                height: 260.w,
                fit: BoxFit.fitWidth),
          ),
          Column(
            children: [
              _topBar(context),
              Si<PERSON>Box(
                height: 159.w,
              ),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () => AppPage.to(Routes.competitionListPage),
                child: Container(
                  decoration: BoxDecoration(
                      image: DecorationImage(
                          image: WxAssets.images.aiBg.provider(),
                          fit: BoxFit.fill)),
                  width: double.infinity,
                  margin: EdgeInsets.symmetric(horizontal: 15.w),
                  padding:
                      EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.w),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ShaderMask(
                              shaderCallback: (bounds) => const LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: [
                                  Colours.colorFFECC1,
                                  Colours.colorE7CEFF,
                                  Colours.colorD1EAFF,
                                ],
                              ).createShader(bounds),
                              child: Text(
                                '赛事管理',
                                style: TextStyles.display12.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16.sp),
                              ),
                            ),
                            SizedBox(
                              height: 10.w,
                            ),
                            Text(
                              '可创建赛事，智能生成赛事报告',
                              style: TextStyles.display12.copyWith(
                                  color: Colours.colorA8A8BC, fontSize: 12.sp),
                            ),
                          ],
                        ),
                      ),
                      WxAssets.images.ai6.image(width: 78.w, height: 60.w),
                    ],
                  ),
                ),
              ),
              SizedBox(
                height: 40.w,
              ),
              InkWell(
                onTap: () {
                  logic.coursStep.value = logic.checked.value
                      ? CoursStep.openBluetooth
                      : CoursStep.placePtz;
                  showPTZBinding(context);
                },
                child: Container(
                  width: double.infinity,
                  height: 50.w,
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(
                      left: 15.w,
                      right: 15.w,
                      bottom: ScreenUtil().bottomBarHeight),
                  decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(25.r)),
                  child: Text(
                    '连接云台',
                    style: TextStyles.semiBold14,
                  ),
                ),
              )
            ],
          )
        ],
      ),
    );
  }

  Widget _topBar(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(top: 4.w),
      margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 60.w,
            padding: EdgeInsets.only(
              left: 8.w,
              right: 0.w,
            ),
            child: IconButton(
              icon: Icon(
                Icons.arrow_back_ios,
                size: 20.w,
                color: Colors.white,
              ),
              onPressed: () {
                AppPage.back();
              },
            ),
          ),
          Text(
            S.current.my_tripod_head,
            style: TextStyles.titleSemiBold16,
          ),
          Container(
            width: 60.w,
            padding: EdgeInsets.only(left: 0.w, right: 8.w),
          ),
        ],
      ),
    );
  }

  //选择解锁球队
  void showPTZBinding(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      isDismissible: false,
      builder: (context) {
        return Obx(() {
          return SafeArea(
            bottom: true,
            child: BottomFixedPanel(
                showTwoBottomBtn: logic.coursStep.value == CoursStep.showDevice,
                showBottomBtn: logic.coursStep.value != CoursStep.connectingPtz,
                showBottomText:
                    logic.coursStep.value == CoursStep.connectingPtz,
                title: _getPannelTitle(),
                buttonText: _bottomBtnTitle(),
                onButtonPressed: _clickBottomBtn,
                leftButtonPressed: () {
                  logic.coursStep.value = CoursStep.connectPtz;
                },
                rightButtonPressed: () {
                  logic.coursStep.value = CoursStep.connectingPtz;
                },
                content: _getWidgetWithStatus(context)),
          );
        });
      },
    );
  }

  _getPannelTitle() {
    switch (logic.coursStep.value) {
      case CoursStep.placePtz:
        return '教程：放置云台';
      case CoursStep.connectPtz:
        return '连接球秀云台';
      case CoursStep.openBluetooth:
        return '请先开启手机蓝牙';
      case CoursStep.showDevice:
        return '球秀云台Shootz Q1';
      case CoursStep.connectingPtz:
        return '球秀云台Shootz Q1';
      case CoursStep.permissionNotEnabled:
        return '开启云台拍摄前，需以下权限';
      case CoursStep.permissionGranted:
        return '开启云台拍摄前，需以下权限';
    }
  }

  _bottomBtnTitle() {
    switch (logic.coursStep.value) {
      case CoursStep.placePtz:
        return "知道了";
      case CoursStep.connectPtz:
        return '开始连接';
      case CoursStep.openBluetooth:
        return "去开启";
      case CoursStep.showDevice:
        return '球秀云台Shootz Q1';
      case CoursStep.connectingPtz:
        return '云台正在连接中...';
      case CoursStep.permissionNotEnabled:
        return '去开启权限';
      case CoursStep.permissionGranted:
        return '开启拍摄';
    }
  }

  _clickBottomBtn() async {
    switch (logic.coursStep.value) {
      case CoursStep.placePtz:
        _checkBluetoothPermissions();
        logic.coursStep.value = logic.bluetoothOpen.value
            ? CoursStep.connectPtz
            : CoursStep.openBluetooth;
        break;
      case CoursStep.connectPtz:
        logic.coursStep.value = CoursStep.showDevice;
        Future.delayed(const Duration(seconds: 5), () {
          // 2秒后执行的代码
          _checkPermissionAllOpen();
        });
        break;
      case CoursStep.openBluetooth:
        final status = await Permission.bluetooth.request();
        if (status == PermissionStatus.permanentlyDenied) {
          openAppSettings();
        }
        if (permissionIsOpen(status)) {
          logic.coursStep.value = CoursStep.connectPtz;
        }
        break;
      case CoursStep.showDevice:
        logic.coursStep.value = CoursStep.connectingPtz;

        break;
      case CoursStep.connectingPtz:
        break;
      case CoursStep.permissionNotEnabled:
        openAppSettingsPage();
        // requestMicrophonePermission();
        // logic.coursStep.value = CoursStep.permissionGranted;
        break;
      case CoursStep.permissionGranted:
        // logic.coursStep.value = CoursStep.connectingPtz;
        break;
    }
  }

  _getWidgetWithStatus(BuildContext context) {
    switch (logic.coursStep.value) {
      case CoursStep.placePtz:
        return _getWidgetPlacePtz();
      case CoursStep.connectPtz:
        return _getWidgetConnectPtz();
      case CoursStep.openBluetooth:
        return _getWidgetOpenBlueTooth();
      case CoursStep.showDevice:
        return _getWidgetShowDevive();
      case CoursStep.connectingPtz:
        return _getWidgetConnectingPtz();
      case CoursStep.permissionNotEnabled:
        return _getWidgetPermission(context);
      case CoursStep.permissionGranted:
        return _getWidgetPermission(context);
    }
  }

  _getWidgetPlacePtz() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: EdgeInsets.all(15.w),
          height: 173.w,
          decoration: BoxDecoration(
              border: Border.all(color: const Color(0xffEA0000), width: 2.0.w),
              borderRadius: BorderRadius.circular(20.r)),
          child: const Center(
            child: Text(
              '请将此红色区域卡在云台，保持平衡，摄像头不被遮挡以保证高质量的拍摄效果',
              style: TextStyle(fontSize: 14, color: Colors.white),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        SizedBox(height: 20.w),
        Image.asset(
          "assets/images/goal_bg.png",
          width: 186.w,
          height: 105.w,
        ),
        SizedBox(height: 20.w),
        InkWell(
          onTap: () {
            logic.checked.value = !logic.checked.value;
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                logic.checked.value
                    ? Icons.check_circle_outline
                    : Icons.radio_button_unchecked,
                color: Colours.white,
                size: 16,
              ),
              // Image.asset(
              //     "assets/images/ptz_checked.png"
              //         : 'assets/images/choiceNo.png',
              //     width: 16,
              //     height: 16,
              //   ),
              SizedBox(
                width: 4.w,
              ),
              Text(
                '下次不再弹出',
                style: TextStyles.display12.copyWith(color: Colors.white),
                textAlign: TextAlign.center,
              )
            ],
          ),
        )
        // 可以添加更多自定义内容
      ],
    );
  }

  _getWidgetOpenBlueTooth() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 20.w),
        // Image.asset(
        //   "assets/images/goal_bg.png",
        //   width: 160,
        //   height: 160,
        // ),
        SizedBox(
          width: 160.w,
          height: 160.w,
          child: Center(
            child: Stack(
              alignment: Alignment.center,
              children: List.generate(logic.imageUrls.length, (index) {
                return AnimatedBuilder(
                    animation: logic.animationController,
                    builder: (context, child) {
                      return Opacity(
                        opacity: index == 0 ? 1 : logic.animations[index].value,
                        child: Image.asset(logic.imageUrls[index]),
                      );
                    });
              }),
            ),
          ),
        ),
        SizedBox(height: 30.w),
        Padding(
          padding: EdgeInsets.only(left: 18.w, right: 18.w, bottom: 25.w),
          child: const Text(
              '如果设备仍连接不上，请打开系统设置，进入“隐私”选择蓝牙，确保球秀APP已打开蓝牙权限然后再次开启手机蓝牙连接设备。',
              style: TextStyle(fontSize: 14, color: Colors.white, height: 1.71),
              textAlign: TextAlign.left),
        )
      ],
    );
  }

  _getWidgetConnectPtz() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 20.w),
        SizedBox(
          width: 160.w,
          height: 160.w,
          child: Center(
            child: Stack(
              alignment: Alignment.center,
              children: List.generate(logic.linkImageUrls.length, (index) {
                return AnimatedBuilder(
                    animation: logic.animationController,
                    builder: (context, child) {
                      return Opacity(
                        opacity: index == 0 ? 1 : logic.animations[index].value,
                        child: Image.asset(logic.linkImageUrls[index]),
                      );
                    });
              }),
            ),
          ),
        ),
        SizedBox(height: 30.w),
        Padding(
          padding: EdgeInsets.only(left: 18.w, right: 18.w, bottom: 77.w),
          child: Text('请确定设备已开启，且未被其他手机连接',
              style: TextStyles.display14, textAlign: TextAlign.left),
        )
      ],
    );
  }

  _getWidgetShowDevive() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 20.w),
        Image.asset(
          "assets/images/ptz_device.png",
          width: 72.w,
          height: 222.w,
        ),
        SizedBox(height: 30.w)
      ],
    );
  }

  _getWidgetConnectingPtz() {
    return Stack(
      alignment: Alignment.center,
      children: [
        SizedBox(height: 20.w),
        SizedBox(
          width: 160.w,
          height: 160.w,
          child: Center(
            child: Stack(
              alignment: Alignment.center,
              children: List.generate(logic.linkImageUrls.length, (index) {
                return AnimatedBuilder(
                    animation: logic.animationController,
                    builder: (context, child) {
                      return Opacity(
                        opacity: index == 0 ? 1 : logic.animations[index].value,
                        child: Image.asset(logic.linkImageUrls[index]),
                      );
                    });
              }),
            ),
          ),
        ),
        Center(
            child: Image.asset(
          "assets/images/ptz_device.png",
          width: 72.w,
          height: 222.w,
        )),
      ],
    );
  }

  _getWidgetPermission(BuildContext context) {
    return Container(
        margin: EdgeInsets.only(left: 40.w, right: 40.w),
        child: Column(
          children: [
            _getWidgetPermisionCellSync(context, 'permision_camera.png', '相机权限',
                '使用相机进行拍摄', logic.cameraOpen, Permission.camera),
            SizedBox(
              height: 20.w,
            ),
            _getWidgetPermisionCellSync(context, 'permision_photo.png',
                '相册完全权限', '读写相册', logic.photoOpen, Permission.photos),
            SizedBox(
              height: 20.w,
            ),
            _getWidgetPermisionCellSync(context, 'permision_micro.png', '麦克风权限',
                '使用麦克风进行声音录制', logic.microOpen, Permission.microphone),
            SizedBox(
              height: 20.w,
            ),
            _getWidgetPermisionCellSync(
                context,
                'permision_location.png',
                '位置权限',
                '记录拍摄的位置信息',
                logic.locationOpen,
                Permission.locationWhenInUse),
            SizedBox(
              height: 10.w,
            ),
          ],
        ));
  }

  // 同步版本的权限单元格，避免闪烁
  Widget _getWidgetPermisionCellSync(BuildContext context, String imagePath,
      String title, String desc, RxBool isOpen, Permission permission) {
    // 只在第一次构建时检查权限状态，避免重复调用
    _checkPermissionStatusOnce(permission, isOpen);

    // 直接返回UI，不使用FutureBuilder
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Image.asset(
              'assets/images/$imagePath',
              width: 42.w,
              height: 42.w,
            ),
            SizedBox(
              width: 10.w,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title,
                    style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.white,
                        fontWeight: FontWeight.bold),
                    textAlign: TextAlign.left),
                Text(desc,
                    style:
                        TextStyle(fontSize: 12.sp, color: Colours.color5C5C6E),
                    textAlign: TextAlign.left)
              ],
            )
          ],
        ),
        Obx(() {
          return InkWell(
            onTap: () {
              if (isOpen.value) {
                openAppSettings();
              } else {
                requestPermission(permission, isOpen);
              }
              if (logic.coursStep.value == CoursStep.permissionNotEnabled) {
                _checkPermissionAllOpen();
              }
            },
            child: MyImage(
              isOpen.value ? "switch_on.png" : "switch_off.png",
              fit: BoxFit.fitWidth,
              bgColor: Colors.transparent,
              isAssetImage: true,
              width: 48.w,
            ),
          );
        })
      ],
    );
  }

  // 只检查一次权限状态，避免重复调用
  void _checkPermissionStatusOnce(Permission permission, RxBool isOpen) {
    // 使用一个简单的标记来避免重复检查
    if (!logic.permissionChecked.value) {
      _checkAllPermissions();
      logic.permissionChecked.value = true;
    }
  }

// 检查蓝牙权限状态
  void _checkBluetoothPermissions() async {
    // 检查蓝牙权限
    final bluetoothStatus = await Permission.bluetooth.status;
    logic.bluetoothOpen.value = permissionIsOpen(bluetoothStatus);
  }

  // 检查所有权限状态
  void _checkAllPermissions() async {
    // 检查相机权限
    final cameraStatus = await Permission.camera.status;
    logic.cameraOpen.value = permissionIsOpen(cameraStatus);

    // 检查相册权限
    final photoStatus = await Permission.photos.status;
    logic.photoOpen.value = permissionIsOpen(photoStatus);

    // 检查麦克风权限
    final microStatus = await Permission.microphone.status;
    logic.microOpen.value = permissionIsOpen(microStatus);

    // 检查位置权限
    final locationStatus = await Permission.locationWhenInUse.status;
    logic.locationOpen.value = permissionIsOpen(locationStatus);

    // 检查是否所有权限都已开启
    _checkPermissionAllOpen();

    cc.log(
        '!!!!!!!!!!权限检查完成: camera=${logic.cameraOpen.value}, photo=${logic.photoOpen.value}, micro=${logic.microOpen.value}, location=${logic.locationOpen.value}');
  }

  bool permissionIsOpen(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.denied:
        return false;
      case PermissionStatus.granted:
        return true;
      case PermissionStatus.limited:
        return true;
      default:
        return false;
    }
  }

  _checkPermissionAllOpen() {
    if (logic.locationOpen.value &&
        logic.cameraOpen.value &&
        logic.photoOpen.value &&
        logic.microOpen.value) {
      logic.coursStep.value = CoursStep.permissionGranted;
    } else {
      logic.coursStep.value = CoursStep.permissionNotEnabled;
    }
  }

  Future<void> requestPermission(Permission permission, RxBool isOpen) async {
    final status = await permission.request();
    if (status == PermissionStatus.permanentlyDenied) {
      openAppSettings();
    }
    isOpen.value = permissionIsOpen(status);
    _checkPermissionAllOpen();
  }

  // 跳转到应用设置页面
  void openAppSettingsPage() async {
    await openAppSettings();
  }
}
