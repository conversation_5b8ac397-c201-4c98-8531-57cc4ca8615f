import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ui_packages/ui_packages.dart';

class BottomFixedPanel extends StatelessWidget {
  final String title;
  final Widget content;
  final String buttonText;
  final String? leftBtnText;
  final String? rightBtnText;
  final VoidCallback onButtonPressed;
  final VoidCallback? leftButtonPressed;
  final VoidCallback? rightButtonPressed;
  final VoidCallback? onClose;
  final double panelHeight; // 可选固定高度，不设置则为自适应
  final bool showTwoBottomBtn;
  final bool showBottomText;
  final bool showBottomBtn;
  const BottomFixedPanel(
      {super.key,
      required this.title,
      required this.content,
      required this.buttonText,
      required this.onButtonPressed,
      this.leftBtnText,
      this.rightBtnText,
      this.leftButtonPressed,
      this.rightButtonPressed,
      this.onClose,
      this.panelHeight = 0, // 0表示自适应
      this.showTwoBottomBtn = false,
      this.showBottomText = false,
      this.showBottomBtn = true});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 15.w),
      height: panelHeight > 0 ? panelHeight : null,
      constraints: panelHeight > 0
          ? null
          : const BoxConstraints(maxHeight: 600), // 自适应时的最大高度
      decoration: BoxDecoration(
        color: const Color(0xff191921),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 标题栏
          _buildHeader(context),
          const SizedBox(
            height: 30,
          ),
          // 内容区域（可滚动）
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 0),
              child: content,
            ),
          ),
          // 底部按钮
          showTwoBottomBtn ? _buildTwoFooter(context) : _buildFooter(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // 居中标题
        Padding(
          padding: const EdgeInsets.only(top: 30),
          child: Text(
            title,
            style: const TextStyle(
                fontSize: 16, fontWeight: FontWeight.bold, color: Colors.white),
            textAlign: TextAlign.center,
          ),
        ),
        // 右上角关闭按钮
        Positioned(
          top: 10,
          right: 10,
          child: IconButton(
            icon: const Icon(
              Icons.close,
              color: Colors.white,
            ),
            onPressed: onClose ?? () => Navigator.of(context).pop(),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            iconSize: 24,
          ),
        ),
      ],
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Opacity(
        opacity: (showBottomBtn || showBottomText) ? 1 : 0,
        child: Padding(
          padding: EdgeInsets.all(15.w),
          child: showBottomText
              ? Container(
                  margin: EdgeInsets.symmetric(vertical: 40.w),
                  child: Text(
                    buttonText,
                    style: TextStyles.semiBold14
                        .copyWith(fontWeight: FontWeight.normal),
                    textAlign: TextAlign.center,
                  ),
                )
              : InkWell(
                  onTap: onButtonPressed,
                  child: Container(
                    width: double.infinity,
                    height: 50.w,
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(bottom: 15.w),
                    decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Colours.color7732ED, Colours.colorA555EF],
                          begin: Alignment.bottomLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(25.r)),
                    child: Text(
                      buttonText,
                      style: TextStyles.semiBold14,
                    ),
                  ),
                ),
        ));
  }

  Widget _buildTwoFooter(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(15.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          InkWell(
            onTap: leftButtonPressed ?? () {},
            child: Container(
              width: 150.w,
              height: 50.w,
              alignment: Alignment.center,
              margin: EdgeInsets.only(bottom: 15.w),
              decoration: BoxDecoration(
                  color: Colours.color191921,
                  border: Border.all(
                    width: 1,
                    color: Colours.white,
                  ),
                  borderRadius: BorderRadius.circular(25.r)),
              child: Text(
                leftBtnText ?? '不是我的设备',
                style: TextStyles.semiBold14,
              ),
            ),
          ),
          InkWell(
            onTap: rightButtonPressed ?? () {},
            child: Container(
              width: 150.w,
              height: 50.w,
              alignment: Alignment.center,
              margin: EdgeInsets.only(bottom: 15.w),
              decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Colours.color7732ED, Colours.colorA555EF],
                    begin: Alignment.bottomLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(25.r)),
              child: Text(
                rightBtnText ?? '连接',
                style: TextStyles.semiBold14,
              ),
            ),
          )
        ],
      ),
    );
  }
}
