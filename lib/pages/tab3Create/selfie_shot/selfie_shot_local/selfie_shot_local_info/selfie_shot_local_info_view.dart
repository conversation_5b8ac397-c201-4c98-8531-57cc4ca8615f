import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot/selfie_shot_local/selfie_shot_local_info/selfie_shot_local_info_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/DateTimeUtils.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///半场投篮 温馨提示
class SelfieShotLocalInfoPage extends StatelessWidget {
  SelfieShotLocalInfoPage({super.key});
  final logic = Get.put(SelfieShotLocalInfoLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: MyAppBar(
          title: const Text("视频详情"),
          actions: [
            Obx(() {
              return GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  logic.checkAllVideo();
                },
                child: Container(
                  height: 40.w,
                  alignment: Alignment.centerRight,
                  margin: EdgeInsets.only(right: 15.w),
                  child: Text(
                    logic.allCheck.value
                        ? S.current.Deselect_all
                        : S.current.select_all,
                    style: TextStyles.medium.copyWith(
                      fontSize: 13.sp,
                      color: Colours.color964AEE,
                    ),
                  ),
                ),
              );
            }),
          ],
        ),
        body: Obx(() {
          return (logic.dataFag["isFrist"] as bool)
              ? buildLoad()
              : logic.dataList.isEmpty
                  ? Container(
                      margin: EdgeInsets.only(top: 15.w, bottom: 70.w),
                      child: myNoDataView(
                        context,
                        msg: "暂无视频",
                        textColor: Colours.color5C5C6E,
                        height: 10.w,
                        imagewidget: Container(
                          margin: EdgeInsets.only(left: 10.w),
                          child: WxAssets.images.noVideos.image(
                              width: 96.w, height: 60.w, fit: BoxFit.fill),
                        ),
                      ),
                    )
                  : SingleChildScrollView(
                      child: Container(
                        margin:
                            EdgeInsets.only(left: 15.w, right: 15.w, top: 20.w),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        logic.latestDate.value,
                                        style: TextStyle(
                                          color: Colours.color5C5C6E,
                                          height: 1,
                                          fontSize: 14.sp,
                                        ),
                                      ),
                                      SizedBox(
                                        height: 15.w,
                                      ),
                                      Text(
                                        logic.siteName.value == ""
                                            ? "即刻创作"
                                            : logic.siteName.value,
                                        style: TextStyle(
                                          color: Colours.colorA8A8BC,
                                          height: 1,
                                          fontSize: 12.sp,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            GridView.builder(
                                scrollDirection: Axis.vertical,
                                // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                                shrinkWrap: true,
                                physics:
                                    const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                                gridDelegate:
                                    SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 2,
                                  crossAxisSpacing: 15.w,
                                  mainAxisSpacing: 15.w,
                                  childAspectRatio: 165 / 94,
                                ),
                                padding:
                                    EdgeInsets.only(bottom: 60.w, top: 15.w),
                                itemCount: logic.dataList.length,
                                itemBuilder: (context, index) {
                                  return Obx(() {
                                    return GestureDetector(
                                      behavior: HitTestBehavior.translucent,
                                      onTap: () {
                                        // 0 待合成；1 合成中 ； 2 合成完成；3 失败
                                        // AppPage.to(Routes.videoPath, arguments: {
                                        //   "videoPath": item.records[index].videoPath,
                                        //   "teamName": "投篮视频",
                                        //   "isShowShareUpdate": "1",
                                        //   "videoId": item.records[index].id,
                                        // });

                                        AppPage.to(Routes.videoPath,
                                            arguments: {
                                              "videoPath": (logic
                                                      .dataList[index]
                                                      .filePath ??
                                                  ""),
                                              "teamName": "",
                                              "isShowShareUpdate":
                                                  logic.type.value == "1"
                                                      ? "2"
                                                      : "3",
                                              "shotRecordModel":
                                                  logic.dataList[index],
                                            });
                                      },
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Stack(
                                            alignment: Alignment.center,
                                            children: [
                                              MyImage(
                                                "error_shot_video.png",
                                                width: 165.w,
                                                isAssetImage: true,
                                                height: 94.w,
                                                radius: 8.r,
                                                fit: BoxFit.fill,
                                                errorImage:
                                                    "error_shot_video.png",
                                                placeholderImage:
                                                    "error_shot_video.png",
                                              ),
                                              Positioned(
                                                bottom: 5.w,
                                                right: 5.w,
                                                child: Container(
                                                  padding: EdgeInsets.only(
                                                      left: 5.w,
                                                      right: 5.w,
                                                      top: 2.w,
                                                      bottom: 2.w),
                                                  decoration: BoxDecoration(
                                                      color:
                                                          Colours.color65000000,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              2.r)),
                                                  child: Text(
                                                    DateTimeUtils.formatDateTimeWithSeconds(
                                                        DateTime.fromMillisecondsSinceEpoch(
                                                            (logic.dataList[index]
                                                                        .shootTime ??
                                                                    0.0)
                                                                .toInt())),
                                                    textAlign: TextAlign.right,
                                                    style: TextStyles.medium
                                                        .copyWith(
                                                            fontSize: 10.sp,
                                                            color:
                                                                Colours.white),
                                                  ),
                                                ),
                                              ),
                                              Positioned(
                                                top: 0.w,
                                                right: 0.w,
                                                child: GestureDetector(
                                                  onTap: () {
                                                    logic.dataList[index]
                                                        .isCheck = logic
                                                                .dataList[index]
                                                                .isCheck ==
                                                            "1"
                                                        ? "0"
                                                        : "1";
                                                    logic.dataList.refresh();
                                                  },
                                                  child: Container(
                                                      padding: EdgeInsets.only(
                                                          left: 20.w,
                                                          right: 8.w,
                                                          top: 8.w,
                                                          bottom: 20.w),
                                                      decoration: BoxDecoration(
                                                          borderRadius:
                                                              BorderRadius.circular(
                                                                  2.r)),
                                                      child: (logic.dataList[index].isCheck ==
                                                              "1")
                                                          ? WxAssets
                                                              .images.checkOn3
                                                              .image(
                                                                  width: 16.w,
                                                                  height: 16.w)
                                                          : WxAssets.images
                                                              .checkOn3Wihte
                                                              .image(
                                                                  width: 16.w,
                                                                  height:
                                                                      16.w)),
                                                ),
                                              ),
                                              if (logic.dataList[index]
                                                      .videoLoadOK ==
                                                  "1")
                                                Positioned(
                                                  top: 0.w,
                                                  left: 0.w,
                                                  child: Container(
                                                      padding: EdgeInsets.only(
                                                          left: 10.w,
                                                          right: 10.w,
                                                          top: 4.w,
                                                          bottom: 4.w),
                                                      decoration: BoxDecoration(
                                                          gradient:
                                                              const LinearGradient(
                                                                  colors: [
                                                                Colours
                                                                    .color7732ED,
                                                                Colours
                                                                    .colorA555EF
                                                              ]),
                                                          borderRadius:
                                                              BorderRadius.only(
                                                                  topLeft: Radius
                                                                      .circular(
                                                                          8.r),
                                                                  bottomRight: Radius
                                                                      .circular(
                                                                          8.r))),
                                                      child: Text(
                                                        logic.dataList[index]
                                                                    .videoLoadOK ==
                                                                "1"
                                                            ? "已上传"
                                                            : "",
                                                        style: TextStyles
                                                            .regular
                                                            .copyWith(
                                                                fontSize: 10.sp,
                                                                color: Colours
                                                                    .white),
                                                      )),
                                                ),
                                              WxAssets.images.selfieShotPlay
                                                  .image(
                                                      width: 25.w, height: 25.w)
                                            ],
                                          ),
                                        ],
                                      ),
                                    );
                                  });
                                }),
                          ],
                        ),
                      ),
                    );
        }),
        bottomNavigationBar: Obx(() {
          return logic.dataList.isEmpty
              ? const SizedBox()
              //单人模式不管有没有选场地，都没有上传，多人对战只有选了场地才有上传
              : (logic.siteId.value == "0" ||
                      logic.siteId.value == "" ||
                      logic.type.value == "1")
                  ? Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            GestureDetector(
                              behavior: HitTestBehavior.translucent,
                              onTap: () {
                                var list = logic.dataList.where((value) {
                                  return value.isCheck == "1";
                                }).toList();
                                if (list.isEmpty) {
                                  WxLoading.showToast("请至少选择1个视频片段");
                                } else {
                                  getMyDialog(
                                    S.current.dialog_title,
                                    S.current.sure,
                                    content: "确认下载选中视频至手机相册？",
                                    () {
                                      AppPage.back();
                                      logic.loadVideo();
                                    },
                                    isShowClose: false,
                                    btnIsHorizontal: true,
                                    btnText2: S.current.cancel,
                                    onPressed2: () {
                                      AppPage.back();
                                    },
                                  );
                                }
                              },
                              child: Container(
                                height: 44.w,
                                margin: EdgeInsets.only(
                                    bottom: 25.w, top: 10.w, left: 15.w),
                                width: 165.w,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  color: Colours.color282735,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(28.r)),
                                  gradient: const LinearGradient(
                                    colors: [
                                      Colours.color7732ED,
                                      Colours.colorA555EF
                                    ],
                                    begin: Alignment.bottomLeft,
                                    end: Alignment.bottomRight,
                                  ),
                                ),
                                child: Text(
                                  "下载",
                                  style: TextStyles.semiBold14,
                                ),
                              ),
                            ),
                            GestureDetector(
                              behavior: HitTestBehavior.translucent,
                              onTap: () {
                                var list = logic.dataList.where((value) {
                                  return value.isCheck == "1";
                                }).toList();
                                if (list.isEmpty) {
                                  WxLoading.showToast("请至少选择1个视频片段");
                                } else {
                                  getMyDialog(
                                    "确认删除选中视频？",
                                    S.current.sure,
                                    content: "删除后报告中的视频也会被删除",
                                    () {
                                      AppPage.back();
                                      logic.deleteVideo();
                                    },
                                    isShowClose: false,
                                    btnIsHorizontal: true,
                                    btnText2: S.current.cancel,
                                    onPressed2: () {
                                      AppPage.back();
                                    },
                                  );
                                }
                              },
                              child: Container(
                                height: 44.w,
                                margin: EdgeInsets.only(
                                    bottom: 25.w, top: 10.w, right: 15.w),
                                width: 165.w,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(28.r)),
                                    border: Border.all(
                                        width: 1.w, color: Colours.white)),
                                child: Text(
                                  "删除",
                                  style: TextStyles.semiBold14
                                      .copyWith(color: Colours.cFF3F3F),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    )
                  : Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        //该气泡对每个用户只首次进入展示一次
                        if (logic.showUploadTip.value)
                          Container(
                            height: 27.w,
                            padding: EdgeInsets.only(
                                top: 7.w, left: 10.w, right: 10.w),
                            decoration: BoxDecoration(
                                color: Colors.transparent,
                                image: DecorationImage(
                                    image: WxAssets.images.dialogTipsBg
                                        .provider())),
                            child: Text(
                              "与其他球友共享你的精彩记录",
                              style: TextStyles.regular.copyWith(
                                  fontSize: 12.sp, color: Colours.color191921),
                            ),
                          ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            GestureDetector(
                              behavior: HitTestBehavior.translucent,
                              onTap: () {
                                var list = logic.dataList.where((value) {
                                  return value.isCheck == "1";
                                }).toList();
                                if (list.isEmpty) {
                                  WxLoading.showToast("请至少选择1个视频片段");
                                } else {
                                  getMyDialog(
                                    S.current.dialog_title,
                                    S.current.sure,
                                    content: "确认上传选中视频至场地？",
                                    () {
                                      AppPage.back();
                                      logic.uploadVideo();
                                    },
                                    isShowClose: false,
                                    btnIsHorizontal: true,
                                    btnText2: S.current.cancel,
                                    onPressed2: () {
                                      AppPage.back();
                                    },
                                  );
                                }
                              },
                              child: Container(
                                height: 44.w,
                                margin: EdgeInsets.only(
                                    bottom: 25.w, top: 10.w, left: 15.w),
                                width: 105.w,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  color: Colours.color282735,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(28.r)),
                                  gradient: const LinearGradient(
                                    colors: [
                                      Colours.color7732ED,
                                      Colours.colorA555EF
                                    ],
                                    begin: Alignment.bottomLeft,
                                    end: Alignment.bottomRight,
                                  ),
                                ),
                                child: Text(
                                  "上传",
                                  style: TextStyles.semiBold14,
                                ),
                              ),
                            ),
                            GestureDetector(
                              behavior: HitTestBehavior.translucent,
                              onTap: () {
                                var list = logic.dataList.where((value) {
                                  return value.isCheck == "1";
                                }).toList();
                                if (list.isEmpty) {
                                  WxLoading.showToast("请至少选择1个视频片段");
                                } else {
                                  getMyDialog(
                                    S.current.dialog_title,
                                    S.current.sure,
                                    content: "确认下载选中视频至手机相册？",
                                    () {
                                      AppPage.back();
                                      logic.loadVideo();
                                    },
                                    isShowClose: false,
                                    btnIsHorizontal: true,
                                    btnText2: S.current.cancel,
                                    onPressed2: () {
                                      AppPage.back();
                                    },
                                  );
                                }
                              },
                              child: Container(
                                height: 44.w,
                                margin: EdgeInsets.only(
                                  bottom: 25.w,
                                  top: 10.w,
                                ),
                                width: 105.w,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  color: Colours.color282735,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(28.r)),
                                  gradient: const LinearGradient(
                                    colors: [
                                      Colours.color7732ED,
                                      Colours.colorA555EF
                                    ],
                                    begin: Alignment.bottomLeft,
                                    end: Alignment.bottomRight,
                                  ),
                                ),
                                child: Text(
                                  "下载",
                                  style: TextStyles.semiBold14,
                                ),
                              ),
                            ),
                            GestureDetector(
                              behavior: HitTestBehavior.translucent,
                              onTap: () {
                                var list = logic.dataList.where((value) {
                                  return value.isCheck == "1";
                                }).toList();
                                if (list.isEmpty) {
                                  WxLoading.showToast("请至少选择1个视频片段");
                                } else {
                                  getMyDialog(
                                    "确认删除选中视频？",
                                    S.current.sure,
                                    content: "删除后报告中的视频也会被删除",
                                    () {
                                      AppPage.back();
                                      logic.deleteVideo();
                                    },
                                    isShowClose: false,
                                    btnIsHorizontal: true,
                                    btnText2: S.current.cancel,
                                    onPressed2: () {
                                      AppPage.back();
                                    },
                                  );
                                }
                              },
                              child: Container(
                                height: 44.w,
                                margin: EdgeInsets.only(
                                    bottom: 25.w, top: 10.w, right: 15.w),
                                width: 105.w,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(28.r)),
                                    border: Border.all(
                                        width: 1.w, color: Colours.white)),
                                child: Text(
                                  "删除",
                                  style: TextStyles.semiBold14
                                      .copyWith(color: Colours.cFF3F3F),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    );
        }));
  }
}
