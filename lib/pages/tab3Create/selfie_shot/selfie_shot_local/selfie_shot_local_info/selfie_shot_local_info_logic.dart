import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/database/app_database.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/selfie_shot_local_info_model.dart';
import 'package:shoot_z/network/model/shot_record_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab/upload/UploadController.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/utils/FileUtils.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/utils/utils.dart';

class SelfieShotLocalInfoLogic extends GetxController {
  RefreshController refreshController2 =
      RefreshController(initialRefresh: false);
  final uploadController = Get.find<UploadController>();
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  //数据列表
  var dataList = <ShotRecordModel>[].obs;
  var latestDate = "".obs;
  var siteId = "".obs;
  var siteName = "".obs;
  var trainingId = "".obs;
  var type = "1".obs; //1单人  2多人
  var allCheck = false.obs;
  StreamSubscription? subscription;
  var showUploadTip = true.obs;
  @override
  void onInit() async {
    super.onInit();
    final isShowed = await WxStorage.instance.getBool("showUploadTip") ?? false;
    showUploadTip.value = !isShowed;
    //自由半场 本地视频 单个比赛详情
    if (Get.arguments != null && Get.arguments.containsKey('latestDate')) {
      latestDate.value = Get.arguments['latestDate'];
    }
    if (Get.arguments != null && Get.arguments.containsKey('siteId')) {
      siteId.value = Get.arguments['siteId'];
    }
    if (Get.arguments != null && Get.arguments.containsKey('siteName')) {
      siteName.value = Get.arguments['siteName'];
    }
    if (Get.arguments != null && Get.arguments.containsKey('trainingId')) {
      trainingId.value = Get.arguments['trainingId'];
    }
    if (Get.arguments != null && Get.arguments.containsKey('type')) {
      type.value = Get.arguments['type'];
    }
    if (siteId.value != "0" && siteId.value != "" && type.value == "2") {
      WxStorage.instance.setBool("showUploadTip", true);
    }
    // BusUtils.instance.fire(EventAction(key: EventBusKey.uploadVideo));
    subscription = BusUtils.instance.on((action) {
      if (action.key == EventBusKey.uploadVideo) {
        getShootingRecords();
      } else if (action.key == EventBusKey.deleteLocalVideo1 ||
          action.key == EventBusKey.deleteLocalVideo2) {
        getdataList(controller: refreshController2, isLoad: false);
      }
    });
    getdataList(controller: refreshController2, isLoad: false);
  }

  //获得最新列表
  getdataList({
    isLoad = true,
    required RefreshController controller,
  }) async {
    final database =
        await $FloorAppDatabase.databaseBuilder('app_database.db').build();
    final selfieShotDao = database.selfieShotDao;
    List<ShotRecordModel> filteredNumbers = await selfieShotDao.findAllShot(
        trainingId.value,
        UserManager.instance.userInfo.value?.userId ?? "",
        type.value);
    var ss = filteredNumbers.map((e) {
      return e.copyWith(videoLoadOK: "0");
    }).toList();
    dataList.assignAll(ss);
    log("getShootingRecords12=${filteredNumbers}");
    getShootingRecords();
    // if (dataFag["isFrist"] as bool) {
    //   dataFag["isFrist"] = false;
    //   refresh();
    // }
    //TODO
    // var outputPath =
    //     "/storage/emulated/0/Android/data/com.shootZ.app.shoot_z/files/Movies/final_merge_1757422012563.mp4";
    // ShotRecordModel ssss = filteredNumbers.first;
    // ssss.filePath = outputPath;
    // ssss.eventId = "123123122231";
    // uploadController.addTasks([ssss]);
  }

  //获得投篮记录列表
  getShootingRecords() async {
    Map<String, dynamic> param = {
      'trainingId': trainingId.value,
      'hasVideos': 1, //是否只查询有视频的事件记录
    };
    var url = ApiUrl.getShootinEventVideos(trainingId.value);
    var res = await Api().get(url, queryParameters: param);
    log("getShootingRecords2=${jsonEncode(res.data)}");
    if (res.isSuccessful()) {
      if (res.data != null) {
        var list = res.data ?? [];
        var modelList =
            list.map((e) => SelfieShotLocalInfoModel.fromJson(e)).toList();
        log("zzzzzz12removeAt-${res.data}");
        log("getShootingRecords12=${modelList.length}");
        for (int i = 0; i < dataList.length; i++) {
          for (int a = 0; a < modelList.length; a++) {
            if (modelList[a].eventId == (dataList[i].eventId ?? "")) {
              //  log("getShootingRecords13=${modelList[a].eventId}-${dataList[i].eventId}");
              dataList[i].newworkFilePath = modelList[a].videoPath;
              dataList[i].videoLoadOK = "1";
            }
          }
        }
      }
      dataList.refresh();
    } else {
      WxLoading.showToast(res.message);
    }
    checkAllVideo(isCheck: false);
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  //全选和取消全选
  Future<void> checkAllVideo({bool? isCheck}) async {
    allCheck.value = isCheck ?? !allCheck.value;
    for (int i = 0; i < dataList.length; i++) {
      dataList[i].isCheck = (allCheck.value) ? "1" : "0";
    }
    dataList.refresh();
  }

  deleteVideo() async {
    final database =
        await $FloorAppDatabase.databaseBuilder('app_database.db').build();
    final selfieShotDao = database.selfieShotDao;
    var list = dataList.where((value) {
      return value.isCheck == "1";
    }).toList();
    for (int i = 0; i < list.length; i++) {
      if (list[i].isCheck == "1") {
        log("deleteVideo=${jsonEncode(list[i])}");
        selfieShotDao.deleteShot1(
            list[i].trainingId ?? "0",
            UserManager.instance.userInfo.value?.userId ?? "",
            list[i].eventId ?? "0");
        if (type.value == "1") {
          BusUtils.instance
              .fire(EventAction(key: EventBusKey.deleteLocalVideo1));
        } else {
          BusUtils.instance
              .fire(EventAction(key: EventBusKey.deleteLocalVideo2));
        }
        log("deleteVideo2=${type.value}");
        dataList.remove(list[i]);
        FileUtils.deleteFile(list[i].filePath ?? "", //list[i].filePath ??
            context: Get.context!,
            isShow: false);
        if (dataList.isEmpty) {
          AppPage.back();
        }
        //删除本地文件
      }

      // if (dataList[i].isCheck == "1") {
      //   selfieShotDao.deleteShot1(
      //       dataList[i].trainingId ?? "0",
      //       UserManager.instance.userInfo.value?.userId ?? "",
      //       dataList[i].eventId ?? "0");
      //   if (type.value == "1") {
      //     BusUtils.instance
      //         .fire(EventAction(key: EventBusKey.deleteLocalVideo1));
      //   } else {
      //     BusUtils.instance
      //         .fire(EventAction(key: EventBusKey.deleteLocalVideo2));
      //   }
      //   log("deleteVideo2=${type.value}");
      //   dataList.removeAt(i);
      //   FileUtils.deleteFile(dataList[i].filePath ?? "", context: Get.context!);
      //   //删除本地文件
      // }
    }
  }

  Future<void> uploadVideo() async {
    // // 申请权限
    // if (defaultTargetPlatform == TargetPlatform.android) {
    //   // 请求基本的前台服务权限
    //   var status = await Permission.notification.request();
    //   if (!status.isGranted) {
    //     print('前台服务权限被拒绝');
    //     return;
    //   }
    // }
    var list = dataList.where((value) {
      return value.isCheck == "1" && value.videoLoadOK != "1";
    }).toList();
    if (list.isEmpty) {
      WxLoading.showToast("请选择一个未上传的视频");
      return;
    }
    //uploadVideo=/storage/emulated/0/Android/data/com.shootZ.app.shoot_z/files/Movies/final_videos/extended_last_20250827_171729.mp4
    // var list2 = await Utils.listFiles(
    //     "/storage/emulated/0/Android/data/com.shootZ.app.shoot_z/files/Movies/final_videos");
    // log("uploadVideo1=$list2");
    uploadController.addTasks(list);
  }

//将视频保存到相册
  Future<void> loadVideo() async {
    var list = dataList.where((value) {
      return value.isCheck == "1";
    }).toList();
    Utils.localDownloadAndSaveToPhotoAlbum(list);
  }

  // // 获取视频缩略图（用于预览）
  // static Future<Uint8List?> getVideoThumbnail(String videoPath) async {
  //   try {
  //     final thumbnail = await VideoThumbnail.thumbnailData(
  //       video: videoPath,
  //       imageFormat: ImageFormat.JPEG,
  //       maxWidth: 200,
  //       quality: 75,
  //     );
  //     return thumbnail;
  //   } catch (e) {
  //     print('获取视频缩略图失败: $e');
  //     return null;
  //   }
  // }
  @override
  void dispose() {
    super.dispose();
    subscription?.cancel();
  }

  @override
  void onClose() {
    super.onClose();
    subscription?.cancel();
  }
}
