// ignore_for_file: avoid_print, deprecated_member_use

import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/database/app_database.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/shot_record_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:flutter/material.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot/list_items/item2/selfie_shot_item_logic2.dart';
import 'package:shoot_z/pages/tab3Create/selfie_shot/list_items/item1/selfie_shot_item_logic1.dart';

class SelfieShotLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  static const platform =
      MethodChannel('com.example.my_flutter_app/native_method');

  Future<void> _getResultToApp(Map<String, dynamic> param) async {
    try {
      await platform.invokeMethod('uploadResult', param);
    } on PlatformException catch (e) {
      print("native_method：Failed to invoke native method: '${e.message}'.");
    }
  }

  TabController? tabController;
  var tabbarIndex = 0.obs;
  final logic1 = Get.put(SelfieShotItemLogic1());
  final logic2 = Get.put(SelfieShotItemLogic2());
  // var uploadListLength = 0.obs;
  // var isEnd = false.obs;
  // var videolength = 0.obs;
  // // 文件上传状态
  // var fileUploadStatus2 = <String, FileUploadStatus2>{}.obs;

  // // 重试机制配置
  // static const maxRetryCount = 3;
  // static const initialRetryDelay = 1000; // 1秒
  // static const maxRetryDelay = Duration(minutes: 1);

  get tabNameList =>
      [S.current.selfile_shot_title2, S.current.selfile_shot_title1]; // 最大延迟1分钟
  var selectHalfCourt = [].obs; //场地id
  var venueName = ''.obs;
  var venueId = 0.obs;
  var datalist2 = <ShotRecordModel>[].obs;
  @override
  void onInit() {
    super.onInit();

    if (Get.arguments != null) {
      final arguments = Get.arguments as Map<String, dynamic>;
      if (arguments.containsKey("venueId")) {
        venueId.value = arguments['venueId'];
        venueName.value = arguments['venueName'];
        final halfCourt2 = Get.arguments['halfCourt'] is List
            ? Get.arguments['halfCourt']
            : [Get.arguments['halfCourt']];
        selectHalfCourt.addAll(halfCourt2);
      }
    }

    tabController = TabController(length: 2, vsync: this);
    tabController?.addListener(() {
      tabbarIndex.value = tabController?.index ?? 0;
      if (tabController?.indexIsChanging ?? false) {
        switch (tabbarIndex.value) {
          case 1:
            //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
            UserManager.instance.postApmTracking(1,
                remark: "点击tab切换",
                nowPage: Routes.selfieShotPage,
                subPage: "tabSelfieShot1",
                content: "切换tab自由投篮");
            break;
          case 0:
            //action int 动作 0:进入页面 100:退出界面 1:点击 2:滑动 3:输入 4:提交 5:分享 6:下载
            UserManager.instance.postApmTracking(1,
                remark: "点击tab切换",
                nowPage: Routes.selfieShotPage,
                subPage: "tabSelfieShot2",
                content: "切换tab单人训练");
            break;
        }
      }
    });
    getTeamMemberInfo();
  }

  @override
  void onReady() {
    super.onReady();
    logic1.setVenueData(venueId.value, venueName.value, selectHalfCourt);
    logic2.setVenueData(venueId.value, venueName.value, selectHalfCourt);
  }
  // showUploadListDialog(BuildContext context, String trainingId1) async {
  //   return showDialog<void>(
  //     context: context,
  //     barrierDismissible: true,
  //     builder: (BuildContext context2) {
  //       return WillPopScope(
  //         onWillPop: () async => false,
  //         child: Obx(() {
  //           return Padding(
  //             padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
  //             child: Material(
  //               type: MaterialType.transparency,
  //               child: Center(
  //                 child: ClipRRect(
  //                   borderRadius: BorderRadius.circular(8),
  //                   child: Column(
  //                     mainAxisAlignment: MainAxisAlignment.center,
  //                     mainAxisSize: MainAxisSize.min,
  //                     children: [
  //                       Container(
  //                         color: Colors.transparent,
  //                         child: Column(
  //                           children: [
  //                             Container(
  //                               constraints: BoxConstraints(
  //                                 maxHeight: 220.w,
  //                                 minHeight: 185.w,
  //                               ),
  //                               decoration: BoxDecoration(
  //                                 color: Colours.color191921,
  //                                 borderRadius: BorderRadius.only(
  //                                   bottomLeft: Radius.circular(25.r),
  //                                   bottomRight: Radius.circular(25.r),
  //                                 ),
  //                               ),
  //                               padding: const EdgeInsets.symmetric(
  //                                   horizontal: 18, vertical: 2),
  //                               width: double.infinity,
  //                               child: Column(
  //                                 mainAxisSize: MainAxisSize.min,
  //                                 crossAxisAlignment: CrossAxisAlignment.center,
  //                                 children: [
  //                                   const SizedBox(height: 30),
  //                                   Center(
  //                                     child: Text(
  //                                       "提示",
  //                                       style: TextStyles.regular.copyWith(
  //                                           color: Colours.white,
  //                                           fontSize: 18.sp),
  //                                     ),
  //                                   ),
  //                                   const SizedBox(height: 20),
  //                                   Text(
  //                                     "视频正在上传，剩余${uploadListLength.value}个视频",
  //                                     style: TextStyles.regular.copyWith(
  //                                         color: Colours.white,
  //                                         fontSize: 14.sp),
  //                                     maxLines: 20,
  //                                     overflow: TextOverflow.ellipsis,
  //                                   ),
  //                                   const SizedBox(height: 50),
  //                                   GestureDetector(
  //                                     behavior: HitTestBehavior.translucent,
  //                                     onTap: () {
  //                                       if (uploadListLength.value > 0) {
  //                                         WxLoading.showToast(
  //                                             "请稍等，剩余${uploadListLength.value}视频");
  //                                       } else {
  //                                         AppPage.back();
  //                                         Future.delayed(const Duration(
  //                                                 milliseconds: 100))
  //                                             .then((onValue) {
  //                                           getNextpage2(trainingId1, 0);
  //                                         });
  //                                       }
  //                                     },
  //                                     child: Container(
  //                                       height: 46.w,
  //                                       width: double.infinity,
  //                                       alignment: Alignment.center,
  //                                       margin: EdgeInsets.only(right: 10.w),
  //                                       padding: EdgeInsets.only(
  //                                           left: 5.w,
  //                                           right: 5.w,
  //                                           top: 3.w,
  //                                           bottom: 3.w),
  //                                       decoration: BoxDecoration(
  //                                         color: Colours.color282735,
  //                                         borderRadius: BorderRadius.all(
  //                                             Radius.circular(28.r)),
  //                                         gradient: uploadListLength.value <= 0
  //                                             ? const LinearGradient(
  //                                                 colors: [
  //                                                   Colours.color7732ED,
  //                                                   Colours.colorA555EF
  //                                                 ],
  //                                                 begin: Alignment.bottomLeft,
  //                                                 end: Alignment.bottomRight,
  //                                               )
  //                                             : null,
  //                                       ),
  //                                       child: Text(
  //                                         "生成报告",
  //                                         style: TextStyles.titleMedium18
  //                                             .copyWith(fontSize: 15.sp),
  //                                       ),
  //                                     ),
  //                                   ),
  //                                 ],
  //                               ),
  //                             ),
  //                           ],
  //                         ),
  //                       ),
  //                     ],
  //                   ),
  //                 ),
  //               ),
  //             ),
  //           );
  //         }),
  //       );
  //     },
  //   );
  // }

  void switchTab(index) {
    tabbarIndex.value = index;
  }

  getTeamMemberInfo() async {
    Map<String, dynamic> param = {};
    var res = await Api().get(ApiUrl.shootingSample, queryParameters: param);
    if (res.isSuccessful()) {
      log(jsonEncode(res.data));
      logic1.setVideos(res.data["path"] ?? "", res.data["cover"] ?? "");
      logic2.setVideos(res.data["path"] ?? "", res.data["cover"] ?? "");
    } else {
      WxLoading.showToast(res.message);
    }
  }

  getEndShooting(String trainingId1, int type) async {
    log("filteredNumbers2222:trainingId1=$trainingId1-userId=${UserManager.instance.userInfo.value?.userId ?? ""}---type=$type");

    // if (type == 1) isEnd.value = true;
    // log("halfShootingRecordingEnd1:$type${isEnd.value} uploadListLength.value${uploadListLength.value}");
    // if (type == 1 && isEnd.value && uploadListLength.value > 0) {
    //   log("halfShootingRecordingEnd11:$type ${isEnd.value} uploadListLength.value${uploadListLength.value}");
    //   showUploadListDialog(Get.context!, trainingId1);
    // } else if (type == 1) {
    //   getNextpage2(trainingId1, 1);
    // }
    if (type == 1) {
      if ((tabController?.index ?? 0) == 0) {
        await logic1.getHalfShootingRecordingEnd(trainingId1);
      } else {
        await logic2.getHalfShootingRecordingEnd(trainingId1);
      }
    }
  }

  postHalfShootingEvents(ShotRecordModel shotRecordModel) async {
    datalist2.add(shotRecordModel);
    // log("filteredNumbers2220:${jsonEncode(shotRecordModel)}-userId=${UserManager.instance.userInfo.value?.userId ?? ""}");
    log("postHalfShootingEvents:${datalist2.length}");
    //获得自由投篮单个事件
    final database =
        await $FloorAppDatabase.databaseBuilder('app_database.db').build();
    final selfieShotDao = database.selfieShotDao;
    shotRecordModel.venueId = venueId.value;
    shotRecordModel.venueName = venueName.value;
    int timestamp = DateTime.now().millisecondsSinceEpoch;
    shotRecordModel.startTime = timestamp.toString(); //时间戳
    shotRecordModel.goalTime =
        toMilliseconds(shotRecordModel.goalTime ?? 0.0) * 1.0; //时间戳
    shotRecordModel.shootTime =
        toMilliseconds(shotRecordModel.shootTime ?? 0.0) * 1.0; //时间戳
    await selfieShotDao.insertShot(
        shotRecordModel, UserManager.instance.userInfo.value?.userId ?? "");
    log("filteredNumbers2221:${jsonEncode(shotRecordModel)}");
  }

  /// 智能判断时间戳精度
  bool isMillisecond(double timestamp) {
    // 排除明显过大或过小的值
    if (timestamp < 1000000000) return false; // 2001年之前的秒级
    if (timestamp > 9999999999999) return false; // 超过公元3000年

    // 通过日期有效性验证
    try {
      final date = DateTime.fromMillisecondsSinceEpoch(timestamp.toInt());
      return date.year > 1970 && date.year < 2100;
    } catch (e) {
      return false;
    }
  }

  /// 统一转换为毫秒级
  int toMilliseconds(double timestamp) {
    return isMillisecond(timestamp)
        ? timestamp.toInt()
        : (timestamp * 1000).toInt();
  }
  // postHalfShootingResource(
  //     ShotRecordModel shotRecordModel, String uploadPath, int type) async {
  //   Map<String, dynamic> param = {
  //     "eventId": shotRecordModel.eventId,
  //     "path": uploadPath,
  //     "resType": type
  //   };

  //   var url =
  //       await ApiUrl.halfShootingResource(shotRecordModel.trainingId ?? "");
  //   var res = await Api().post(url, data: [param]);

  //   if (res.isSuccessful()) {
  //     log("资源上传成功: ${shotRecordModel.eventId} $uploadPath");
  //   } else {
  //     WxLoading.showToast(res.message);
  //   }
  // }

  // Future<void> uploadShotRecord(ShotRecordModel shotRecordModel) async {
  //   try {
  //     final tencentCosModel = await fetchSessionCredentials2();
  //     await Cos().forceInvalidationCredential();
  //     await Cos().initWithSessionCredential(FetchCredentials());
  //     final serviceConfig = pigeon.CosXmlServiceConfig(
  //       region: tencentCosModel.region ?? "ap-guangzhou",
  //       isDebuggable: true,
  //       isHttps: true,
  //     );

  //     final transferManager = await _initializeTransferManager(serviceConfig);
  //     // 并行处理视频和图片上传
  //     await Future.wait([
  //       _processFile(
  //         shotRecordModel,
  //         shotRecordModel.filePath,
  //         "video",
  //         "mobile/videos/",
  //         transferManager,
  //         tencentCosModel.bucketName ?? "",
  //         (url) => postHalfShootingResource(shotRecordModel, url, 2),
  //       ),
  //       _processFile(
  //         shotRecordModel,
  //         shotRecordModel.playerImagePath,
  //         "image",
  //         "mobile/image/",
  //         transferManager,
  //         tencentCosModel.bucketName ?? "",
  //         (url) => postHalfShootingResource(shotRecordModel, url, 1),
  //       ),
  //     ]);
  //   } catch (e) {
  //     log("uploadShotRecord error: $e");
  //     _getResultToApp({'success': false, 'message': '上传失败: $e'});

  //     // 失败后重新加入队列
  //     await Future.delayed(const Duration(seconds: 5));
  //     blockingQueue.enqueue(shotRecordModel);
  //   } finally {
  //     uploadListLength.value = await blockingQueue.getLength();
  //   }
  // }

  // Future<CosTransferManger> _initializeTransferManager(
  //     pigeon.CosXmlServiceConfig serviceConfig) async {
  //   Cos().registerDefaultService(serviceConfig);

  //   final transferConfig = pigeon.TransferConfig(
  //     forceSimpleUpload: false,
  //     enableVerification: true,
  //     divisionForUpload: 2097152,
  //     sliceSizeForUpload: 1048576,
  //   );

  //   await Cos().registerDefaultTransferManger(serviceConfig, transferConfig);
  //   return Cos().getDefaultTransferManger();
  // }

  // Future<void> _processFile(
  //   ShotRecordModel shotRecordModel,
  //   String? filePath,
  //   String type,
  //   String remotePrefix,
  //   CosTransferManger transferManager,
  //   String bucketName,
  //   Function(String) onSuccess,
  // ) async {
  //   if (filePath == null || filePath.isEmpty) {
  //     _getResultToApp({'success': false, 'message': '$type路径为空'});
  //     return;
  //   }

  //   // 检查文件是否已成功上传
  //   if (_isFileAlreadyUploaded(filePath)) {
  //     log("$type 文件已经成功上传，跳过: $filePath");
  //     return;
  //   }

  //   // 检查文件状态
  //   final fileStatus = fileUploadStatus2[filePath] ??
  //       FileUploadStatus2(
  //         path: filePath,
  //         status: UploadStatus2.pending,
  //         retryCount: 0,
  //         lastError: '',
  //       );

  //   // 检查文件是否存在
  //   if (!await _fileExists(filePath)) {
  //     _handleFileNotFound(type, filePath, fileStatus);
  //     return;
  //   }

  //   // 检查文件大小
  //   if (await _isZeroSizeFile(filePath)) {
  //     _handleZeroSizeFile(type, filePath, fileStatus);
  //     return;
  //   }

  //   // 生成远程存储路径
  //   final remoteKey = _generateRemoteKey(filePath, remotePrefix);

  //   try {
  //     await _uploadWithRetry(
  //       shotRecordModel,
  //       transferManager,
  //       bucketName,
  //       remoteKey,
  //       filePath,
  //       type,
  //       fileStatus,
  //       onSuccess,
  //     );
  //   } catch (e) {
  //     log("$type 上传失败，达到最大重试次数: $filePath, 错误: $e");
  //     _getResultToApp(
  //         {'success': false, 'message': '$type上传失败: ${e.toString()}'});

  //     // 将整个记录重新加入队列
  //     await Future.delayed(const Duration(seconds: 3));
  //     blockingQueue.enqueue(shotRecordModel);
  //   }
  // }

  // bool _isFileAlreadyUploaded(String filePath) {
  //   final status = fileUploadStatus2[filePath];
  //   return status != null && status.status == UploadStatus2.success;
  // }

  // void _handleFileNotFound(
  //     String type, String filePath, FileUploadStatus2 status) {
  //   log("$type文件不存在: $filePath");
  //   _getResultToApp({'success': false, 'message': '$type文件不存在: $filePath'});

  //   status.status = UploadStatus2.failed;
  //   status.retryCount = maxRetryCount;
  //   status.lastError = "文件不存在";
  //   fileUploadStatus2[filePath] = status;
  // }

  // void _handleZeroSizeFile(
  //     String type, String filePath, FileUploadStatus2 status) {
  //   log("$type文件大小为0: $filePath");
  //   _getResultToApp({'success': false, 'message': '$type文件大小为0: $filePath'});

  //   status.status = UploadStatus2.failed;
  //   status.retryCount = maxRetryCount;
  //   status.lastError = "文件大小为0";
  //   fileUploadStatus2[filePath] = status;
  // }

  // Future<void> _uploadWithRetry(
  //   ShotRecordModel shotRecordModel,
  //   CosTransferManger transferManager,
  //   String bucketName,
  //   String remoteKey,
  //   String filePath,
  //   String type,
  //   FileUploadStatus2 status,
  //   Function(String) onSuccess,
  // ) async {
  //   int attempt = 0;
  //   final random = cc.Random();

  //   // 设置初始状态
  //   status.status = UploadStatus2.retrying;
  //   status.retryCount = 0;
  //   status.lastError = '';
  //   fileUploadStatus2[filePath] = status;

  //   while (attempt < maxRetryCount) {
  //     attempt++;
  //     status.retryCount = attempt;
  //     fileUploadStatus2[filePath] = status;

  //     log("尝试上传 $type (第 $attempt 次): $filePath");

  //     try {
  //       // 执行上传
  //       await _uploadToCos(
  //           transferManager, bucketName, remoteKey, filePath, type, onSuccess);

  //       // 上传成功
  //       status.status = UploadStatus2.success;
  //       status.retryCount = 0;
  //       status.lastError = '';
  //       fileUploadStatus2[filePath] = status;

  //       log("$type 上传成功: $filePath");
  //       return;
  //     } catch (e) {
  //       // 更新状态
  //       status.lastError = e.toString();
  //       fileUploadStatus2[filePath] = status;

  //       log("$type 上传失败 (第 $attempt 次): $filePath, 错误: $e");

  //       if (attempt < maxRetryCount) {
  //         // 计算指数退避延迟
  //         final delay = _calculateRetryDelay(attempt, random);
  //         log("将在 ${delay.inMilliseconds} 毫秒后重试...");

  //         // 等待一段时间后重试
  //         await Future.delayed(delay);
  //       }
  //     }
  //   }

  //   // 达到最大重试次数仍然失败
  //   status.status = UploadStatus2.failed;
  //   fileUploadStatus2[filePath] = status;
  //   throw Exception("$type 上传失败，达到最大重试次数: $filePath");
  // }

  // Duration _calculateRetryDelay(int attempt, cc.Random random) {
  //   // 指数退避算法
  //   final baseDelay = initialRetryDelay * cc.pow(2, attempt - 1);

  //   // 添加随机抖动（避免所有客户端同时重试）
  //   final jitter = random.nextInt(500);

  //   // 限制最大延迟时间
  //   final delayMs = cc.min(baseDelay + jitter, maxRetryDelay.inMilliseconds);

  //   return Duration(milliseconds: delayMs.toInt());
  // }

  // Future<void> _uploadToCos(
  //   CosTransferManger transferManager,
  //   String bucketName,
  //   String remoteKey,
  //   String filePath,
  //   String type,
  //   Function(String) onSuccess,
  // ) async {
  //   final completer = Completer<pigeon.CosXmlResult>();

  //   // 创建结果监听器
  //   final listener = ResultListener((headers, result) {
  //     if (!completer.isCompleted) {
  //       completer.complete(result);
  //     }
  //   }, (clientException, serviceException) {
  //     if (!completer.isCompleted) {
  //       completer.completeError(
  //         clientException ?? serviceException ?? Exception('上传失败'),
  //       );
  //     }
  //   });

  //   // 创建上传任务
  //   final uploadTask = await transferManager.upload(
  //     bucketName,
  //     remoteKey,
  //     filePath: filePath,
  //     resultListener: listener,
  //   );

  //   // 等待上传完成
  //   try {
  //     final result = await completer.future;
  //     final url = result.accessUrl ?? "无法获取URL";

  //     log("$type 上传成功: $url");
  //     onSuccess(url);
  //     _getResultToApp(
  //         {'success': true, 'accessUrl': url, 'filePath': filePath});
  //   } catch (e) {
  //     log("$type 上传失败: $e");
  //     _getResultToApp(
  //         {'success': false, 'message': '$type上传失败: ${e.toString()}'});
  //     rethrow; // 重新抛出以便重试机制捕获
  //   }
  // }

  // Future<bool> _fileExists(String filePath) async {
  //   try {
  //     return await File(filePath).exists();
  //   } catch (e) {
  //     log("检查文件存在失败: $e");
  //     return false;
  //   }
  // }

  // Future<bool> _isZeroSizeFile(String filePath) async {
  //   try {
  //     final file = File(filePath);
  //     final length = await file.length();
  //     return length == 0;
  //   } catch (e) {
  //     log("检查文件大小失败: $e");
  //     return true;
  //   }
  // }

  // String _generateRemoteKey(String filePath, String prefix) {
  //   final fileExtension = filePath.substring(filePath.lastIndexOf("."));
  //   return "$prefix${DateTime.now().millisecondsSinceEpoch}$fileExtension";
  // }

  // Future<TencentCosModel> fetchSessionCredentials2() async {
  //   final httpClient = HttpClient();
  //   try {
  //     final request = await httpClient
  //         .getUrl(Uri.parse("https://i.shootz.tech/mgr-api/common/sts-data"));
  //     final response = await request.close();

  //     if (response.statusCode == HttpStatus.ok) {
  //       final json = await response.transform(utf8.decoder).join();
  //       return TencentCosModel.fromJson(jsonDecode(json));
  //     } else {
  //       throw Exception("获取临时密钥失败: ${response.statusCode}");
  //     }
  //   } catch (e) {
  //     log("fetchSessionCredentials2 error: $e");
  //     throw Exception("获取临时密钥失败: $e");
  //   } finally {
  //     httpClient.close();
  //   }
  // }
}

// enum UploadStatus2 {
//   pending, // 等待上传
//   uploading, // 上传中
//   success, // 上传成功
//   retrying, // 重试中
//   failed, // 上传失败
// }

// class FileUploadStatus2 {
//   final String path;
//   UploadStatus2 status;
//   int retryCount;
//   String lastError;

//   FileUploadStatus2({
//     required this.path,
//     this.status = UploadStatus2.pending,
//     this.retryCount = 0,
//     this.lastError = '',
//   });

//   @override
//   String toString() {
//     return 'FileUploadStatus2{path: $path, status: $status, retryCount: $retryCount, lastError: $lastError}';
//   }
// }







// // ignore_for_file: unused_local_variable

// import 'dart:async';
// import 'dart:convert';
// import 'dart:developer';
// import 'dart:io';
// import 'package:flutter/services.dart';
// import 'package:flutter_common/api.dart';
// import 'package:flutter_common/wx_loading.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:get/get.dart';
// import 'package:shoot_z/network/api_url.dart';
// import 'package:shoot_z/network/model/shot_record_model.dart';
// import 'package:shoot_z/network/model/tencent_cos_model.dart';
// import 'package:shoot_z/pages/shoot/selfie_shot/BlockingQueue.dart';
// import 'package:shoot_z/routes/app.dart';
// import 'package:shoot_z/utils/tencentcos/FetchCredentials.dart';
// import 'package:tencentcloud_cos_sdk_plugin/cos.dart';
// import 'package:tencentcloud_cos_sdk_plugin/cos_transfer_manger.dart';
// import 'package:tencentcloud_cos_sdk_plugin/pigeon.dart';
// import 'package:tencentcloud_cos_sdk_plugin/transfer_task.dart';
// import 'package:flutter/material.dart';
// import 'package:shoot_z/pages/shoot/selfie_shot/list_items/item1/selfie_shot_item_logic1.dart';
// import 'package:shoot_z/pages/shoot/selfie_shot/list_items/item2/selfie_shot_item_logic2.dart';
// import 'package:ui_packages/ui_packages.dart';

// class SelfieShotLogic extends GetxController
//     with GetSingleTickerProviderStateMixin {
//   static const platform =
//       MethodChannel('com.example.my_flutter_app/native_method');
//   Future<void> _getResultToApp(Map<String, dynamic> param) async {
//     try {
//       await platform.invokeMethod('uploadResult', param);
//     } on PlatformException catch (e) {
//       print("native_method：Failed to invoke native method: '${e.message}'.");
//     }
//   }

//   BlockingQueue<ShotRecordModel> blockingQueue = BlockingQueue(10);
//   TabController? tabController;
//   var tabbarIndex = 0.obs;
//   final logic1 = Get.put(SelfieShotItemLogic1());
//   final logic2 = Get.put(SelfieShotItemLogic2());
//   var uploadListLength = 0.obs;
//   // 'trainType': 2, //1单人 2多人
//   var isEnd = false.obs;
//   var videolength = 0.obs; //判断添加的长度
//   var isLoading = false.obs;
//   var isLoading2 = false.obs;
//   @override
//   void onInit() {
//     super.onInit();
//     tabController = TabController(length: 2, vsync: this);
//     tabController?.addListener(
//       () {
//         tabbarIndex.value = tabController?.index ?? 0;
//       },
//     );

//     getTeamMemberInfo();
//   }

// //弹出"版本更新"对话框
//   showUploadListDialog(BuildContext context, String trainingId1) async {
//     return showDialog<void>(
//         context: context,
//         barrierDismissible: true,
//         builder: (BuildContext context2) {
//           return WillPopScope(
//             onWillPop: () async {
//               print('返回了');
//               return false;
//             },
//             child: Obx(() {
//               return Padding(
//                 padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
//                 child: Material(
//                   type: MaterialType.transparency,
//                   color: Colors.transparent,
//                   child: Center(
//                     child: ClipRRect(
//                       borderRadius: BorderRadius.circular(8),
//                       child: Column(
//                         mainAxisAlignment: MainAxisAlignment.center,
//                         mainAxisSize: MainAxisSize.min,
//                         children: <Widget>[
//                           Container(
//                             color: Colors.transparent,
//                             child: Column(
//                               children: <Widget>[
//                                 Container(
//                                   alignment: Alignment.topLeft,
//                                   constraints: BoxConstraints(
//                                     maxHeight: 220.w,
//                                     minHeight: 185.w,
//                                   ),
//                                   decoration: BoxDecoration(
//                                     color: Colours.color191921,
//                                     borderRadius: BorderRadius.only(
//                                       bottomLeft: Radius.circular(25.r),
//                                       bottomRight: Radius.circular(25.r),
//                                     ),
//                                   ),
//                                   padding: const EdgeInsets.symmetric(
//                                       horizontal: 18, vertical: 2),
//                                   width: double.infinity,
//                                   child: Column(
//                                     mainAxisSize: MainAxisSize.min,
//                                     crossAxisAlignment:
//                                         CrossAxisAlignment.center,
//                                     children: <Widget>[
//                                       const SizedBox(
//                                         height: 30,
//                                       ),
//                                       Center(
//                                         child: Text(
//                                           ("提示"), //"1.修复了一些BUG;\n2.更新部分内容",//
//                                           //  "1.修复了一些BUG;\n2.更新部分内容" * 20,
//                                           style: TextStyles.regular.copyWith(
//                                               color: Colours.white,
//                                               fontSize: 18.sp),
//                                           maxLines: 1,
//                                           overflow: TextOverflow.ellipsis,
//                                         ),
//                                       ),
//                                       const SizedBox(
//                                         height: 20,
//                                       ),
//                                       Text(
//                                         ("视频正在上传，剩余${uploadListLength.value}个视频"), //"1.修复了一些BUG;\n2.更新部分内容",//
//                                         //  "1.修复了一些BUG;\n2.更新部分内容" * 20,
//                                         style: TextStyles.regular.copyWith(
//                                             color: Colours.white,
//                                             fontSize: 14.sp),
//                                         maxLines: 20,
//                                         overflow: TextOverflow.ellipsis,
//                                       ),
//                                       const SizedBox(
//                                         height: 50,
//                                       ),
//                                       GestureDetector(
//                                         behavior: HitTestBehavior.translucent,
//                                         onTap: () {
//                                           if (uploadListLength.value > 0) {
//                                             WxLoading.showToast(
//                                                 "请稍等，剩余${uploadListLength.value}视频");
//                                           } else {
//                                             //  Navigator.of(context2).pop();
//                                             AppPage.back();
//                                             getNextpage2(trainingId1, 0);
//                                           }
//                                         },
//                                         child: Container(
//                                           height: 46.w,
//                                           width: double.infinity,
//                                           alignment: Alignment.center,
//                                           margin: EdgeInsets.only(right: 10.w),
//                                           padding: EdgeInsets.only(
//                                               left: 5.w,
//                                               right: 5.w,
//                                               top: 3.w,
//                                               bottom: 3.w),
//                                           decoration: BoxDecoration(
//                                             color: Colours.color282735,
//                                             borderRadius: BorderRadius.all(
//                                                 Radius.circular(28.r)),
//                                             gradient: uploadListLength.value <=
//                                                     0
//                                                 ? const LinearGradient(
//                                                     colors: [
//                                                       Colours.color7732ED,
//                                                       Colours.colorA555EF
//                                                     ],
//                                                     begin: Alignment.bottomLeft,
//                                                     end: Alignment.bottomRight,
//                                                   )
//                                                 : null,
//                                           ),
//                                           child: Text(
//                                             "生成报告",
//                                             style: TextStyles.titleMedium18
//                                                 .copyWith(fontSize: 15.sp),
//                                           ),
//                                         ),
//                                       ),
//                                       const SizedBox(
//                                         height: 10,
//                                       ),
//                                     ],
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),
//                 ),
//               );
//             }),
//           );
//         });
//   }

//   @override
//   void onReady() {
//     super.onReady();
//     getUploadqueue();
//   }

//   getUploadqueue() async {
//     while (true) {
//       // var length2 = await blockingQueue.getLength();
//       // uploadListLength.value = length2;
//       var data = await blockingQueue.dequeue();
//       // var ishave = await checkIfFileExists(data.filePath ?? "");
//       // var ishave2 = await checkIfFileExists(data.playerImagePath ?? "");
//       // log("uploadVideo0ishave=${ishave}-${data.filePath}");
//       // log("uploadVideo0ishave1=${ishave2}-${data.playerImagePath}");
//       // if (ishave && ishave2) {
//       //   await getUpLoadVideoCos(data);
//       // }
//       await getUpLoadVideoCos(data);
//     }
//   }

//   void switchTab(index) {
//     tabbarIndex.value = index;
//   }

//   //获得球员资料
//   getTeamMemberInfo() async {
//     Map<String, dynamic> param = {};
//     var res = await Api().get(ApiUrl.shootingSample, queryParameters: param);
//     if (res.isSuccessful()) {
//       //{"path":"https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/video/0220xbot_test.mp4"}
//       log(jsonEncode(res.data));
//       logic1.setVideos(res.data["path"] ?? "", res.data["cover"] ?? "");
//       logic2.setVideos(res.data["path"] ?? "", res.data["cover"] ?? "");
//     } else {
//       WxLoading.showToast(res.message);
//     }
//   }

//   //关闭自由投篮
//   getEndShooting(String trainingId1) async {
//     isEnd.value = true;
//     getNextpage(trainingId1);
//   }

//   getNextpage(String trainingId1) async {
//     log("postHalfShootingEvents05=$trainingId1--$trainingId1--${isEnd.value}");
//     // var length2 = await blockingQueue.getLength();
//     // log("getNextpage31=$trainingId1--$length2");
//     if (isEnd.value && uploadListLength.value > 0) {
//       log("getNextpage32=$trainingId1--${uploadListLength.value}");

//       showUploadListDialog(Get.context!, trainingId1);
//     } else {
//       getNextpage2(trainingId1, 1);
//     }
//   }

//   getNextpage2(String trainingId1, int type) async {
//     if ((tabController?.index ?? 0) == 0) {
//       await logic1.getHalfShootingRecordingEnd(trainingId1);
//     } else {
//       await logic2.getHalfShootingRecordingEnd(trainingId1);
//     }
//     log("halfShootingRecordingEnd:${videolength.value}");
//     if (videolength.value > 0 && uploadListLength.value == 0) {
//       videolength.value = 0;
//       isEnd.value = false;
//       await Future.delayed(const Duration(milliseconds: 50));
//       if ((tabController?.index ?? 0) == 0) {
//         logic1.getNextPage2(trainingId1);
//       } else {
//         logic2.getNextPage2(trainingId1);
//       }
//     }
//     // else if (videolength.value == 0 && type == 0) {
//     //   AppPage.back();
//     // }
//   }

//   //上传事件
//   postHalfShootingEvents(ShotRecordModel shotRecordModel) async {
//     videolength.value = videolength.value + 1;
//     Map<String, dynamic> param = {
//       "eventId": shotRecordModel.eventId,
//       "goalTime": (shotRecordModel.goalTime ?? "0.0").toString(),
//       "hit": shotRecordModel.isGoal ?? false,
//       "playerImage": "",
//       "shootTime": shotRecordModel.startTime,
//       "shootX": (shotRecordModel.shootCoord?.length ?? 0) > 0
//           ? shotRecordModel.shootCoord?.first
//           : 0.0,
//       "shootY": (shotRecordModel.shootCoord?.length ?? 0) > 1
//           ? shotRecordModel.shootCoord?.last
//           : 0.0,
//       "shotType": 1,
//       "videoPath": "",
//     };
//     var aar = [];
//     aar.add(param);
//     var url = await ApiUrl.halfShootingEvents(shotRecordModel.trainingId ?? "");
//     log("postHalfShootingEvents=${param}");
//     _getResultToApp(
//         {'success': false, 'message': 'postHalfShootingEventsParams:$param'});
//     var res = await Api().post(url, data: aar);
//     log("postHalfShootingEvents01=${param}-${jsonEncode(res.data)}");
//     if (res.isSuccessful()) {
//       blockingQueue.enqueue(shotRecordModel);
//       var length2 = await blockingQueue.getLength();
//       uploadListLength.value = length2;
//       log("uploadVideo111=${uploadListLength.value}");
//     } else {
//       WxLoading.showToast(res.message);
//     }
//   }

//   //添加资源
//   postHalfShootingResource(
//       ShotRecordModel shotRecordModel, String uploadPath, int type) async {
//     Map<String, dynamic> param = {
//       "eventId": shotRecordModel.eventId,
//       "path": uploadPath,
//       "resType": type //1 照片； 2 视频地址
//     };
//     var aar = [];
//     aar.add(param);
//     log("postHalfShootingResource=${param}");
//     _getResultToApp(
//         {'success': false, 'message': 'postHalfShootingResourceParams:$param'});
//     var url =
//         await ApiUrl.halfShootingResource(shotRecordModel.trainingId ?? "");
//     var res = await Api().post(url, data: aar);
//     if (res.isSuccessful()) {
//       log("postHalfShootingResource${jsonEncode(res.data)}");
//     } else {
//       WxLoading.showToast(res.message);
//     }
//   }

//   Future<void> getUpLoadVideoCos(ShotRecordModel shotRecordModel) async {
//     TencentCosModel tencentCosModel = await fetchSessionCredentials2();
//     await Cos().forceInvalidationCredential(); //强制使本地保存的临时密钥失效
//     await Cos().initWithSessionCredential(FetchCredentials());
//     log("tencentCosModel=${jsonEncode(tencentCosModel)}");
// // 存储桶所在地域简称，例如广州地区是 ap-guangzhou
//     String region = tencentCosModel.region ?? "ap-guangzhou";
// // 创建 CosXmlServiceConfig 对象，根据需要修改默认的配置参数
//     CosXmlServiceConfig serviceConfig = CosXmlServiceConfig(
//       region: region,
//       isDebuggable: true,
//       isHttps: true,
//     );
// // 注册默认 COS Service
//     Cos().registerDefaultService(serviceConfig);
// // 创建 TransferConfig 对象，根据需要修改默认的配置参数
//     // TransferConfig 可以设置智能分块阈值 默认对大于或等于2M的文件自动进行分块上传，可以通过如下代码修改分块阈值
//     TransferConfig transferConfig = TransferConfig(
//       forceSimpleUpload: false,
//       enableVerification: true,
//       divisionForUpload: 2097152, // 设置大于等于 2M 的文件进行分块上传
//       sliceSizeForUpload: 1048576, //设置默认分块大小为 1M
//     );
// // 注册默认 COS TransferManger
//     await Cos().registerDefaultTransferManger(serviceConfig, transferConfig);
//     await Future.delayed(const Duration(milliseconds: 1300));
// // 也可以通过 registerService 和 registerTransferManger 注册其他实例， 用于后续调用
// // 一般用 region 作为注册的key
//     // String newRegion = tencentCosModel.region ?? "ap-guangzhou";
//     // Cos().registerService(newRegion, serviceConfig..region = newRegion);
//     // Cos().registerTransferManger(
//     //     newRegion, serviceConfig..region = newRegion, transferConfig);

//     // "/storage/emulated/0/Movies/1747040142000.mp4";
//     var ishave = await checkIfFileExists(shotRecordModel.filePath ?? "");
//     if (ishave) {
//       _getResultToApp(
//           {'success': false, 'message': '视频地址存在:${shotRecordModel.filePath}'});
//       if (shotRecordModel.videoLoadOK != "1") {
//         var end2 = shotRecordModel.filePath
//             ?.substring(shotRecordModel.filePath!.lastIndexOf("."));
//         final remoteKey =
//             "mobile/videos/${DateTime.now().millisecondsSinceEpoch}${end2}";
//         await uploadVideo(
//             shotRecordModel, remoteKey, tencentCosModel.bucketName ?? "");
//       }
//     } else {
//       _getResultToApp(
//           {'success': false, 'message': '视频地址不存在:${shotRecordModel.filePath}'});
//       var length2 = await blockingQueue.getLength();
//       uploadListLength.value = length2;
//     }

//     var ishave2 =
//         await checkIfFileExists(shotRecordModel.playerImagePath ?? "");
//     log("postHalfShootingEvents02=视频地址存在：${ishave}-图片地址存在：${ishave2}");
//     if (ishave2) {
//       _getResultToApp({
//         'success': false,
//         'message': '图片地址存在:${shotRecordModel.playerImagePath}'
//       });
//       if (shotRecordModel.imgLoadOK != "1") {
//         var end2 = shotRecordModel.playerImagePath
//             ?.substring(shotRecordModel.playerImagePath!.lastIndexOf("."));
//         final remoteKey =
//             "mobile/image/${DateTime.now().millisecondsSinceEpoch}${end2}";
//         await uploadImage(
//             shotRecordModel, remoteKey, tencentCosModel.bucketName ?? "");
//       }
//     } else {
//       _getResultToApp({
//         'success': false,
//         'message': '身型图片地址不存在:${shotRecordModel.playerImagePath}'
//       });
//       var length2 = await blockingQueue.getLength();
//       uploadListLength.value = length2;
//     }
//   }

//   Future<bool> checkIfFileExists(String filePath) async {
//     // 构建目标文件路径
//     String fullPath = filePath;

//     // 检查文件是否存在
//     final file = File(fullPath);
//     bool exists = await file.exists();

//     if (exists) {
//       print("文件存在: $fullPath");
//     } else {
//       print("文件不存在: $fullPath");
//     }
//     return await file.exists();
//   }

//   Future<void> uploadVideo(ShotRecordModel shotRecordModel, String remoteKey,
//       String bucketName) async {
//     // 获取 TransferManager
//     CosTransferManger transferManager = Cos().getDefaultTransferManger();
//     //CosTransferManger transferManager = Cos().getTransferManger("newRegion");
//     // 存储桶名称，由 bucketname-appid 组成，appid 必须填入，可以在 COS 控制台查看存储桶名称。 https://console.cloud.tencent.com/cos5/bucket
//     String bucket = bucketName;
//     String cosPath = remoteKey; //对象在存储桶中的位置标识符，即称对象键
//     String srcPath = shotRecordModel.filePath ?? ""; //本地文件的绝对路径
//     //若存在初始化分块上传的 UploadId，则赋值对应的 uploadId 值用于续传；否则，赋值 null
//     String? _uploadId;
//     log("postHalfShootingEvents03=uploadVideo1  ${jsonEncode(shotRecordModel)}");
//     // 上传成功回调
//     successCallBack(Map<String?, String?>? header, CosXmlResult? result) async {
//       // todo 上传成功后的逻辑
//       //  log("uploadVideo11=${result?.accessUrl}");
//       log("postHalfShootingEvents03=uploadVideo2   ${result?.accessUrl}");
//       _getResultToApp({
//         'success': true,
//         'accessUrl': result?.accessUrl,
//         'filePath': shotRecordModel.filePath
//       });
//       postHalfShootingResource(shotRecordModel, result?.accessUrl ?? "", 2);
//       var length2 = await blockingQueue.getLength();
//       uploadListLength.value = length2;
//       log("uploadVideo11=${uploadListLength.value}");
//     }

//     //上传失败回调
//     failCallBack(clientException, serviceException) async {
//       // todo 上传失败后的逻辑
//       _getResultToApp({
//         'success': false,
//         'message':
//             "上传失败：${clientException.message}serviceException:$serviceException本地地址：${shotRecordModel.filePath}"
//       });
//       log("postHalfShootingEvents03=clientException=${clientException.message}-${clientException.errorCode}-${clientException.details}\nserviceException=${serviceException}");
//       // var shotRecordModel2 = shotRecordModel;
//       // shotRecordModel2.imgLoadOK = "1";
//       // blockingQueue.enqueue(shotRecordModel2);
//       var length2 = await blockingQueue.getLength();
//       uploadListLength.value = length2;
//     }

//     //上传状态回调, 可以查看任务过程
//     stateCallback(state) {
//       // todo notify transfer state
//       log("postHalfShootingEvents03=${state}");
//     }

//     //上传进度回调
//     progressCallBack(complete, target) {
//       // todo Do something to update progress...
//       //log("uploadVideo4=${complete}-${target}");
//     }

//     //初始化分块完成回调
//     initMultipleUploadCallback(String bucket, String cosKey, String uploadId) {
//       //用于下次续传上传的 uploadId
//       _uploadId = uploadId;
//       //log("uploadVideo5=${bucket}-${cosKey}-${uploadId}");
//     }

//     //开始上传
//     TransferTask transferTask = await transferManager.upload(bucket, cosPath,
//         filePath: srcPath,
//         uploadId: _uploadId,
//         resultListener: ResultListener(successCallBack, failCallBack),
//         stateCallback: stateCallback,
//         progressCallBack: progressCallBack,
//         initMultipleUploadCallback: initMultipleUploadCallback);
//     //暂停任务
//     //transferTask.pause();
//     //恢复任务
//     //transferTask.resume();
//     //取消任务
//     //transferTask.cancel();
//   }

//   Future<void> uploadImage(ShotRecordModel shotRecordModel, String remoteKey,
//       String bucketName) async {
//     // 获取 TransferManager
//     CosTransferManger transferManager = Cos().getDefaultTransferManger();
//     //CosTransferManger transferManager = Cos().getTransferManger("newRegion");
//     // 存储桶名称，由 bucketname-appid 组成，appid 必须填入，可以在 COS 控制台查看存储桶名称。 https://console.cloud.tencent.com/cos5/bucket
//     String bucket = bucketName;
//     String cosPath = remoteKey; //对象在存储桶中的位置标识符，即称对象键
//     String srcPath = shotRecordModel.playerImagePath ?? ""; //本地文件的绝对路径
//     //若存在初始化分块上传的 UploadId，则赋值对应的 uploadId 值用于续传；否则，赋值 null
//     String? _uploadId;
//     log("postHalfShootingEvents04=uploadImage1 ${jsonEncode(shotRecordModel)}");
//     // 上传成功回调
//     successCallBack(Map<String?, String?>? header, CosXmlResult? result) async {
//       // todo 上传成功后的逻辑
//       log("postHalfShootingEvents04=uploadImage2 ${result?.accessUrl}");
//       _getResultToApp({
//         'success': true,
//         'accessUrl': result?.accessUrl,
//         'filePath': shotRecordModel.playerImagePath
//       });
//       await postHalfShootingResource(
//           shotRecordModel, result?.accessUrl ?? "", 1);
//       var length2 = await blockingQueue.getLength();
//       uploadListLength.value = length2;
//     }

//     //上传失败回调
//     failCallBack(clientException, serviceException) async {
//       // todo 上传失败后的逻辑
//       log("postHalfShootingEvents04=clientException=${clientException.message}-${clientException.errorCode}-${clientException.details}\nserviceException=${serviceException}");
//       _getResultToApp({
//         'success': false,
//         'message':
//             "上传失败：${clientException.message}本地地址：${shotRecordModel.playerImagePath}"
//       });
//       // var shotRecordModel2 = shotRecordModel;
//       // shotRecordModel2.videoLoadOK = "1";
//       // blockingQueue.enqueue(shotRecordModel2);
//       var length2 = await blockingQueue.getLength();
//       uploadListLength.value = length2;
//     }

//     //上传状态回调, 可以查看任务过程
//     stateCallback(state) {
//       // todo notify transfer state
//       log("uploadVideo3img=${state}");
//     }

//     //上传进度回调
//     progressCallBack(complete, target) {
//       // todo Do something to update progress...
//       //log("uploadVideo4=${complete}-${target}");
//     }

//     //初始化分块完成回调
//     initMultipleUploadCallback(String bucket, String cosKey, String uploadId) {
//       //用于下次续传上传的 uploadId
//       _uploadId = uploadId;
//       //log("uploadVideo5=${bucket}-${cosKey}-${uploadId}");
//     }

//     //开始上传
//     TransferTask transferTask = await transferManager.upload(bucket, cosPath,
//         filePath: srcPath,
//         uploadId: _uploadId,
//         resultListener: ResultListener(successCallBack, failCallBack),
//         stateCallback: stateCallback,
//         progressCallBack: progressCallBack,
//         initMultipleUploadCallback: initMultipleUploadCallback);
//     //暂停任务
//     //transferTask.pause();
//     //恢复任务
//     //transferTask.resume();
//     //取消任务
//     //transferTask.cancel();
//   }

//   Future<TencentCosModel> fetchSessionCredentials2() async {
//     // 首先从您的临时密钥服务器获取包含了密钥信息的响应，例如：
//     var httpClient = HttpClient();
//     try {
//       // 临时密钥服务器 url，临时密钥生成服务请参考 https://cloud.tencent.com/document/product/436/14048
//       var stsUrl = "https://i.shootz.tech/mgr-api/common/sts-data";
//       var request = await httpClient.getUrl(Uri.parse(stsUrl));
//       var response = await request.close();
//       if (response.statusCode == HttpStatus.OK) {
//         var json = await response.transform(utf8.decoder).join();
//         print(jsonEncode(json));
//         // 然后解析响应，获取临时密钥信息
//         var data = jsonDecode(json);
//         // 最后返回临时密钥信息对象
//         TencentCosModel tencentCosModel = TencentCosModel.fromJson(data);
//         return tencentCosModel;
//       } else {
//         throw ArgumentError();
//       }
//     } catch (exception) {
//       throw ArgumentError();
//     }
//   }
// }
// ignore_for_file: unused_local_variable
