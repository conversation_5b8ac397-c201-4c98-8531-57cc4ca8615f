import 'package:get/get.dart';
import 'package:shoot_z/pages/tab3Create/shoot_composite/venue_goal_tab/item1/venue_goal_item_logic1.dart';
import 'package:shoot_z/pages/tab3Create/shoot_composite/venue_goal_tab/item2/venue_goal_item_logic2.dart';
import 'package:shoot_z/pages/tab3Create/shoot_composite/venue_goal_tab/venue_goal_tab_logic.dart';

class VenueGoalTabBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => VenueGoalTabLogic());
    Get.lazyPut(() => VenueGoalItemLogic1());
    Get.lazyPut(() => VenueGoalItemLogic2());
  }
}
