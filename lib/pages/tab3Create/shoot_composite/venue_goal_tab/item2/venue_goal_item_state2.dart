import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/model/venue_goal_model.dart';

class VenueGoalItemState2 {
  TextEditingController codeController = TextEditingController();

  //推荐时段
  var dateRecommendedList = [
    "22:00~23:00",
    "21:00~22:00",
    "20:00~21:00",
    "19:00~20:00",
    "18:00~19:00",
    "17:00~18:00",
    "16:00~17:00",
    "15:00~16:00",
    "14:00~15:00",
    "13:00~14:00",
    "12:00~13:00",
    "11:00~12:00",
    "10:00~11:00",
    "9:00~10:00",
    "8:00~9:00",
  ];
  //推荐时段快速选择 4个时间  推荐时段提供当前已选时段的前半小时、前一小时、后半小时、后一小时 区间快捷选择，若已选时段结束时间为生成至时间，则不提供后半小时和一小时的推荐区间
  var dateFristList2 = [];
  //精准查找时间断
  var datePrecisionList = [];

  //推荐时段选择今天还是昨天
  var todayRecommended = true.obs;
  var indexVideoId = "".obs;
  var indexVideoLibrary = 9999.obs; //选择视频下标

  var videoDate = "".obs;
  var videoStartTime = "".obs;
  var videoEndTime = "".obs;
  var videoNextStartTime = "".obs;
  var videoNextEndTime = "".obs;

  var compositeOption2 = ["0", "0", "0", "0"].obs; //视频效果与个性化 0选中  1选中 多选
  var rememberOption = "0".obs; //记住我的选择
  var deleteOption = true.obs; //记住我的选择
  var isShare = false.obs; //是否可以分享 0不能 1可以

  //精确查找预选时间
  var videoDate1 = "".obs;
  var videoStartTime1 = "".obs;
  var videoEndTime1 = "".obs;

  // 响应式列表
  final RxList<VenueGoalModel> dataList1 = <VenueGoalModel>[].obs;
  final RxList<VenueGoalModel> dataList2 = <VenueGoalModel>[].obs;

  // 存储选中项目的唯一列表（自动去重）
  final RxList<VenueGoalModel> checkedList = <VenueGoalModel>[].obs;
  RxList<VenueGoalModel> checkedList3 = <VenueGoalModel>[].obs;
  // 计算选中总数（直接使用checkedList长度）
  RxInt get totalCheckedCount => checkedList.length.obs;
  // 切换选中状态
  void toggleCheck(VenueGoalModel item) {
    if (checkedList.any((e) => e.id == item.id)) {
      // 已存在则移除
      checkedList.removeWhere((e) => e.id == item.id);
    } else {
      // 不存在则添加
      checkedList.add(item);
    }
    // 同步更新item的isCheck状态
    item.isCheck.toggle();
  }

// 是否全部选中（计算属性）
  RxBool get isAllSelectedLibrary {
    final selectedCount =
        checkedList3.where((item) => item.isCheck.value).length;
    return (selectedCount == checkedList3.length).obs;
  }

  // 全选/取消全选
  void toggleSelectAllLibrary(bool select) {
    for (var item in checkedList3) {
      item.isCheck.value = select;
    }
    checkedList3.refresh();
  }

  void toggleSelectDeleteLibrary() {
    // 1. 获取checkedList2中所有选中的ID
    final checkedIds = checkedList3
        .where((item) => item.isCheck.value)
        .map((item) => item.id)
        .toSet();
    checkedList3.removeWhere((item) => checkedIds.contains(item.id));
    // 2. 从checkedList中移除匹配项
    checkedList.removeWhere((item) => checkedIds.contains(item.id));
    for (final item in dataList1) {
      if (checkedIds.contains(item.id)) {
        item.isCheck.value = false; // 同步选中状态
      }
    }
    for (final item in dataList2) {
      if (checkedIds.contains(item.id)) {
        item.isCheck.value = false; // 同步选中状态
      }
    }
  }
  // // 加载数据并同步选中状态
  // void loadDataWithCheckedState(List<VenueGoalModel> newData) {
  //   // 1. 保存当前所有选中ID
  //   final checkedIds = checkedList.map((item) => item.id).toSet();

  //   // 2. 更新数据列表并恢复选中状态
  //   dataList1.assignAll(newData.map((newItem) {
  //     // 如果新数据的ID在已选中列表中，恢复选中状态
  //     if (checkedIds.contains(newItem.id)) {
  //       return newItem.copyWith(isCheck: true);
  //     }
  //     return newItem;
  //   }));

  //   // 3. 更新checkedList（移除不存在的数据）
  //   checkedList.removeWhere((checkedItem) =>
  //       !newData.any((newItem) => newItem.id == checkedItem.id));
  // }

  // // 切换选中状态
  // void toggleCheck(VenueGoalModel item) {
  //   if (checkedList.any((e) => e.id == item.id)) {
  //     checkedList.removeWhere((e) => e.id == item.id);
  //     item.isCheck.value = false;
  //   } else {
  //     checkedList.add(item);
  //     item.isCheck.value = true;
  //   }
  // }
}
