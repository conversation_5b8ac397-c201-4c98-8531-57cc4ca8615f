// ignore_for_file: avoid_print, deprecated_member_use

import 'dart:developer';

import 'package:get/get.dart';
import 'package:flutter/material.dart';

class VenueGoalTabLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  TabController? tabController;
  var tabbarIndex = 0.obs;
  get tabNameList => ["横屏", "竖屏"]; // 最大延迟1分钟
  var venueId = 0.obs; //场地id
  @override
  void onInit() {
    super.onInit();
    venueId.value = Get.arguments['venueId']; //场地id
    tabController = TabController(length: 2, vsync: this);
    tabController?.addListener(() {
      tabbarIndex.value = tabController?.index ?? 0;
    });
  }

  @override
  void onReady() {
    super.onReady();
    log("setVenueData10=${venueId.value}");
    // logic1.setVenueData(
    //     venueId.value, venueName.value, halfCourt, videoEndTime.value);
    // logic2.setVenueData(
    //     venueId.value, venueName.value, halfCourt, videoEndTime.value);
  }

  void switchTab(index) {
    tabbarIndex.value = index;
  }
}
