// ignore_for_file: avoid_print

import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_cupertino_datetime_picker/flutter_cupertino_datetime_picker.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/half_court_model.dart';
import 'package:shoot_z/network/model/invite_mini_path_model.dart';
import 'package:shoot_z/network/model/shot_record_model.dart';
import 'package:shoot_z/network/model/venue_goal_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab/upload/CosUploadServiceSingle.dart';
import 'package:shoot_z/pages/tab3Create/shoot_composite/venue_goal/venue_goal_state.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/FileDownloadManager.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/utils/merge/SerialTaskQueue.dart';
import 'package:shoot_z/utils/merge/VideoMergeTool.dart';
import 'package:shoot_z/utils/myShareH5.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/exclusive_dialog.dart';
import 'package:shoot_z/widgets/video/exclusive_dialog2.dart';
import 'package:shoot_z/widgets/video/video_controller.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:intl/intl.dart';

class VenueGoalLogic extends GetxController
    with WidgetsBindingObserver, GetTickerProviderStateMixin {
  final VenueGoalLogicState state = VenueGoalLogicState();
  String _date = DateFormat('HH:mm').format(DateTime.now());
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  final VideoController videoController =
      VideoController(pushDisposeOnAndroid: true);
  TextEditingController nickNameController = TextEditingController();
  TabController? tabDialogController;
  TabController? tabController;
  var tabbarIndex = 0.obs;
  var isFrist = true.obs;
  // var videostate = 0.obs; //0初始 1播放  2暂停  3完成
  var todayDate = "";
  var yesterdayDate = "";
  var isFullScreen = false.obs;
  var type = 0.obs; //0普通球馆  1高级球馆 有ai身形
  var venueId = 0.obs; //场地id
  var venueName = "".obs; //场地名称
  var halfCourt = [].obs; //场地id
  var halfCourtModel = HalfCourtModel().obs; //传递参数
  late final VideoMergeTool _merger;
  late final CosUploadServiceSingle _uploadService;
  var _progress = 0.0.obs;
  var _uploadProgress = 0.0.obs;
  bool _isProcessing = false;
  String? _outputPath;
  var trainingId = "0".obs;
  var total2 = 0.obs;
  var index2 = 0.obs;
  var uploadImg = ''.obs;
  var uploadVideo = ''.obs;
  var a = 0;
  var downloadListProgress = 0.0.obs;
  var downloadTotal = 0.obs;
  var downloadIndex = 0.obs;
  var isToMerge = false; //是否去合成 true去 false不去
  // 切换选中状态（通用方法）
  void toggleCheck(RxList<VenueGoalModel> list, int index) {
    list[index].isCheck.toggle();
  }

  @override
  Future<void> onInit() async {
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
    if (Get.arguments != null) {
      final arguments = Get.arguments as Map<String, dynamic>;
      venueId.value = arguments['venueId'];
      venueName.value = arguments['venueName'];

      final halfCourt2 = Get.arguments['halfCourt'] is List
          ? Get.arguments['halfCourt']
          : [Get.arguments['halfCourt']];
      halfCourt.addAll(halfCourt2);
      if (arguments.containsKey("videoEndTime")) {
        state.videoEndTime.value = Get.arguments['videoEndTime'];
        // state.videoEndTime.value = "2025-09-02 13:00:00";
      }
    }
    tabController = TabController(length: halfCourt.length, vsync: this);
    tabDialogController = TabController(length: 2, vsync: this);
    tabController!.addListener(() {
      tabbarIndex.value = tabController!.index;
    });
    state.rememberOption.value =
        await WxStorage.instance.getString("rememberOptionMerge") ?? "0";
    if (state.rememberOption.value == "0") {
      state.compositeOption2[1] =
          (await WxStorage.instance.getInt("compositeOptionMerge1") ?? 0) == 1
              ? "1"
              : "0"; //慢动作视频效果与个性化 0选中  1选中 多选
      state.isShare.value =
          (await WxStorage.instance.getInt("isShareOptionMerge") ?? 0) == 1
              ? true
              : false;
    }
    getDataTime();
    SerialTaskQueue.init();

    _merger = VideoMergeTool(
        onProgress: (p, index, total, msg, {bool isError = false}) {
      a++;
      if (a % 3 == 0 || p >= 1) {
        _progress.value = p > 1
            ? 1
            : _progress.value > p
                ? _progress.value
                : p;
        total2.value = total ~/ 3;
        index2.value = index ~/ 3;
        log('VideoMergePagemergedPath1333011=$p-${_progress.value}=${total2.value}=${index2.value}');
      }
      if (isError) {
        log('VideoMergePagemergedPath1333012=${msg}');
        //    Get.snackbar('错误', msg);
      }
    }, onCompleted: (String url, String coverPath) {
      log('VideoMergePagemergedPath13330 ${state.compositeOption2[1] != "1"} onCompleted=$url');

      _merger.cancelAll();
      checkDialog2();
      if (url != "") {
        _uploadFile(url, coverPath);
      }
    });
    _uploadService = CosUploadServiceSingle(onProgress: (p) {
      _uploadProgress.value = p > 1 ? 1 : p;
      log('CosUploadServiceSingle1=$p');
    }, onCompleted: (String url, int type) {
      log('CosUploadServiceSingle2=  type=$type onCompleted=$url');
      if (type == 1) {
        uploadVideo.value = url;
      } else if (type == 2) {
        uploadImg.value = url;
      }
      if (uploadImg.value != "" && uploadVideo.value != "") {
        WxLoading.dismiss();
        getUpDateMergeVideos(
            uploadVideo.value, uploadImg.value == "1" ? "" : uploadImg.value);
      }
    }, onFailure: (String error, int type) {
      log('CosUploadServiceSingle3= error=$error');
      if (type == 2) {
        uploadImg.value = "1";
      } else if (type == 1) {
        WxLoading.showToast("视频上传失败，请检查网络后重试");
      }
      WxLoading.dismiss();
    });
  }

  getDataTime() {
    DateFormat dateFormat3 = DateFormat("yyyy-MM-dd");
    // 定义日期时间格式
    DateFormat dateFormat = DateFormat("yyyy-MM-dd HH:mm:ss");
    DateTime nowDateTime = DateTime.now();
    todayDate = dateFormat3.format(nowDateTime);
    DateTime nextEndTime = nowDateTime.subtract(const Duration(days: 1));
    yesterdayDate = dateFormat3.format(nextEndTime);
    if (state.videoEndTime.value == "") {
      state.videoEndTime.value = dateFormat.format(nowDateTime);
    }
    try {
      // 解析日期时间字符串
      DateTime endtime = DateTime.parse(state.videoEndTime.value);
      // 减少一个小时
      DateTime starttime = endtime.subtract(const Duration(hours: 1));

      // 创建一个 DateTime 对象（可以是当前时间或特定时间）
      DateTime dateTime = DateTime(2024, 12, 17, 21, 53, 55);

      state.videoStartTime.value = dateFormat.format(starttime);
      DateTime nextEndTime = endtime.add(const Duration(hours: 1));
      state.dateFristList2.clear();
      DateTime dateTime1 = DateTime(starttime.year, starttime.month,
          starttime.day, starttime.hour, 00, 00);
      DateTime dateTime2 = DateTime(starttime.year, starttime.month,
          starttime.day, starttime.hour, 30, 00);
      DateTime dateTime3 = DateTime(
          endtime.year, endtime.month, endtime.day, endtime.hour, 00, 00);
      DateTime dateTime4 = DateTime(
          endtime.year, endtime.month, endtime.day, endtime.hour, 30, 00);
      DateTime dateTime5 = DateTime(
          nextEndTime.year,
          nextEndTime.month,
          //推荐时段的4个时间
          nextEndTime.day,
          nextEndTime.hour,
          00,
          00);
      state.dateFristList2.add({
        "startTime": dateFormat.format(dateTime1),
        "endTime": dateFormat.format(dateTime3)
      });
      state.dateFristList2.add({
        "startTime": dateFormat.format(dateTime1),
        "endTime": dateFormat.format(dateTime2)
      });
      state.dateFristList2.add({
        "startTime": dateFormat.format(dateTime3),
        "endTime": dateFormat.format(dateTime5)
      });
      state.dateFristList2.add({
        "startTime": dateFormat.format(dateTime3),
        "endTime": dateFormat.format(dateTime4)
      });
      state.datePrecisionList.clear();
      //精确查找最近8天

      DateTime nowDateTime = DateTime.now();
      DateFormat dateFormat2 = DateFormat("yyyy.MM.dd");
      final DateFormat formatter = DateFormat('EEEE', 'zh_CN'); // 根据需要更改语言代码
      // DateTime videoDate2 = DateTime.parse(state.videoDate.value);
      state.videoDate1.value = dateFormat2.format(nowDateTime);
      state.videoNextStartTime.value = state.videoEndTime.value;
      DateTime endtime3 = DateTime.parse(state.videoEndTime.value);
      // 增加一个小时
      DateTime nextStartTime = endtime3.add(const Duration(hours: 1));
      // 定义日期时间格式
      state.videoNextEndTime.value = dateFormat.format(nextStartTime);
      state.datePrecisionList.add({
        "week": formatter.format(nowDateTime),
        "date": dateFormat2.format(nowDateTime),
        "datestr": S.current.today
      });
      for (int i = 0; i < 7; i++) {
        var days = i + 1;
        DateTime nextEndTime = nowDateTime.subtract(Duration(days: days));
        state.datePrecisionList.add({
          "week": formatter.format(nextEndTime),
          "date": dateFormat2.format(nextEndTime),
          "datestr": i == 0 ? S.current.yesterday : ""
        });
      }
      getDataList2(0);
      if (halfCourt.length > 1) {
        Future.delayed(const Duration(milliseconds: 200)).then((onValue) {
          getDataList2(1);
        });
      }

      print('Parsed DateTime: $dateTime');
    } catch (e) {
      print('Error parsing date string: $e');
    }
  }

  Future<void> _uploadFile(String filePath2, String coverPath) async {
    WxLoading.show(status: "上传视频中...");
    // 生成任务ID
    final taskId =
        '${filePath2.hashCode}_${DateTime.now().millisecondsSinceEpoch}';
    // 准备上传数据
    final record = ShotRecordModel(
        filePath: filePath2,
        trainingId: trainingId.value,
        eventId: taskId,
        playerImagePath: coverPath);
    // 添加上传任务
    _uploadService.addUploadTask(record, taskId);
    final taskId2 =
        '${coverPath.hashCode}_${DateTime.now().millisecondsSinceEpoch}';
    // 准备上传数据
    final record2 = ShotRecordModel(
        filePath: filePath2,
        trainingId: trainingId.value,
        eventId: taskId,
        playerImagePath: coverPath);
    // 添加上传任务
    _uploadService.addUploadTask(record2, taskId2);
  }

  @override
  void onReady() {
    //TODO
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 这里的代码将在当前帧结束后执行
      refresh();
    });

    super.onReady();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {}

  reloadVideo(String videoUrl) async {
    if (videoUrl.isEmpty) {
      log("betterPlayer-message11=$videoUrl");
      return;
    }
    videoController.setData(
      videoPath: videoUrl,
    );
  }

  //获得最新列表
  getDataList2(int type) async {
    var param = {
      'venueId': venueId.value, //"29", //
      'venueCourtId': type == 0
          ? halfCourt.first["id"]
          : halfCourt.last["id"], //type == 0 ? "88" : "102",
      'startTime': state.videoStartTime
          .value, // "2025-09-02 12:00:00", //state.videoStartTime.value,
      'endTime': state.videoEndTime
          .value, // "2025-09-02 13:59:00", //state.videoEndTime.value,
      'shootType': 0, //  0:横屏 1:竖屏
    };
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.show();
    }
    final res = await Api().get(ApiUrl.getVenueVideos, queryParameters: param);
    log("getVenueVideos222 $type-$param--${jsonEncode(res.data)}");
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.dismiss();
    }
    if (res.isSuccessful()) {
      List list = res.data;
      List<VenueGoalModel> modelList =
          list.map((e) => VenueGoalModel.fromJson(e)).toList();
      if (type == 0) {
        // 1. 保存当前所有选中ID
        final checkedIds = state.checkedList.map((item) => item.id).toSet();

        // 2. 更新数据列表并恢复选中状态
        state.dataList1.assignAll(modelList.map((newItem) {
          // 如果新数据的ID在已选中列表中，恢复选中状态
          if (checkedIds.contains(newItem.id)) {
            return newItem.copyWith(isCheck: true);
          }
          return newItem;
        }));
        if (state.dataList1.isNotEmpty && tabbarIndex.value == 0) {
          changeVideoIndex(0, type);
        } else {
          state.indexVideoId.value = "";
          videoController.setData(videoPath: '');
        }
      } else {
        // 1. 保存当前所有选中ID
        final checkedIds = state.checkedList.map((item) => item.id).toSet();

        // 2. 更新数据列表并恢复选中状态
        state.dataList2.assignAll(modelList.map((newItem) {
          // 如果新数据的ID在已选中列表中，恢复选中状态
          if (checkedIds.contains(newItem.id)) {
            return newItem.copyWith(isCheck: true);
          }
          return newItem;
        }));
        // if (state.dataList2.isNotEmpty && tabbarIndex.value == 1) {
        //   changeVideoIndex(0, type);
        // } else {
        //   state.indexVideoId.value = "";
        //   videoController.setData(videoPath: '');
        // }
      }
      if (dataFag["isFrist"] as bool && type == 0) {
        dataFag["isFrist"] = false;
        refresh();
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //判断视频是否能下载  然后返回最新下载地址
  getCanDownLoadVideoUrl(List<VenueGoalModel> dataListSelete, int type) async {
    var param = dataListSelete.map((model) => model.id).toList();
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.show();
    }

    final res = await Api().post(ApiUrl.getVenueCanDownLoadVideoUrl,
        data: param, showError: false);
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.dismiss();
    }
    log("getCanDownLoadVideoUrl=$param-${res.data}-${res.code}");
    if (res.isSuccessful()) {
      if (type == 1) {
        showUpdateDialog2(0);
        isToMerge = true;
        List list = res.data ?? [];
        downloadListProgress.value = 0.0;
        downloadTotal.value = dataListSelete.length;
        downloadIndex.value = 0;
        List<VenueGoalModel> modelList =
            list.map((e) => VenueGoalModel.fromJson(e)).toList();
        if (modelList.isNotEmpty) {
          //
          // 执行下载
          final service = FileDownloadService();
          var list = await service.batchDownload(
            modelList,
            onProgress: (current, total, p) {
              //下载进度
              log("getCanDownLoadVideoUrl1=$current-$total-$p");
              if (downloadListProgress.value <= p) {
                downloadListProgress.value = p > 1
                    ? 1
                    : downloadListProgress.value > p
                        ? downloadListProgress.value
                        : p;
                downloadTotal.value = total;
                downloadIndex.value = current;
              }

              if (p >= 1) {
                log("getCanDownLoadVideoUrl12=$current-$total-$p");
              }
            },
          );
          checkDialog();
          if (isToMerge) {
            final List<ShotRecordModel> shotRecords = list.map((venueGoal) {
              return ShotRecordModel(
                eventId: venueGoal.eventId,
                filePath: venueGoal.localFilePath,
                venueId: venueId.value,
                venueName: venueName.value,
                newworkFilePath: venueGoal.videoPath, // 示例计算函数
              );
            }).toList();

            final sortedList = List<ShotRecordModel>.from(shotRecords)
              ..sort((a, b) {
                final numA = int.tryParse(a.eventId ?? "") ?? 0;
                final numB = int.tryParse(b.eventId ?? "") ?? 0;
                return numA.compareTo(numB);
              });
            startAll(sortedList);
          }
          log("getCanDownLoadVideoUrl1=${jsonEncode(list)}");
        }
      } else {
        showUpdateDialog2(1);
        isToMerge = false;
        List list = res.data ?? [];
        downloadListProgress.value = 0.0;
        downloadTotal.value = dataListSelete.length;
        downloadIndex.value = 0;
        List<VenueGoalModel> modelList =
            list.map((e) => VenueGoalModel.fromJson(e)).toList();
        if (modelList.isNotEmpty) {
          //
          // 执行下载
          final service = FileDownloadService();
          var list = await service.batchDownload(
            modelList,
            onProgress: (current, total, p) {
              //下载进度
              log("getCanDownLoadVideoUrl1=$current-$total-$p");
              if (downloadListProgress.value <= p) {
                downloadListProgress.value = p > 1
                    ? 1
                    : downloadListProgress.value > p
                        ? downloadListProgress.value
                        : p;
                downloadTotal.value = total;
                downloadIndex.value = current;
              }

              if (p >= 1) {
                log("getCanDownLoadVideoUrl12=$current-$total-$p");
              }
            },
          );
          // 使用 map() 转换
          final List<ShotRecordModel> shotRecords = list
              .map((goal) => ShotRecordModel(
                  filePath: goal.localFilePath, eventId: goal.eventId))
              .toList(); // 必须调用 toList()，因为 map() 返回的是 Iterable
          Utils.localDownloadAndSaveToPhotoAlbum(shotRecords);
          checkDialog();
          WxLoading.showToast("下载完成");
        }
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }

// 替换您的startAll方法
  Future<void> startAll(
    List<ShotRecordModel> list,
  ) async {
    log('compositeVideo2');
    if (_isProcessing) {
      Get.snackbar('警告', '已有任务进行中');
      return;
    }
    try {
      log('compositeVideo3=最终合成');
      _isProcessing = true;
      _progress.value = 0;
      total2.value = list.length;
      index2.value = 0;
      //  _outputPath =
      showMergeDialog2();
      if (state.compositeOption2[1] != "1") {
        //无慢放
        await _merger.startAllNoSpecial(list);
      } else {
        await _merger.startAll(list);
      }
      log('compositeVideo4=合成视频路径: $_outputPath');
    } finally {
      _isProcessing = false;
      log('compositeVideo5=合成视频路径:');
    }
  }

  getDownLoadList() {
    var dataListSelete1 =
        state.dataList1.where((item) => item.isCheck.value == true).toList();
    var dataListSelete2 =
        state.dataList2.where((item) => item.isCheck.value == true).toList();

    if (dataListSelete1.isEmpty && dataListSelete2.isEmpty) {
      WxLoading.showToast(S.current.Please_select_video_click_Download);
      return;
    }
    List<VenueGoalModel> dataListSelete = [];
    dataListSelete.addAll(dataListSelete1);
    dataListSelete.addAll(dataListSelete2);
    getCanDownLoadVideoUrl(dataListSelete, 0);
  }

// 下载进度显示弹窗（自动避免重复）
  void showUpdateDialog2(int type) {
    ExclusiveDialog.show(
        context: Get.context!,
        builder: (_) => Padding(
              padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
              child: Material(
                type: MaterialType.transparency,
                color: Colors.transparent,
                child: Center(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        Container(
                          color: Colors.transparent,
                          child: Column(
                            children: <Widget>[
                              Container(
                                alignment: Alignment.topLeft,
                                height: 204.w,
                                decoration: BoxDecoration(
                                  color: Colours.color191921,
                                  borderRadius: BorderRadius.only(
                                    bottomLeft: Radius.circular(25.r),
                                    bottomRight: Radius.circular(25.r),
                                  ),
                                ),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 18, vertical: 2),
                                width: double.infinity,
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: <Widget>[
                                    const SizedBox(
                                      height: 30,
                                    ),
                                    Center(
                                      child: Text(
                                        ("提示"), //"1.修复了一些BUG;\n2.更新部分内容",//
                                        //  "1.修复了一些BUG;\n2.更新部分内容" * 20,
                                        style: TextStyles.regular.copyWith(
                                            color: Colours.white,
                                            fontSize: 18.sp),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    Obx(() {
                                      return LinearPercentIndicator(
                                        lineHeight: 4,
                                        linearGradient: const LinearGradient(
                                            colors: [
                                              Colours.color7732ED,
                                              Colours.colorA555EF
                                            ]),
                                        percent:
                                            downloadListProgress.value, //??
                                        //     controller.progressNotifier
                                        //         .value, //uploadController.progress, //
                                        backgroundColor: Colours.color000000,
                                        //    progressColor: Colours.colorA555EF, // 已完成的任务为绿色，否则根据进度设置颜色
                                        barRadius: const Radius.circular(3),
                                        animation: true,
                                        animateFromLastPercent: true,
                                        animationDuration: 500,
                                      );
                                    }),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    Obx(() {
                                      return RichText(
                                        textAlign: TextAlign.left,
                                        text: TextSpan(
                                            text: "视频下载中，请稍等",
                                            style: TextStyle(
                                                color: Colours.colorA8A8BC,
                                                fontSize: 14.sp,
                                                height: 1,
                                                fontWeight: FontWeight.w400),
                                            children: <InlineSpan>[
                                              TextSpan(
                                                  text:
                                                      "${downloadIndex.value}/${downloadTotal.value}",
                                                  style: TextStyle(
                                                      color: Colours.white,
                                                      fontSize: 14.sp,
                                                      height: 1,
                                                      fontWeight:
                                                          FontWeight.w400)),
                                            ]),
                                      );
                                    }),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    Obx(() {
                                      return GestureDetector(
                                        behavior: HitTestBehavior.translucent,
                                        onTap: () {
                                          if (type == 0) {
                                            if (downloadListProgress.value >=
                                                1) {
                                              isToMerge = true;
                                            } else {
                                              isToMerge = false;
                                            }
                                          }
                                          checkDialog();

                                          //   _merger.cancelAll();
                                        },
                                        child: Container(
                                          width: double.infinity,
                                          height: 40.w,
                                          alignment: Alignment.center,
                                          margin: EdgeInsets.symmetric(
                                              horizontal: 20.w),
                                          decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(22.w),
                                              border: Border.all(
                                                  width: 1.w,
                                                  color: Colours.white)),
                                          child: Text(
                                            type == 0
                                                ? downloadListProgress.value >=
                                                        1
                                                    ? "去合成"
                                                    : "取消"
                                                : "关闭",
                                            style: TextStyles.regular.copyWith(
                                                fontWeight: FontWeight.w600),
                                          ),
                                        ),
                                      );
                                    }),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ));
  }

// 合成显示弹窗（自动避免重复）
  void showMergeDialog2() {
    ExclusiveDialog2.show(
        context: Get.context!,
        builder: (_) => Padding(
              padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
              child: Material(
                type: MaterialType.transparency,
                color: Colors.transparent,
                child: Center(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        Container(
                          color: Colors.transparent,
                          child: Column(
                            children: <Widget>[
                              Container(
                                alignment: Alignment.topLeft,
                                height: 204.w,
                                decoration: BoxDecoration(
                                  color: Colours.color191921,
                                  borderRadius: BorderRadius.only(
                                    bottomLeft: Radius.circular(25.r),
                                    bottomRight: Radius.circular(25.r),
                                  ),
                                ),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 18, vertical: 2),
                                width: double.infinity,
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: <Widget>[
                                    const SizedBox(
                                      height: 30,
                                    ),
                                    Center(
                                      child: Text(
                                        ("提示"), //"1.修复了一些BUG;\n2.更新部分内容",//
                                        //  "1.修复了一些BUG;\n2.更新部分内容" * 20,
                                        style: TextStyles.regular.copyWith(
                                            color: Colours.white,
                                            fontSize: 18.sp),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    Obx(() {
                                      return LinearPercentIndicator(
                                        lineHeight: 4,
                                        linearGradient: const LinearGradient(
                                            colors: [
                                              Colours.color7732ED,
                                              Colours.colorA555EF
                                            ]),
                                        percent: _progress.value, //??
                                        //     controller.progressNotifier
                                        //         .value, //uploadController.progress, //
                                        backgroundColor: Colours.color000000,
                                        //    progressColor: Colours.colorA555EF, // 已完成的任务为绿色，否则根据进度设置颜色
                                        barRadius: const Radius.circular(3),
                                        animation: true,
                                        animateFromLastPercent: true,
                                        animationDuration: 500,
                                      );
                                    }),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    Obx(() {
                                      return RichText(
                                        textAlign: TextAlign.left,
                                        text: TextSpan(
                                            text: "合成视频中，请稍等",
                                            style: TextStyle(
                                                color: Colours.colorA8A8BC,
                                                fontSize: 14.sp,
                                                height: 1,
                                                fontWeight: FontWeight.w400),
                                            children: <InlineSpan>[
                                              TextSpan(
                                                  text:
                                                      "${((_progress.value * total2.value * 10) ~/ 10)}/${total2.value}",
                                                  style: TextStyle(
                                                      color: Colours.white,
                                                      fontSize: 14.sp,
                                                      height: 1,
                                                      fontWeight:
                                                          FontWeight.w400)),
                                            ]),
                                      );
                                    }),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    // if (_progress < 1)
                                    Obx(() {
                                      return GestureDetector(
                                        behavior: HitTestBehavior.translucent,
                                        onTap: () {
                                          checkDialog2();
                                          _merger.cancelAll();
                                        },
                                        child: Container(
                                          width: double.infinity,
                                          height: 40.w,
                                          alignment: Alignment.center,
                                          margin: EdgeInsets.symmetric(
                                              horizontal: 20.w),
                                          decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(22.w),
                                              border: Border.all(
                                                  width: 1.w,
                                                  color: Colours.white)),
                                          child: Text(
                                            "取消合成",
                                            style: TextStyles.regular.copyWith(
                                                fontWeight: FontWeight.w600),
                                          ),
                                        ),
                                      );
                                    }),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ));
  }

// 在任意位置检查状态
  void checkDialog() {
    if (ExclusiveDialog.isShowing) {
      print("弹窗正在显示");
      ExclusiveDialog.forceClose(); // 强制关闭
    }
  }

  void checkDialog2() {
    if (ExclusiveDialog2.isShowing) {
      print("checkDialog2弹窗正在显示");
      ExclusiveDialog2.forceClose(); // 强制关闭
    }
  }

  //改变选择视频index
  void changeVideoIndex(
    int index,
    int tabIndex, //0 半场1   1半场2
  ) {
    state.indexVideoId.value = tabbarIndex.value == 0
        ? state.dataList1[index].id
        : state.dataList2[index].id;
    if (tabbarIndex.value == 0) {
      if (state.indexVideoId.value != "") {
        reloadVideo(state.dataList1[index].videoPath);
      }
    } else {
      if (state.indexVideoId.value != "") {
        reloadVideo(state.dataList2[index].videoPath);
      }
    }
  }

  //新增数据和删除数据
  Future<void> checkVideo(int index, int tabIndex, //0 半场1   1半场2
      {bool isToNext = false}) async {
    if (tabIndex == 0) {
      state.toggleCheck(state.dataList1[index]);
    } else {
      state.toggleCheck(state.dataList2[index]);
    }
    log("checkVideo2-1");
    if (state.dataList1.isNotEmpty &&
        state.dataList1[index].isCheck.value == true &&
        isToNext) {
      if ((index + 1) < state.dataList1.length) {
        changeVideoIndex(index + 1, tabIndex);
      }
    }
  }

  //改变选择视频index
  void changeVideoIndexLibrary(VenueGoalModel venueGoalModel, int index) {
    state.indexVideoLibrary.value = index;
    reloadVideo(venueGoalModel.videoPath);
  }

  //弹窗选择时间
  Future<void> chooseTime(String startTime, String endTime, int type) async {
    state.videoStartTime.value = startTime;
    state.videoEndTime.value = endTime;
    state.videoNextStartTime.value = endTime;
    DateTime endtime = DateTime.parse(endTime);
    // 增加一个小时
    DateTime nextStartTime = endtime.add(const Duration(hours: 1));
    // 定义日期时间格式
    DateFormat dateFormat = DateFormat("yyyy-MM-dd HH:mm:ss");
    state.videoNextEndTime.value = dateFormat.format(nextStartTime);
    getDataList2(0);
    if (halfCourt.length > 1) {
      Future.delayed(const Duration(milliseconds: 100)).then((onValue) {
        getDataList2(1);
      });
    }
  }

  void showDatePicker(BuildContext context, int type) {
    //0开始时间 1结束时间
    FocusManager.instance.primaryFocus?.unfocus();
    DatePicker.showDatePicker(
      context,
      locale: DateTimePickerLocale.zh_cn, // 设置为中文
      pickerTheme: DateTimePickerTheme(
        backgroundColor: Colours.color191921,
        itemTextStyle: TextStyles.display16,
        itemHeight: 60,
        showTitle: true,
        title: Container(
          padding: const EdgeInsets.only(left: 20, right: 20, top: 25),
          height: 50,
          decoration: const BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16), topRight: Radius.circular(16))),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Text(
                  S.current.cancel,
                  style:
                      TextStyles.display14.copyWith(color: Colours.color9393A5),
                ),
              ),
              Text(
                type == 0
                    ? S.current.Please_select_start_time
                    : S.current.Please_select_end_time,
                style: TextStyles.titleSemiBold16,
              ),
              GestureDetector(
                onTap: () {
                  if (type == 0) {
                    state.videoStartTime1.value = _date;
                  } else {
                    state.videoEndTime1.value = _date;
                  }
                  Navigator.pop(context);
                },
                child: Text(
                  S.current.save,
                  style:
                      TextStyles.display14.copyWith(color: Colours.colorA44EFF),
                ),
              ),
            ],
          ),
        ),
        titleHeight: 50,
      ),
      initialDateTime: type == 0
          ? state.videoStartTime1.value.isEmpty
              ? DateTime.now()
              : DateFormat('HH:mm').parse(state.videoStartTime1.value, false)
          : state.videoEndTime1.value.isEmpty
              ? DateTime.now()
              : DateFormat('HH:mm').parse(state.videoEndTime1.value, false),
      dateFormat: S.current.hh_mm,
      // maxDateTime: DateTime.parse(MAX_DATETIME),
      // minDateTime: DateTime.parse(MIN_DATETIME),
      onConfirm: (dateTime, _) {},
      onChange: (dateTime, _) {
        _date = DateFormat('HH:mm').format(dateTime);
      },
    );
  }

//精准查找 确认按钮
  void sureTimeSearch(BuildContext context) {
    if (state.videoDate1.value.isEmpty) {
      WxLoading.showToast(S.current.time_choose_tips1);
      return;
    }

    if (state.videoStartTime1.value.isEmpty) {
      WxLoading.showToast(S.current.Please_select_start_time);
      return;
    }
    if (state.videoEndTime1.value.isEmpty) {
      WxLoading.showToast(S.current.Please_select_end_time);
      return;
    }
    var startTime =
        DateFormat('HH:mm').parse(state.videoStartTime1.value, false);
    var endTime = DateFormat('HH:mm').parse(state.videoEndTime1.value, false);
    if (startTime.isAfter(endTime)) {
      WxLoading.showToast(S.current.time_choose_tips2);
      return;
    }
    DateTime twoHoursEarlier = endTime.subtract(const Duration(hours: 2));
    if (startTime.isBefore(twoHoursEarlier)) {
      WxLoading.showToast(S.current.time_choose_tips3);
      return;
    }
    if (startTime == endTime) {
      WxLoading.showToast(S.current.time_choose_tips4);
      return;
    }
    DateFormat dateFormat = DateFormat("yyyy-MM-dd HH:mm:ss");

    // 解析日期时间字符串
    DateTime originalTime =
        DateFormat("yyyy.MM.dd").parse(state.videoDate1.value);
    state.videoDate.value = dateFormat.format(originalTime);

    DateTime startDateTime1 = DateTime(originalTime.year, originalTime.month,
        originalTime.day, startTime.hour, startTime.minute, 00);
    DateTime endDdteTime2 = DateTime(originalTime.year, originalTime.month,
        originalTime.day, endTime.hour, endTime.minute, 00);

    Navigator.pop(context);
    chooseTime(
        dateFormat.format(startDateTime1), dateFormat.format(endDdteTime2), 3);
  }

//分享去邀请新人   0微信  1朋友圈
  Future<void> getShareWx(int i) async {
    var param = {
      'shareType': "1",
    };
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.show();
    }
    final res = await Api().get(ApiUrl.inviteMiniPath, queryParameters: param);
    if (!(dataFag["isFrist"] as bool)) {
      WxLoading.dismiss();
    }
    if (res.isSuccessful()) {
      if (res.data == null) {
        WxLoading.showToast(S.current.No_data_available);
        return;
      }
      log("getVideosUsedShots42=${jsonEncode(res.data)}-user=${UserManager.instance.user?.userId}");
      InviteMiniPathModel inviteMiniPathModel =
          InviteMiniPathModel.fromJson(res.data);
      // 下载网络图片并转换为 Uint8List
      Uint8List? imageBytes =
          await downloadImageToUint8List(inviteMiniPathModel.image ?? "");
      if (imageBytes == null || inviteMiniPathModel.image == "") {
        ByteData data = await rootBundle
            .load("assets/images/dialog_invitation.png"); //dialog_invitation
        imageBytes = data.buffer.asUint8List();
      }
      MyShareH5.shareMiniProgram(inviteMiniPathModel, imageBytes);
      // List list = res.data;
      // List<AiOptionModel> modelList =
      //     list.map((e) => AiOptionModel.fromJson(e)).toList();
      // log("getAiOptionGoal=${res.data}");
      // state.aiDataList.assignAll(modelList);
    } else {
      WxLoading.showToast(res.message);
    }

    if (i == 0) {
    } else {}
  }

  Future<Uint8List?> downloadImageToUint8List(String imageUrl) async {
    try {
      // 发起 HTTP GET 请求获取图片数据
      final response = await http.get(Uri.parse(imageUrl));

      // 检查响应状态码是否成功
      if (response.statusCode == 200) {
        // 将响应体的字节数据转换为 Uint8List
        return Uint8List.fromList(response.bodyBytes);
      } else {
        print("Failed to load image: ${response.statusCode}");
        return null;
      }
    } catch (e) {
      print("Error downloading image: $e");
      return null;
    }
  }

  //合成视频
  void compositeVideo(List<VenueGoalModel> list2) {
    if (list2.isEmpty) {
      WxLoading.showToast(S.current.merge_videos_tips1);
      return;
    }
    if (list2.length > 50) {
      WxLoading.showToast(S.current.merge_videos_tips3);
      return;
    }
    if (nickNameController.text.trim().isEmpty) {
      WxLoading.showToast(S.current.video_name);
      return;
    }

    // var list = [
    //   {
    //     "id": "9856",
    //     "videoTime": "15:25:19",
    //     "isCheck": true,
    //     "videoPath":
    //         "https://shootz-cos-1308047407.cos.ap-guangzhou.myqcloud.com/mobile/videos/293589/1515/video/1757663116932.mp4?q-sign-algorithm=sha1&q-ak=AKIDSktrb8RXQ4TkmZ8h2xnPozwgOuCH8lBQ&q-sign-time=1757749115%3B1757750915&q-key-time=1757749115%3B1757750915&q-header-list=host&q-url-param-list=&q-signature=735c21a4460b3b31e0e288dd73407aecfcddef9e",
    //     "coverPath": "",
    //     "eventId": "15150004",
    //     "trainingId": "1515",
    //     "typeIndex": 0,
    //     "localFilePath": ""
    //   },
    //   {
    //     "id": "9853",
    //     "videoTime": "15:24:48",
    //     "isCheck": true,
    //     "videoPath":
    //         "https://shootz-cos-1308047407.cos.ap-guangzhou.myqcloud.com/mobile/videos/293589/1515/video/1757663101964.mp4?q-sign-algorithm=sha1&q-ak=AKIDSktrb8RXQ4TkmZ8h2xnPozwgOuCH8lBQ&q-sign-time=1757749115%3B1757750915&q-key-time=1757749115%3B1757750915&q-header-list=host&q-url-param-list=&q-signature=6c13ff3a9527b6a203206ace445e554a7e242a39",
    //     "coverPath": "",
    //     "eventId": "15150001",
    //     "trainingId": "1515",
    //     "typeIndex": 0,
    //     "localFilePath": ""
    //   },
    //   {
    //     "id": "9855",
    //     "videoTime": "15:25:06",
    //     "isCheck": true,
    //     "videoPath":
    //         "https://shootz-cos-1308047407.cos.ap-guangzhou.myqcloud.com/mobile/videos/293589/1515/video/1757663111947.mp4?q-sign-algorithm=sha1&q-ak=AKIDSktrb8RXQ4TkmZ8h2xnPozwgOuCH8lBQ&q-sign-time=1757749115%3B1757750915&q-key-time=1757749115%3B1757750915&q-header-list=host&q-url-param-list=&q-signature=1890f6de7ec44d1def9b4ec3643d771d0a10d607",
    //     "coverPath": "",
    //     "eventId": "15150003",
    //     "trainingId": "1515",
    //     "typeIndex": 0,
    //     "localFilePath": ""
    //   },
    //   {
    //     "id": "9854",
    //     "videoTime": "15:24:56",
    //     "isCheck": true,
    //     "videoPath":
    //         "https://shootz-cos-1308047407.cos.ap-guangzhou.myqcloud.com/mobile/videos/293589/1515/video/1757663106932.mp4?q-sign-algorithm=sha1&q-ak=AKIDSktrb8RXQ4TkmZ8h2xnPozwgOuCH8lBQ&q-sign-time=1757749115%3B1757750915&q-key-time=1757749115%3B1757750915&q-header-list=host&q-url-param-list=&q-signature=7d2c378e6e67ff2b17a0243b52401dbe9860301f",
    //     "coverPath": "",
    //     "eventId": "15150002",
    //     "trainingId": "1515",
    //     "typeIndex": 0,
    //     "localFilePath": ""
    //   }
    // ];
    // List<VenueGoalModel> modelList =
    //     list.map((e) => VenueGoalModel.fromJson(e)).toList();

    getCanDownLoadVideoUrl(list2, 1);
  }

  //合成视频
  getUpDateMergeVideos(String filePath2, String coverPath) async {
    if (state.rememberOption.value == "0") {
      await WxStorage.instance.setString(
          "rememberOptionMerge", state.rememberOption.value); //记住我的选择 0记住  1不记住
      await WxStorage.instance.setInt(
          "isShareOptionMerge", state.isShare.value ? 1 : 0); //是否 共享到场地展示为精彩视频
      await WxStorage.instance.setInt("compositeOptionMerge1",
          (state.compositeOption2[1] != "1") ? 0 : 1); //慢动作视频效果与个性化 0选中  1选中 多选
    }

    var param = {
      "VideoIdList": [],
      "coverPath": coverPath,
      "effects": (state.compositeOption2[1] != "1") ? [0] : [1],
      "share": state.isShare.value,
      "title": nickNameController.text.trim(), //合成名称
      "trainingId": trainingId.value,
      "venueId": venueId.value.toString(),
      "videoPath": filePath2
    };
    WxLoading.show();
    log("paramcompositeOption4=${jsonEncode(param)}");
    final res = await Api().post(ApiUrl.shootingAchievements, data: param);
    WxLoading.dismiss();
    log("message1");
    if (res.isSuccessful()) {
      nickNameController.text = "";
      uploadImg.value = "";
      uploadVideo.value = "";
      total2.value = 0;
      index2.value = 0;
      //  getMergeDialog(res.data["points"].toString(), isCheckAll);
      getMergeDialog("0", false);
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //合成视频弹窗
  Future<void> getMergeDialog(var points, bool isCheckAll) async {
    getMergeDialog2(
        points == "0" || points == ""
            ? ""
            : S.current.merge_videos_dialog_tips8(points),
        S.current.sure, () {
      AppPage.back();
    });
  }

  void getMergeDialog2(
    String titltPoint,
    String sureText,
    //String sureText2,
    void Function()? onPressed,
    // void Function()? onPressed2
  ) {
    Get.dialog(
      Padding(
        padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
        child: Material(
          type: MaterialType.transparency,
          color: Colors.transparent,
          child: Center(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Container(
                    color: Colors.transparent,
                    child: Column(
                      children: <Widget>[
                        //upload_top_img
                        SizedBox(
                          height: 20.w,
                        ),
                        Container(
                          alignment: Alignment.topLeft,
                          constraints: BoxConstraints(
                            // maxHeight: titltPoint != "" ? 245.w : 185.w,
                            minHeight: 145.w,
                          ),
                          decoration: BoxDecoration(
                            color: Colours.color191921,
                            borderRadius: BorderRadius.all(
                              Radius.circular(25.r),
                            ),
                          ),
                          padding: EdgeInsets.symmetric(
                              vertical: 20.w, horizontal: 25.w),
                          width: double.infinity,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: <Widget>[
                              Text(S.current.hint,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: 18.sp,
                                    color: Colours.white,
                                    fontWeight: AppFontWeight.medium(),
                                    height: 1,
                                  )),
                              SizedBox(
                                height: 20.w,
                              ),
                              if (titltPoint != "")
                                Text(titltPoint,
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: 18.sp,
                                      color: Colours.colorA44EFF,
                                      fontWeight: AppFontWeight.regular(),
                                      height: 1,
                                    )),
                              if (titltPoint != "")
                                SizedBox(
                                  height: 25.w,
                                ),
                              RichText(
                                textAlign: TextAlign.center,
                                text: TextSpan(
                                    text: S.current.merge_videos_dialog_tips11,
                                    style: TextStyle(
                                        color: Colours.color9393A5,
                                        fontSize: 14.sp,
                                        height: 2,
                                        fontWeight: FontWeight.normal),
                                    children: <InlineSpan>[
                                      TextSpan(
                                        text:
                                            S.current.merge_videos_dialog_tips1,
                                        style: TextStyle(
                                            color: Colours.color9393A5,
                                            fontSize: 14.sp,
                                            height: 2,
                                            fontWeight: FontWeight.normal),
                                      ),
                                      TextSpan(
                                          text: " ${S.current.my_highlights} ",
                                          style: TextStyle(
                                              color: Colours.white,
                                              fontSize: 14.sp,
                                              height: 2,
                                              fontWeight: FontWeight.normal)),
                                      TextSpan(
                                          text: S.current
                                              .merge_videos_dialog_tips31,
                                          style: TextStyle(
                                              color: Colours.color9393A5,
                                              height: 2,
                                              fontSize: 14.sp,
                                              fontWeight: FontWeight.normal)),
                                    ]),
                              ),
                              SizedBox(
                                height: 30.w,
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  GestureDetector(
                                    behavior: HitTestBehavior.translucent,
                                    onTap: () async {
                                      // AppPage.back(
                                      //     page: Routes.selfieShotInfoPage,
                                      //     result: "1");
                                      //我的集锦
                                      AppPage.back();
                                      await Future.delayed(
                                          const Duration(milliseconds: 100));
                                      Future.delayed(
                                              const Duration(milliseconds: 700))
                                          .then((onValue) {
                                        BusUtils.instance.fire(EventAction(
                                            key: EventBusKey.changeMyVideo));
                                      });
                                      AppPage.to(
                                          Routes.careerHighlightsHomePage,
                                          closePreviousPage: true,
                                          arguments: {
                                            "type": 1,
                                          });
                                    },
                                    child: Container(
                                      height: 46.w,
                                      width: 125.w,
                                      alignment: Alignment.center,
                                      margin: EdgeInsets.only(
                                        top: 15.w,
                                      ),
                                      padding: EdgeInsets.only(
                                          left: 5.w,
                                          right: 5.w,
                                          top: 3.w,
                                          bottom: 3.w),
                                      decoration: BoxDecoration(
                                        color: Colours.color22222D,
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(28.r)),
                                      ),
                                      child: Text(
                                        S.current.merge_videos_dialog_tips5,
                                        style: TextStyles.semiBold
                                            .copyWith(fontSize: 14.sp),
                                      ),
                                    ),
                                  ),
                                  GestureDetector(
                                    behavior: HitTestBehavior.translucent,
                                    onTap: () {
                                      AppPage.back();
                                      //
                                      // AppPage.back(
                                      //     page: Routes.selfieShotInfoPage);
                                    },
                                    child: Container(
                                      height: 46.w,
                                      width: 125.w,
                                      alignment: Alignment.center,
                                      margin: EdgeInsets.only(
                                        top: 15.w,
                                      ),
                                      padding: EdgeInsets.only(
                                          left: 5.w,
                                          right: 5.w,
                                          top: 3.w,
                                          bottom: 3.w),
                                      decoration: BoxDecoration(
                                        color: Colours.color282735,
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(28.r)),
                                        gradient: const LinearGradient(
                                          colors: [
                                            Colours.color7732ED,
                                            Colours.colorA555EF
                                          ],
                                          begin: Alignment.bottomLeft,
                                          end: Alignment.bottomRight,
                                        ),
                                      ),
                                      child: Text(
                                        S.current.merge_videos_continue_tips6,
                                        style: TextStyles.semiBold
                                            .copyWith(fontSize: 14.sp),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                            ],
                          ),
                        ),

                        SizedBox(
                          height: 25.w,
                        ),
                        // GestureDetector(
                        //   behavior: HitTestBehavior.translucent,
                        //   onTap: () {
                        //     AppPage.back();
                        //   },
                        //   child: WxAssets.images.icCloseDialog
                        //       .image(width: 30.w, height: 30.w),
                        // ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      barrierColor: Colors.black.withOpacity(0.85),
    );
  }

  @override
  void onClose() {
    videoController.pause();
    videoController.videoPlayerController?.setVolume(0);
    videoController.videoPlayerController?.seekTo(const Duration(seconds: 0));
    http.Client().close(); // 如果使用http包
    rootBundle.evict('');
    WidgetsBinding.instance.removeObserver(this);
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
    // 强制GC（针对视频编解码器泄漏）
    SystemChannels.platform.invokeMethod('Memory.forceGC');
    _merger.cancelAll();
    super.onClose();
  }

  @override
  void dispose() {
    SerialTaskQueue.dispose();
    // 停止所有动画
    videoController.dispose();
    // 释放视频编解码器
    SystemChannels.platform.invokeMethod('VideoPlayer.disposeAll');

    super.dispose();
  }
}
