import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/game/comparison/video_path/video_path_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/video/video_view.dart';
import 'package:ui_packages/ui_packages.dart';

class VideoPathPage extends StatefulWidget {
  const VideoPathPage({super.key});

  @override
  State<VideoPathPage> createState() => _HighlightsVideoPageState();
}

class _HighlightsVideoPageState extends State<VideoPathPage> {
  final VideoPathLogic logic = Get.put(VideoPathLogic());
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
          appBar: logic.isShowShareUpdate.value == "1"
              ? MyAppBar(
                  title: Text(logic.teamName.value),
                )
              : null,
          body: SafeArea(
            child: Column(
              children: [
                if (logic.isShowShareUpdate.value != "1") _title(context),
                SizedBox(
                  height: 47.w,
                ),
                _video(context),
                SizedBox(
                  height: 47.w,
                ),
              ],
            ),
          ));
    });
  }

  Widget _title(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 20, left: 20, right: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                  child: Text(
                "",
                style: TextStyles.titleSemiBold16.copyWith(fontSize: 20.sp),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              )),
              SizedBox(
                width: 10.w,
              ),
              GestureDetector(
                  onTap: () => AppPage.back(),
                  child:
                      WxAssets.images.icClose.image(width: 20.w, height: 20.w)),
            ],
          ),
        ),
      ],
    );
  }

  Widget _video(BuildContext context) {
    return Expanded(
      child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
        if (logic.isShowShareUpdate.value != "1")
          Padding(
              padding: EdgeInsets.symmetric(horizontal: 30.w),
              child: Text(
                logic.teamName.value,
                style: TextStyles.display14.copyWith(
                    color: Colours.color9393A5,
                    fontWeight: FontWeight.w400,
                    fontSize: 16.sp),
                maxLines: 1,
              )),
        if (logic.isShowShareUpdate.value != "1")
          SizedBox(
            height: 20.w,
          ),
        SizedBox(
          width: double.infinity,
          height: ScreenUtil().screenWidth / 375 * 211,
          child: AspectRatio(
            aspectRatio: 375 / 211, // 宽高比
            child: VideoView(
              controller: logic.videoController!,
            ),
          ),
        ),
        if (logic.isShowShareUpdate.value == "1") _action(context),
        if (logic.isShowShareUpdate.value == "2" ||
            logic.isShowShareUpdate.value == "3")
          _action2(context),
        if (logic.isShowShareUpdate.value == "4") _action4(context),
      ]),
    );
  }

  Widget _action(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 26, top: 100.w),
      child: Wrap(
          spacing: 40.w,
          children: List.generate(2, (index) {
            return GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                switch (index) {
                  // case 0:
                  //   logic.share();
                  //   break;
                  case 0:
                    logic.downloadAndSaveVideo();
                    break;
                  case 1:
                    logic.showDeleteDialog();
                    break;
                }
              },
              child: SizedBox(
                width: 80.w,
                height: 70.w,
                child: Column(
                  children: [
                    // index == 0
                    //     ? WxAssets.images.icShare
                    //         .image(width: 21.w, height: 21.w)
                    //     :
                    index == 0
                        ? WxAssets.images.icDownload
                            .image(width: 21.w, height: 21.w)
                        : WxAssets.images.icDelete
                            .image(width: 21.w, height: 21.w),
                    SizedBox(
                      height: 15.w,
                    ),
                    Text(
                      // index == 0
                      //     ? S.current.video_share
                      //     :
                      index == 0
                          ? S.current.video_download
                          : S.current.video_delete,
                      style: TextStyles.display14.copyWith(
                          color: Colours.white,
                          fontWeight: FontWeight.w400,
                          fontSize: 12.sp),
                      maxLines: 1,
                    )
                  ],
                ),
              ),
            );
          })),
    );
  }

  Widget _action2(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 26, top: 100.w),
      child: Wrap(
          spacing: 64.w,
          children: List.generate(2, (index) {
            return GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                switch (index) {
                  case 0:
                    logic.downloadAndSaveToSinglePhotoAlbum(
                        logic.shotRecordModel.value.filePath ?? "");
                    break;
                  case 1:
                    logic.deleteVideo(logic.shotRecordModel.value);
                    break;
                }
              },
              child: Container(
                padding: EdgeInsets.all(15.w),
                decoration: BoxDecoration(
                    color: Colours.color191921,
                    borderRadius: BorderRadius.all(Radius.circular(30.r))),
                child: index == 0
                    ? WxAssets.images.downloadIcon
                        .image(width: 21.w, height: 21.w)
                    : WxAssets.images.delete
                        .image(width: 21.w, height: 21.w, color: Colors.red),
              ),
            );
          })),
    );
  }

  Widget _action4(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 26, top: 100.w),
      child: Wrap(
          spacing: 64.w,
          children: List.generate(2, (index) {
            return GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                switch (index) {
                  case 0:
                    logic.downloadAndSaveToSinglePhotoAlbum(
                        logic.videoPath.value);
                    break;
                  case 1:
                    logic.deleteVideo4();
                    break;
                }
              },
              child: Container(
                padding: EdgeInsets.all(15.w),
                decoration: BoxDecoration(
                    color: Colours.color191921,
                    borderRadius: BorderRadius.all(Radius.circular(30.r))),
                child: index == 0
                    ? WxAssets.images.downloadIcon
                        .image(width: 21.w, height: 21.w)
                    : WxAssets.images.delete
                        .image(width: 21.w, height: 21.w, color: Colors.red),
              ),
            );
          })),
    );
  }
}
