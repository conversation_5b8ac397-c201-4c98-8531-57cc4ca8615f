// ignore_for_file: unused_element

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab5Mine/careerAndHighlights/my_highlights_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/more_widget.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

/// 赛程列表页面
class MyHighlightsPage extends StatelessWidget {
  MyHighlightsPage({super.key});
  final logic = Get.put(MyHighlightsLogic());
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Column(
        children: [
          Row(
            children: [
              Expanded(
                  child: InkWell(
                onTap: () {
                  _showBattleTypeBottomSheet(context);
                },
                child: Container(
                    width: 60.w,
                    height: 44.w,
                    alignment: Alignment.center,
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                    decoration: BoxDecoration(
                        color: Colours.color191921,
                        borderRadius: BorderRadius.all(Radius.circular(33.r))),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Obx(() => Text(
                              logic.highlightsTypeList[
                                      logic.currentTypeIndex.value]["title"] ??
                                  '',
                              style: TextStyles.semiBold14,
                            )),
                        Icon(
                          Icons.expand_more,
                          color: Colours.white,
                          size: 25.w,
                        ),
                      ],
                    )),
              )),
              Container(
                width: 60.w,
                height: 44.w,
                alignment: Alignment.center,
                margin: EdgeInsets.only(left: 15.w),
                decoration: BoxDecoration(
                    color: Colours.color191921,
                    borderRadius: BorderRadius.all(Radius.circular(33.r))),
                child: Obx(() {
                  return Text(
                    '共${logic.currentTypeIndex.value == 0 ? logic.totalCount.value : logic.totalCount1.value}',
                    style: TextStyles.display12
                        .copyWith(color: Colours.color5C5C6E),
                  );
                }),
              ),
            ],
          ).marginSymmetric(horizontal: 15.w, vertical: 15.w),
          Expanded(
            child: NotificationListener(
                onNotification: (ScrollNotification note) {
                  if (note.metrics.pixels == note.metrics.maxScrollExtent) {
                    logic.loadMore();
                  }
                  return true;
                },
                child: RefreshIndicator(
                  onRefresh: logic.onRefresh,
                  child: logic.init.value
                      ? Obx(() {
                          return logic.currentTypeIndex.value == 0
                              ? (logic.dataList.isEmpty
                                  ? _buildEmptyView(context)
                                  : _list(context))
                              : (logic.venueDataList.isEmpty
                                  ? _buildEmptyView(context)
                                  : _venueList(context));
                        })
                      : buildLoad(),
                )),
          )
        ],
      );
    });
  }

  Widget _refreshList(BuildContext context) {
    return NotificationListener(
      onNotification: (ScrollNotification note) {
        if (note.metrics.pixels == note.metrics.maxScrollExtent) {
          logic.loadMore();
        }
        return true;
      },
      child:
          RefreshIndicator(onRefresh: logic.onRefresh, child: _list(context)),
    );
  }

  Widget _list(BuildContext context) {
    return Obx(
      () => ListView.builder(
        // physics: const ClampingScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        // shrinkWrap: true,
        itemCount: logic.dataList.length + 1,
        itemBuilder: (context, index) {
          if (index == logic.dataList.length) {
            return SizedBox(); //_moreWidget();
          }
          final group = logic.dataList[index];
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 分组标题
              Row(children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () => AppPage.to(Routes.arenaDetailsPage,
                        arguments: {"id": int.parse(group.arenaId)}),
                    child: Text(
                      group.arenaName,
                      style: TextStyles.display12
                          .copyWith(color: Colours.colorA8A8BC),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                ),
                SizedBox(width: 10.w),
                Text(
                  group.date.replaceAll('.', '-'),
                  style: TextStyles.din.copyWith(color: Colours.color5C5C6E),
                ),
              ]),
              SizedBox(
                height: 15.w,
              ),
              // 分组内容
              GridView.builder(
                scrollDirection: Axis.vertical,
                // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                shrinkWrap: true,
                physics:
                    const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 15.w,
                  mainAxisSpacing: 15.w,
                  childAspectRatio: 165 / 124,
                ),
                padding: EdgeInsets.only(bottom: 20.w),
                itemCount: group.videos.length,
                itemBuilder: (context, itemIndex) {
                  final video = group.videos[itemIndex];
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () async {
                          if (!video.isFinish) {
                            return;
                          }
                          video.rxNew.value = false;
                          final result = await AppPage.to(
                              Routes.highlightsVideo,
                              arguments: {
                                'video': video,
                                'group': group,
                                'type': 3,
                              }).then((v) {
                            if (v != null) {
                              logic.onRefresh();
                            }
                          });
                          ;
                          if (result == true) {
                            // 如果结果为 true，表示需要刷新列表
                            group.videos.removeAt(itemIndex);
                            if (group.videos.isEmpty) {
                              logic.dataList.removeAt(index);
                            }
                            logic.dataList.refresh(); // 刷新数据
                          }
                        },
                        child: SizedBox(
                          height: 94.w,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8.w),
                            child: Stack(
                              children: [
                                Positioned(
                                    left: 0,
                                    right: 0,
                                    bottom: 0,
                                    top: 0,
                                    child: video.isFinish
                                        ? CachedNetworkImage(
                                            imageUrl: video.videoCover,
                                            fit: BoxFit.cover,
                                          )
                                        : (video.status == 0
                                            ? WxAssets.images.icHlPdz
                                                .image(fit: BoxFit.fill)
                                            : WxAssets.images.icHlHcz
                                                .image(fit: BoxFit.fill))),
                                Center(
                                    child: WxAssets.images.selfieShotPlay
                                        .image(width: 28.w, height: 28.w))
                                // Obx(
                                //   () => Visibility(
                                //     visible: video.rxNew.value && video.isFinish,
                                //     child: Positioned(
                                //         top: 5,
                                //         left: 5,
                                //         child: WxAssets.images.icHlNew
                                //             .image(width: 38.w, height: 18.w)),
                                //   ),
                                // ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 15.w,
                      ),
                      SizedBox(
                        height: 15.w,
                        child: Text(
                          textAlign: TextAlign.left,
                          video.name,
                          style: TextStyles.semiBold14,
                        ),
                      )
                    ],
                  );
                },
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _venueList(BuildContext context) {
    return Obx(() => ListView.builder(
          // physics: const ClampingScrollPhysics(),
          padding: EdgeInsets.symmetric(
            horizontal: 15.w,
          ),
          // shrinkWrap: true,
          itemCount: logic.venueDataList.length + 1,
          itemBuilder: (context, index) {
            if (index == logic.venueDataList.length) {
              return SizedBox(); //_moreWidget();
            }
            final group = logic.venueDataList[index];
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 分组标题
                Row(children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        if ((group.venueId ?? '0') != "0") {
                          AppPage.to(Routes.arenaDetailsPage, arguments: {
                            "id": int.parse(group.venueId ?? '0')
                          });
                        }
                      },
                      child: Text(
                        group.venueId == "0" ? "即刻创作" : group.venueName ?? '',
                        style: TextStyles.display12
                            .copyWith(color: Colours.colorA8A8BC),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  ),
                  SizedBox(width: 10.w),
                  Text(
                    group.mergeTime ?? '',
                    style: TextStyles.din.copyWith(color: Colours.color5C5C6E),
                  ),
                ]),
                SizedBox(
                  height: 15.w,
                ),
                // 分组内容
                GridView.builder(
                  scrollDirection: Axis.vertical,
                  // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                  shrinkWrap: true,
                  physics:
                      const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 15.w,
                    mainAxisSpacing: 15.w,
                    childAspectRatio: 165 / 124,
                  ),
                  padding: EdgeInsets.only(bottom: 20.w),
                  itemCount: (group.items ?? []).length,
                  itemBuilder: (context, itemIndex) {
                    final video = group.items![itemIndex];
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () async {
                            // if (!video.isFinish) {
                            //   return;
                            // }
                            // video.rxNew.value = false;
                            // final result = await AppPage.to(
                            //     Routes.highlightsVideo,
                            //     arguments: {'video': video, 'group': group});
                            // if (result == true) {
                            //   // 如果结果为 true，表示需要刷新列表
                            //   group.videos.removeAt(itemIndex);
                            //   if (group.videos.isEmpty) {
                            //     logic.dataList.removeAt(index);
                            //   }
                            //   logic.dataList.refresh(); // 刷新数据
                            // }
                            AppPage.to(Routes.highlightsVideo, arguments: {
                              'venueItem': video,
                              'venueGroup': group,
                              'isVenue': true,
                              'type': 2,
                            }).then((v) {
                              if (v != null) {
                                logic.onRefresh();
                              }
                            });
                          },
                          child: SizedBox(
                            height: 94.w,
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(8.w),
                              child: Stack(
                                children: [
                                  Positioned(
                                      left: 0,
                                      right: 0,
                                      bottom: 0,
                                      top: 0,
                                      child: CachedNetworkImage(
                                        imageUrl: video?.coverPath ?? '',
                                        fit: BoxFit.cover,
                                      )),
                                  Center(
                                      child: WxAssets.images.selfieShotPlay
                                          .image(width: 28.w, height: 28.w))
                                  // Obx(
                                  //   () => Visibility(
                                  //     visible: video.rxNew.value && video.isFinish,
                                  //     child: Positioned(
                                  //         top: 5,
                                  //         left: 5,
                                  //         child: WxAssets.images.icHlNew
                                  //             .image(width: 38.w, height: 18.w)),
                                  //   ),
                                  // ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 15.w,
                        ),
                        Text(
                          textAlign: TextAlign.left,
                          video?.title ?? '',
                          style: TextStyles.semiBold14,
                        ),
                      ],
                    );
                  },
                ),
              ],
            );
          },
        ));
  }

  Widget _moreWidget() {
    return Padding(
        padding: const EdgeInsets.only(bottom: 30, top: 5),
        child:
            MoreWidget(logic.dataList.length, logic.hasMore(), logic.pageSize));
  }

  /// 显示约战类型选择底部弹窗
  void _showBattleTypeBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.r),
              topRight: Radius.circular(20.r),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 顶部指示条
              Container(
                width: 38.w,
                height: 3.w,
                margin: EdgeInsets.only(top: 6.w, bottom: 10.w),
                decoration: BoxDecoration(
                  color: Colours.color1AD8D8D8,
                  borderRadius: BorderRadius.circular(2.5.r),
                ),
              ),

              // 全场约战选项
              _buildBattleOption(
                context,
                title: logic.highlightsTypeList[0]["title"] ?? '',
                onTap: () {
                  Navigator.pop(context);
                  if (logic.currentTypeIndex.value == 0) {
                    return;
                  }
                  logic.currentTypeIndex.value = 0;
                  logic.getdataList(true);
                },
              ),
              Container(
                height: 1,
                width: ScreenUtil().screenWidth - 30.w,
                color: Colours.color2F2F3B,
              ),
              // 半场约战选项
              _buildBattleOption(
                context,
                title: logic.highlightsTypeList[1]["title"] ?? '',
                onTap: () {
                  Navigator.pop(context);
                  if (logic.currentTypeIndex.value == 1) {
                    return;
                  }
                  logic.currentTypeIndex.value = 1;
                  logic.getVenueAchievements(true);
                },
              ),

              // 底部安全区域
              SizedBox(height: ScreenUtil().bottomBarHeight + 20.w),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBattleOption(
    BuildContext context, {
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(20.w),
        child: Center(
          child: Text(
            title,
            style: TextStyles.semiBold14,
          ),
        ),
      ),
    );
  }

  /// 构建空状态视图
  Widget _buildEmptyView(BuildContext context) {
    return Center(
      child: myNoDataView(
        context,
        msg: '暂无生涯视频，快去创作吧',
        imagewidget: WxAssets.images.battleEmptyIcon.image(),
      ),
    );
  }

  _getStatusStr(int status) {
    switch (status) {
      case 0:
        return '未开始';
      case 1:
        return '报名中';
      case 2:
        return '待开赛';
      case 3:
        return '进行中';
      case 4:
        return '已结束';
      default:
        return '未开始';
    }
  }

  _getStatusColor(int status) {
    switch (status) {
      case 0:
        return Colours.color6435E9;
      case 1:
        return Colours.color6435E9;
      case 2:
        return Colours.color6435E9;
      case 3:
        return Colours.colorFF661A;
      case 4:
        return Colours.color262626;
      default:
        return Colours.color262626;
    }
  }
}
