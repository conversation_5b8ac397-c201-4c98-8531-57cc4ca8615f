import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/tab2Venue/tab_venue_view.dart';
import 'package:shoot_z/pages/tab1Home/tab_home_view.dart';
import 'package:shoot_z/pages/tab3VideoEdit/video_edit_home_page.dart';
import '../../gen/assets.gen.dart';
import '../../generated/l10n.dart';
import '../tab5Mine/mine_view.dart';

class TabState {
  StreamSubscription? allSubscription;
  StreamSubscription? loginSubscription;
  StreamSubscription? connectivityStreamSubscription;
  bool connectivity = false;
  late List<Widget> pages;
  late List<BottomNavigationBarItem> tabbar;

  var currentIndex = 0.obs;
  PageController pageController = PageController();

  TabState() {
    tabbar = [
      BottomNavigationBarItem(
        icon: Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: WxAssets.images.icHomeNo.image()),
        activeIcon: Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: WxAssets.images.icHomeSel.image()),
        label: S.current.home,
      ),
      BottomNavigationBarItem(
        icon: Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: WxAssets.images.icBsNo.image()),
        activeIcon: Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: WxAssets.images.icBsSel.image()),
        label: S.current.ball_park,
      ),
      const BottomNavigationBarItem(
        icon: SizedBox.shrink(), // 占位符，不显示文案和图标
        label: '',
      ),
      BottomNavigationBarItem(
        icon: Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: WxAssets.images.tabImg3No.image()),
        activeIcon: Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: WxAssets.images.tabImg3.image()),
        label: S.current.video_editing,
      ),
      BottomNavigationBarItem(
        icon: Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: WxAssets.images.icMineNo.image()),
        activeIcon: Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: WxAssets.images.icMineSel.image()),
        label: S.current.mine,
      ),
    ];
    pages = [
      TabHomePage(),
      const TabVenuePage(),
      //    const GamePage(),
      const SizedBox(),
      // PointsMallPage(
      //   key: const Key("111"),
      //   type: 0,
      // ),
      VideoEditHomePage(),
      const MinePage()
    ];
  }
}
