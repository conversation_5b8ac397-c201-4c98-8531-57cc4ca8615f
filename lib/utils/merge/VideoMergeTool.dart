import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:easy_video_editor/easy_video_editor.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:shoot_z/network/model/%20merge_video_model.dart';
import 'package:shoot_z/network/model/shot_record_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/utils/merge/SerialTaskQueue.dart';
import 'package:permission_handler/permission_handler.dart';

class VideoMergeTool {
  final void Function(double progress, int total, int index, String message,
      {bool isError}) onProgress;
  final void Function(String mergeUrl, String coverUrl) onCompleted;

  VideoMergeTool({required this.onProgress, required this.onCompleted});

  final List<MergeVideoModel> _finalVideos = [];
  var _progress = 0.0.obs;
  bool _isProcessing = false;
  var dataList = <ShotRecordModel>[].obs;
  var imgList2 = <String>[].obs;
  var index2 = 0.obs;
  var index3 = 0.obs;
  void init() {
    SerialTaskQueue.init();
  }

  // 基础文件存在检查
  static Future<bool> fileExists(String filePath) async {
    try {
      final file = File(filePath);
      return await file.exists();
    } catch (e) {
      print('检查文件存在时出错: $e');
      return false;
    }
  }

  /// 入队（用户点击开始）
  Future<void> startAll(List<ShotRecordModel> list) async {
    if (_isProcessing) {
      Get.snackbar('警告', '已有任务进行中');
      return;
    }

    SerialTaskQueue.reset();
    await Future.delayed(const Duration(milliseconds: 2000));
    for (var item in list) {
      index3++;
      if ((item.filePath != "")) {
        final item1 = item.copyWith(
            videoLoadOK: "1",
            imgLoadOK: "$index3-1",
            filePath:
                UserManager.instance.changeFilePathInIOS(item.filePath ?? ""));
        final item2 = item.copyWith(
            videoLoadOK: "2",
            imgLoadOK: "$index3-2",
            filePath:
                UserManager.instance.changeFilePathInIOS(item.filePath ?? ""));
        final item3 = item.copyWith(
            videoLoadOK: "3",
            imgLoadOK: "$index3-3",
            filePath:
                UserManager.instance.changeFilePathInIOS(item.filePath ?? ""));
        // 严格按顺序插入
        dataList.addAll([item1, item2, item3]);
      }
    }
    try {
      _isProcessing = true;
      _progress.value = 0;
      onProgress(0.0, _finalVideos.length, dataList.length, '', isError: false);
      // 分阶段进度计算
      final futures = dataList.map((item) async {
        try {
          final result = SerialTaskQueue.push(() => processOneVideo(
                item,
                onTotalProgress: (p) {
                  _progress.value =
                      (((_finalVideos.length) + p) / (dataList.length) * 0.9);
                  //     log('VideoMergePagemergedPath161=completedVideos${_finalVideos.length}-p=$p-totalVideos=$totalVideos total:${_progress.value}');
                  onProgress(
                      _progress.value, _finalVideos.length, dataList.length, '',
                      isError: false);
                },
              ));

          log('完成视频处理:_finalVideos:${_finalVideos.length}-- ${item.filePath}');
          return result;
        } catch (e) {
          log('视频处理失败: ${item.filePath} - $e');
          return null; // 返回null而不是rethrow
        }
      }).toList();
      await Future.wait(futures);
// // 等待所有任务完成（包括失败的）try {
//       final results = await Future.wait(futures.map((f) => f.catchError((e) {
//             log('任务失败: $e');
//             return null; // 返回占位符避免终止
//           })));

//       // 检查有效结果
//       final validResults = results.whereType<String>().toList();
//       // if (validResults.isEmpty) throw Exception('所有任务失败');
//       log('VideoMergePagemergedPath16');
//       _outputPath = await mergeAllVideos();
//       log('VideoMergePagemergedPath17 _outputPath=$_outputPath');
    } on PlatformException catch (e) {
      Get.snackbar('系统错误', e.message ?? '视频处理服务异常');
      log('VideoMergePagemergedPath181 ');
    } catch (e) {
      Get.snackbar('处理失败', e.toString());
      log('VideoMergePagemergedPath182');
    } finally {
      _isProcessing = false;
      log('VideoMergePagemergedPath183 ');
    }
  }

  /// 入队（用户点击开始）开始无特效任务
  Future<void> startAllNoSpecial(List<ShotRecordModel> list) async {
    if (_isProcessing) {
      Get.snackbar('警告', '已有任务进行中');
      return null;
    }

    SerialTaskQueue.reset();
    cancelAll();
    for (var item in list) {
      index3++;
      if ((item.filePath != "")) {
        final item1 = item.copyWith(
            videoLoadOK: "1",
            imgLoadOK: "$index3-1",
            filePath:
                UserManager.instance.changeFilePathInIOS(item.filePath ?? ""));
        // 严格按顺序插入
        dataList.addAll([item1]);
      }
    }
    try {
      _isProcessing = true;
      _progress.value = 0;
      onProgress(_progress.value, _finalVideos.length, dataList.length, '',
          isError: false);
      List<String> imgl = dataList.map((element) {
        return element.filePath ?? "";
      }).toList();

      final outputPath = await _generateFinalOutputPath();
      for (var item in imgl) {
        var isHava = await fileExists(item);
        log("getCanDownLoadVideoUrl10=${isHava}");
      }

      log("getCanDownLoadVideoUrl11=${imgl.length}-${jsonEncode(imgl)}");
      // final editor = VideoEditorBuilder(videoPath: imgl.first)
      //   ..merge(otherVideoPaths: imgl.sublist(1))
      //   ..compress(resolution: VideoResolution.p720)
      //   ..removeAudio(); // 静音
      final editor = VideoEditorBuilder(videoPath: imgl.first)
        ..trim(startTimeMs: 0, endTimeMs: 5000) // Trim first 5 seconds
        ..speed(speed: 1.5) // Speed up by 1.5x
        ..removeAudio(); // 静音
      log("VideoMergePagemergedPath133z1=${jsonEncode(dataList)}");
      try {
        await editor.export(
          outputPath: outputPath,
          onProgress: (p) {
            _progress.value = p;
            onProgress(p, dataList.length, dataList.length, '', isError: false);
            log("getCanDownLoadVideoUrl10=${p}");
          },
        );
        final metadata = await editor.getVideoMetadata();
        log('getCanDownLoadVideoUrl Duration: ${metadata.duration} ms');
        log('getCanDownLoadVideoUrl Dimensions: ${metadata.width}x${metadata.height}');
        log('getCanDownLoadVideoUrl Orientation: ${metadata.rotation}°');
        log('getCanDownLoadVideoUrl File size: ${metadata.fileSize} bytes');
        log('getCanDownLoadVideoUrl Creation date: ${metadata.date}');
        var isHava = await fileExists(outputPath);
        log("getCanDownLoadVideoUrl12=${isHava}");
        log("VideoMergePagemergedPath133z2=${outputPath}");
      } catch (e) {
        log("VideoMergePagemergedPath133z3=e=${e}");
        await _cleanCorruptedFile(outputPath);
        rethrow;
      }
      log("VideoMergePagemergedPath133393=${outputPath}");
      // return;
      // 分阶段进度计算
      if (imgl.isEmpty) return onCompleted("", "");
      await Future.delayed(const Duration(milliseconds: 300));
      final editor2 = VideoEditorBuilder(videoPath: outputPath);
      final result =
          await editor2.generateThumbnail(positionMs: 1000, quality: 85);
      onCompleted(outputPath, result ?? "");
      //  return outputPath;
    } on PlatformException catch (e) {
      //  Get.snackbar('系统错误', e.message ?? '视频处理服务异常');
      log('VideoMergePagemergedPath181$e ');
      return onCompleted("", "");
    } catch (e) {
      //   Get.snackbar('处理失败', e.toString());
      log('VideoMergePagemergedPath182$e');
      return onCompleted("", "");
    } finally {
      _isProcessing = false;
      log('VideoMergePagemergedPath183 ');
    }
  }

  /// 处理单个视频：三段慢放 → 合并
  /// [onTotalProgress] 0~100 实时进度
  Future<String> processOneVideo(
    ShotRecordModel item, {
    required Function(double) onTotalProgress,
  }) async {
    index2++;
    log("VideoMergePagemergedPath1121");
    if (item.filePath == null) throw Exception('视频路径为空');
    log("VideoMergePagemergedPath1122");
    final tempDir = (await getTemporaryDirectory()).path;
    log("VideoMergePagemergedPath1123");
    final results = <String>[];
    // 1. 取元数据
    final editor = VideoEditorBuilder(videoPath: item.filePath!);
    log("VideoMergePagemergedPath1124");
    final meta = await editor.getVideoMetadata();
    log("VideoMergePagemergedPath1125");
    final duration = meta.duration;
// Cancel the operation
    log("VideoMergePagemergedPath11-1  index2=$index2 item=${item.videoLoadOK} duration=$duration videoLoadOK=${item.videoLoadOK}");
    if (duration > 8000) {
      if (item.videoLoadOK == "1") {
        final outFile =
            generateNewFilePath(tempDir, item.filePath!, suffix: 'seg_1');
        await editor
            .trim(startTimeMs: 0, endTimeMs: duration - 4000)
            .compress(resolution: VideoResolution.p720) // 降分辨率提速
            .removeAudio() // 静音
            .export(
              outputPath: outFile,
              onProgress: (progress) {
                onTotalProgress(progress);
              },
            );

        if (outFile.isNotEmpty) results.add(outFile);
        log("VideoMergePagemergedPath11-2 i=1-${results.length}");
      } else if (item.videoLoadOK == "2") {
        final outFile =
            generateNewFilePath(tempDir, item.filePath!, suffix: 'seg_2');
        await editor
            .trim(startTimeMs: duration - 4000, endTimeMs: duration - 2000)
            .compress(resolution: VideoResolution.p720) // 降分辨率提速
            .speed(speed: 0.5)
            .removeAudio() // 静音
            .export(
              outputPath: outFile,
              onProgress: (progress) {
                onTotalProgress(progress);
              },
            );

        if (outFile.isNotEmpty) results.add(outFile);
        log("VideoMergePagemergedPath11-3 i=2-${results.length}");
      } else if (item.videoLoadOK == "3") {
        final outFile =
            generateNewFilePath(tempDir, item.filePath!, suffix: 'seg_3');
        await editor
            .trim(startTimeMs: duration - 2000, endTimeMs: duration)
            .compress(resolution: VideoResolution.p720) // 降分辨率提速
            .removeAudio() // 静音
            .export(
              outputPath: outFile,
              onProgress: (progress) {
                onTotalProgress(progress);
              },
            );

        if (outFile.isNotEmpty) results.add(outFile);
        log("VideoMergePagemergedPath11-4 i=3-${results.length}");
      }
    } else {
      onTotalProgress(100);
      if (item.videoLoadOK == "3") {
        results.add(item.filePath!);
      } else {
        MergeVideoModel mergeVideoModel = MergeVideoModel();
        mergeVideoModel.filePath = "";
        mergeVideoModel.mergePath = "";
        mergeVideoModel.index = item.videoLoadOK;
        _finalVideos.add(mergeVideoModel);
      }
    }

    log("VideoMergePagemergedPath11-5");

    for (final path in results) {
      if (!await File(path).exists()) {
        //  throw Exception('视频文件不存在: $path');
        log("VideoMergePagemergedPath124=${path}");
        MergeVideoModel mergeVideoModel = MergeVideoModel();
        mergeVideoModel.filePath = "";
        mergeVideoModel.mergePath = "";
        mergeVideoModel.index = item.videoLoadOK;
        _finalVideos.add(mergeVideoModel);
      } else {
        log("VideoMergePagemergedPath125=${path}");
        MergeVideoModel mergeVideoModel = MergeVideoModel();
        mergeVideoModel.filePath = item.filePath;
        mergeVideoModel.mergePath = path;
        mergeVideoModel.index = item.videoLoadOK;
        mergeVideoModel.eventId = item.eventId;

        _finalVideos.add(mergeVideoModel);
      }
    }
//VideoMergePagemergedPath125=/data/user/0/com.shootZ.app.shoot_z/final_merge_1757422012563_seg_2281.mp4

    log('VideoMergePagemergedPath11-6 _outputPath=${_finalVideos.length}-${dataList.length}');

    if (dataList.length == _finalVideos.length) {
      log('VideoMergePagemergedPath1333 _finalVideos=${jsonEncode(_finalVideos)}');
      try {
        var outputPath = await mergeAllVideos();
        await Future.delayed(Duration(milliseconds: 300));
        final editor2 = VideoEditorBuilder(videoPath: outputPath ?? "");
        final result =
            await editor2.generateThumbnail(positionMs: 1000, quality: 85);
        onCompleted(outputPath ?? "", result ?? "");
        log('VideoMergePagemergedPath13330 _outputPath=$outputPath');
      } catch (e) {
        print(e);
        log('VideoMergePagemergedPath13331 _outputPath=$e');
      }
    }
    return results.join(",");
  }

  Future<String?> mergeAllVideos() async {
    if (_finalVideos.isEmpty) return null;
    if (_finalVideos.length == 1) return _finalVideos.first.filePath;

    try {
      final outputPath = await _generateFinalOutputPath();

      await _executeFinalMerge(outputPath);
      return outputPath;
    } catch (e) {
      Get.snackbar('合并失败', e.toString());
      return null;
    }
  }

  Future<void> _cleanCorruptedFile(String path) async {
    try {
      if (await File(path).exists()) {
        await File(path).delete();
      }
    } catch (e) {
      debugPrint('清理失败: $path - $e');
    }
  }

  Future<String> _generateFinalOutputPath() async {
    Directory dir2;
    if (Platform.isAndroid) {
      // Android专属目录（无需权限）
      final dir = await getExternalStorageDirectory();
      dir2 = Directory(path.join(dir!.path, 'Movies', 'merge_videos'));
    } else {
      // iOS处理
      // var dir = await getApplicationDocumentsDirectory();
      // dir2 = Directory(path.join(dir.path, 'merge_videos'));
      dir2 = await getTemporaryDirectory(); // 或 getLibraryDirectory()
      dir2 = Directory(path.join(dir2.path, 'merge_videos'));
    }
    if (!await dir2.exists()) await dir2.create(recursive: true);
    return path.join(
        dir2.path, 'final_merge_${DateTime.now().millisecondsSinceEpoch}.mp4');
  }

  bool containsEventId(List<MergeVideoModel> videos, String eventId) {
    return videos.any((video) => video.eventId == eventId);
  }

  Future<String?> _executeFinalMerge(String outputPath) async {
    List<String> pathlist = [];
    List<MergeVideoModel> finalVideosList1 = []; //合成的列表
    List<MergeVideoModel> finalVideosList2 = []; //未合成的列表
    // 检查所有分段是否存在
    for (final path in _finalVideos) {
      if (!await File(path.mergePath ?? "").exists()) {
        //  print('文件不存在，需要重新生成');
        if (!await File(path.filePath ?? "").exists()) {
          //  print('文件不存在，需要重新生成');
        } else {
          if (!containsEventId(finalVideosList1, path.eventId ?? "")) {
            pathlist.add(path.filePath ?? "");
            finalVideosList2.add(path);
          }
        }
      } else {
        if (!containsEventId(finalVideosList2, path.eventId ?? "")) {
          pathlist.add(path.mergePath ?? "");
          finalVideosList1.add(path);
        }
      }
    }
    log("VideoMergePagemergedPath13339=${pathlist.length}");
    final editor = VideoEditorBuilder(videoPath: pathlist.first)
      ..merge(otherVideoPaths: pathlist.sublist(1))
      ..compress(resolution: VideoResolution.p720)
      ..removeAudio(); // 静音

    log("VideoMergePagemergedPath13339=${jsonEncode(pathlist)}");
    try {
      return await editor.export(
        outputPath: outputPath,
        onProgress: (p) {
          _progress.value =
              (((_finalVideos.length) + p) / (dataList.length) * 0.9) + p * 0.1;
          onProgress(_progress.value, _finalVideos.length, dataList.length, '',
              isError: false);
        },
      );
    } catch (e) {
      await _cleanCorruptedFile(outputPath);
      rethrow;
    }
  }

  String generateNewFilePath(String temporaryFilePath, String originalPath,
      {String? suffix}) {
    // 获取目录和文件名信息
    final dir = path.dirname(temporaryFilePath);
    final filename = path.basenameWithoutExtension(originalPath);
    final ext = path.extension(originalPath);
    // 生成新文件名（添加时间戳或自定义后缀）
    final newFilename =
        '${filename}_${suffix ?? ""}${DateTime.now().millisecondsSinceEpoch.toString().substring(10)}$ext';

    // 组合新路径
    return path.join(dir, newFilename);
  }

  Future<void> cancelAll() async {
    _finalVideos.clear();
    _progress.value = 0.0;
    _isProcessing = false;
    dataList.clear();
    imgList2.clear();
    index2.value = 0;
    index3.value = 0;
    try {
// Start an operation
      await VideoEditorBuilder.cancel();
// Cancel the operation
      SerialTaskQueue.cancelAll();
      _isProcessing = false;
      //  Get.snackbar('已取消', '操作已终止');
    } catch (e) {
      Get.snackbar('错误', '取消失败: $e');
    }
  }

  void dispose() {
    SerialTaskQueue.dispose();
  }
}
