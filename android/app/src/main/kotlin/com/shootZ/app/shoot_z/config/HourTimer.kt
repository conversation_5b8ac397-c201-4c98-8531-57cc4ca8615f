package com.shootZ.app.shoot_z.config

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

class HourTimer {
    private var totalSeconds = 0
    private var timerJob: Job? = null
    private var isPaused = false
    private var pausedSeconds = 0

    fun start(update: (String) -> Unit) {
        timerJob?.cancel()
        timerJob = CoroutineScope(Dispatchers.Main).launch {
            while (isActive) {
                if (!isPaused) {
                    totalSeconds++
                    update(formatTime())
                }
                delay(1000)
            }
        }
    }

    fun pause() {
        isPaused = true
        pausedSeconds = totalSeconds
    }

    fun resume() {
        isPaused = false
        totalSeconds = pausedSeconds
    }

    fun reset() {
        totalSeconds = 0
        isPaused = false
    }

    private fun formatTime(): String {
        val hours = totalSeconds / 3600
        val minutes = (totalSeconds % 3600) / 60
        val seconds = totalSeconds % 60
        if(totalSeconds>3600){
            return "%02d:%02d:%02d".format(hours, minutes, seconds)

        }else{
            return "%02d:%02d".format( minutes, seconds)

        }
    }
}