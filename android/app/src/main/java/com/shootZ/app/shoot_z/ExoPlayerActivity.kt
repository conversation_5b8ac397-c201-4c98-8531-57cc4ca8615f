package com.shootZ.app.shoot_z

import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.ui.PlayerView

class ExoPlayerActivity : AppCompatActivity() {

    private var exoPlayer: ExoPlayer? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        // 强制横屏（优先级高于Manifest配置）
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_exo_player)

        val videoUrl = intent.getStringExtra("VIDEO_URL") ?: ""
        val playerView: PlayerView = findViewById(R.id.playerView)
        findViewById<View>(R.id.btnBack).setOnClickListener { finish() }
        initializePlayer(playerView, videoUrl)
    }

    private fun initializePlayer(playerView: PlayerView, url: String) {
        exoPlayer = ExoPlayer.Builder(this).build().apply {
            val mediaItem = MediaItem.fromUri(url)
            setMediaItem(mediaItem)
            prepare()
            playWhenReady = true // 自动播放
        }
        playerView.player = exoPlayer
    }

    override fun onPause() {
        super.onPause()
        exoPlayer?.pause() // 暂停播放（节省电量）
    }

    override fun onDestroy() {
        exoPlayer?.release() // 释放资源
        exoPlayer = null
        super.onDestroy()
    }
}