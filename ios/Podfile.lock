PODS:
  - AMap3DMap (10.1.200):
    - AMapFoundation (>= 1.8.0)
  - amap_flutter_location (0.0.1):
    - AMapLocation
    - Flutter
  - amap_flutter_map (0.0.1):
    - AMap3DMap
    - Flutter
  - AMapFoundation (1.8.2)
  - AMapLocation (2.10.0):
    - AMapFoundation (>= 1.8.0)
  - app_links (0.0.2):
    - Flutter
  - better_player (0.0.1):
    - Cache (~> 6.0.0)
    - Flutter
    - GCDWebServer
    - HLSCachingReverseProxyServer
    - PINCache
  - Bugly (2.6.1)
  - Cache (6.0.0)
  - connectivity_plus (0.0.1):
    - Flutter
    - FlutterMacOS
  - device_info_plus (0.0.1):
    - Flutter
  - easy_video_editor (0.0.5):
    - Flutter
  - Flutter (1.0.0)
  - flutter_bugly (0.0.1):
    - Bugly (= 2.6.1)
    - Flutter
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_native_splash (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - fluwx (0.0.1):
    - Flutter
    - fluwx/no_pay (= 0.0.1)
  - fluwx/no_pay (0.0.1):
    - Flutter
    - OpenWeChatSDKNoPay (~> 2.0.4)
  - GCDWebServer (3.5.4):
    - GCDWebServer/Core (= 3.5.4)
  - GCDWebServer/Core (3.5.4)
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - GRDB.swift (6.24.1):
    - GRDB.swift/standard (= 6.24.1)
  - GRDB.swift/standard (6.24.1)
  - HLSCachingReverseProxyServer (0.1.0):
    - GCDWebServer (~> 3.5)
    - PINCache (>= 3.0.1-beta.3)
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.7.4)
  - image_gallery_saver_plus (0.0.1):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - in_app_purchase_storekit (0.0.1):
    - Flutter
    - FlutterMacOS
  - map_launcher (0.0.1):
    - Flutter
  - MediaPipeTasksCommon (0.10.14)
  - MediaPipeTasksVision (0.10.14):
    - MediaPipeTasksCommon (= 0.10.14)
  - open_file_ios (0.0.1):
    - Flutter
  - OpenWeChatSDKNoPay (2.0.4)
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PINCache (3.0.4):
    - PINCache/Arc-exception-safe (= 3.0.4)
    - PINCache/Core (= 3.0.4)
  - PINCache/Arc-exception-safe (3.0.4):
    - PINCache/Core
  - PINCache/Core (3.0.4):
    - PINOperation (~> 1.2.3)
  - PINOperation (1.2.3)
  - QCloudCore (6.4.7):
    - QCloudCore/Default (= 6.4.7)
  - QCloudCore/Default (6.4.7):
    - QCloudTrack/Beacon (= 6.4.7)
  - QCloudCOSXML (6.4.7):
    - QCloudCOSXML/Default (= 6.4.7)
  - QCloudCOSXML/Default (6.4.7):
    - QCloudCore (= 6.4.7)
  - QCloudTrack/Beacon (6.4.7)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - SnapKit (5.7.1)
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - tencentcloud_cos_sdk_plugin (1.2.3):
    - Flutter
    - QCloudCOSXML (= 6.4.7)
  - TOCropViewController (2.7.4)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - amap_flutter_location (from `.symlinks/plugins/amap_flutter_location/ios`)
  - amap_flutter_map (from `.symlinks/plugins/amap_flutter_map/ios`)
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - better_player (from `.symlinks/plugins/better_player/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/darwin`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - easy_video_editor (from `.symlinks/plugins/easy_video_editor/ios`)
  - Flutter (from `Flutter`)
  - flutter_bugly (from `.symlinks/plugins/flutter_bugly/ios`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - fluwx (from `.symlinks/plugins/fluwx/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - GRDB.swift
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_gallery_saver_plus (from `.symlinks/plugins/image_gallery_saver_plus/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - in_app_purchase_storekit (from `.symlinks/plugins/in_app_purchase_storekit/darwin`)
  - map_launcher (from `.symlinks/plugins/map_launcher/ios`)
  - MediaPipeTasksVision (= 0.10.14)
  - open_file_ios (from `.symlinks/plugins/open_file_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - SnapKit (~> 5.7.0)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - tencentcloud_cos_sdk_plugin (from `.symlinks/plugins/tencentcloud_cos_sdk_plugin/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - AMap3DMap
    - AMapFoundation
    - AMapLocation
    - Bugly
    - Cache
    - GCDWebServer
    - GRDB.swift
    - HLSCachingReverseProxyServer
    - MediaPipeTasksCommon
    - MediaPipeTasksVision
    - OpenWeChatSDKNoPay
    - OrderedSet
    - PINCache
    - PINOperation
    - QCloudCore
    - QCloudCOSXML
    - QCloudTrack
    - SnapKit
    - TOCropViewController

EXTERNAL SOURCES:
  amap_flutter_location:
    :path: ".symlinks/plugins/amap_flutter_location/ios"
  amap_flutter_map:
    :path: ".symlinks/plugins/amap_flutter_map/ios"
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  better_player:
    :path: ".symlinks/plugins/better_player/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/darwin"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  easy_video_editor:
    :path: ".symlinks/plugins/easy_video_editor/ios"
  Flutter:
    :path: Flutter
  flutter_bugly:
    :path: ".symlinks/plugins/flutter_bugly/ios"
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  fluwx:
    :path: ".symlinks/plugins/fluwx/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_gallery_saver_plus:
    :path: ".symlinks/plugins/image_gallery_saver_plus/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  in_app_purchase_storekit:
    :path: ".symlinks/plugins/in_app_purchase_storekit/darwin"
  map_launcher:
    :path: ".symlinks/plugins/map_launcher/ios"
  open_file_ios:
    :path: ".symlinks/plugins/open_file_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  tencentcloud_cos_sdk_plugin:
    :path: ".symlinks/plugins/tencentcloud_cos_sdk_plugin/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  AMap3DMap: 06a11a83072857d6076c14060b2e1a676182e84d
  amap_flutter_location: f033c983c2d4319203ff7b523775579534d0d557
  amap_flutter_map: 6cde084007a3ed06235383d46bc73c6c9a80c1e6
  AMapFoundation: 9885c48fc3a78fdfb84a0299a2293e56ea3c9fec
  AMapLocation: 5248aec2455ebb5d104b367813c946430a2ee033
  app_links: 76b66b60cc809390ca1ad69bfd66b998d2387ac7
  better_player: 472a1f3471b8991bde82327c91498b0f7934245d
  Bugly: 217ac2ce5f0f2626d43dbaa4f70764c953a26a31
  Cache: 4ca7e00363fca5455f26534e5607634c820ffc2d
  connectivity_plus: 2256d3e20624a7749ed21653aafe291a46446fee
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  easy_video_editor: 3b4b38c40be7344935f79698115ee36077df8a57
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_bugly: a77131ff7b385a481c594830dc89aa53e8e7fbee
  flutter_inappwebview_ios: b89ba3482b96fb25e00c967aae065701b66e9b99
  flutter_native_splash: 35ddbc7228eafcb3969dcc5f1fbbe27c1145a4f0
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  fluwx: 7330691eb49010b2b392434361a2526de339a396
  GCDWebServer: 2c156a56c8226e2d5c0c3f208a3621ccffbe3ce4
  geolocator_apple: ab36aa0e8b7d7a2d7639b3b4e48308394e8cef5e
  GRDB.swift: 136dcb5d8dddca50aae3ba7d77475f79e7232cd8
  HLSCachingReverseProxyServer: 59935e1e0244ad7f3375d75b5ef46e8eb26ab181
  image_cropper: c4326ea50132b1e1564499e5d32a84f01fb03537
  image_gallery_saver_plus: e597bf65a7846979417a3eae0763b71b6dfec6c3
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  in_app_purchase_storekit: d1a48cb0f8b29dbf5f85f782f5dd79b21b90a5e6
  map_launcher: fe43bda6720bb73c12fcc1bdd86123ff49a4d4d6
  MediaPipeTasksCommon: 5660099c2dd81f7ac4a7a5f51055785ead8e0e64
  MediaPipeTasksVision: 0fac0db83c0b45e4d7811a9227be5e571403cf83
  open_file_ios: 5ff7526df64e4394b4fe207636b67a95e83078bb
  OpenWeChatSDKNoPay: 84a2f83b3adbae0f1b55651141f477d20aa90a75
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PINCache: d9a87a0ff397acffe9e2f0db972ac14680441158
  PINOperation: fb563bcc9c32c26d6c78aaff967d405aa2ee74a7
  QCloudCore: 49e7571984a8e5fd8f0fd58c2f3b806255796223
  QCloudCOSXML: 7205e76aa9cf613468222615483c9d7c074e4298
  QCloudTrack: 3b53a7fc4fe3920e407f2aa73f2452992a61f7f3
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  SnapKit: d612e99e678a2d3b95bf60b0705ed0a35c03484a
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  tencentcloud_cos_sdk_plugin: c3d78b2c3bae42b5a20009dc047f3f1621e6272b
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  wakelock_plus: e29112ab3ef0b318e58cfa5c32326458be66b556
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2

PODFILE CHECKSUM: f198e24c3aa9a2ee43a39a5fb996108fac37ca5d

COCOAPODS: 1.16.2
